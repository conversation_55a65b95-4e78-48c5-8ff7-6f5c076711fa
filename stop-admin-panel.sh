#!/bin/bash

# 管理员面板停止脚本
# 用于停止后端和前端服务

echo "🛑 停止管理员面板系统..."

# 读取PID文件
if [ -f "backend.pid" ]; then
    BACKEND_PID=$(cat backend.pid)
    echo "🔌 停止后端服务 (PID: $BACKEND_PID)..."
    kill $BACKEND_PID 2>/dev/null
    rm backend.pid
    echo "✅ 后端服务已停止"
else
    echo "⚠️ 未找到后端PID文件"
fi

if [ -f "frontend.pid" ]; then
    FRONTEND_PID=$(cat frontend.pid)
    echo "🔌 停止前端服务 (PID: $FRONTEND_PID)..."
    kill $FRONTEND_PID 2>/dev/null
    rm frontend.pid
    echo "✅ 前端服务已停止"
else
    echo "⚠️ 未找到前端PID文件"
fi

# 强制停止可能残留的进程
echo "🧹 清理残留进程..."
pkill -f "npm run dev" 2>/dev/null
pkill -f "node.*3001" 2>/dev/null
pkill -f "vite" 2>/dev/null

echo "✅ 管理员面板系统已完全停止"

# 清理日志文件（可选）
read -p "是否删除日志文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -f backend.log frontend.log
    echo "🗑️ 日志文件已删除"
fi

echo "👋 再见！"
