// Frontend integration test script
const BASE_URL = 'http://localhost:3002';

async function testFrontendPages() {
    console.log('🧪 开始前端页面测试...\n');
    
    const pages = [
        { path: '/', name: '首页' },
        { path: '/admin', name: '管理员面板' },
        { path: '/admin/dashboard', name: '管理员仪表板' },
        { path: '/admin/users', name: '用户管理' },
        { path: '/admin/tutors', name: '导师管理' },
        { path: '/admin/appointments', name: '预约管理' },
        { path: '/admin/payments', name: '支付管理' },
        { path: '/admin/verification', name: '认证审核' }
    ];
    
    let passed = 0;
    let total = pages.length;
    
    for (const page of pages) {
        try {
            const response = await fetch(`${BASE_URL}${page.path}`);
            
            if (response.ok) {
                const content = await response.text();
                
                // Check if it's a valid HTML page
                if (content.includes('<!DOCTYPE html>') || content.includes('<html')) {
                    console.log(`✅ ${page.name} (${page.path}): 页面加载成功`);
                    passed++;
                } else {
                    console.log(`⚠️ ${page.name} (${page.path}): 返回内容不是HTML`);
                }
            } else {
                console.log(`❌ ${page.name} (${page.path}): HTTP ${response.status}`);
            }
        } catch (error) {
            console.log(`❌ ${page.name} (${page.path}): ${error.message}`);
        }
    }
    
    console.log(`\n📊 前端测试结果: ${passed}/${total} 通过 (${((passed/total)*100).toFixed(1)}%)`);
    
    if (passed === total) {
        console.log('🎉 所有前端页面测试通过！');
    } else {
        console.log('⚠️ 部分前端页面测试失败');
    }
    
    return passed === total;
}

async function testAPIIntegration() {
    console.log('\n🔗 开始API集成测试...\n');
    
    const adminToken = Buffer.from('<EMAIL>').toString('base64');
    
    const apiTests = [
        { endpoint: '/api/admin/dashboard/stats', name: '仪表板数据加载' },
        { endpoint: '/api/admin/dashboard/user-growth', name: '用户增长图表' },
        { endpoint: '/api/admin/dashboard/appointment-trends', name: '预约趋势图表' },
        { endpoint: '/api/admin/tutors/low-rating', name: '低分导师列表' },
        { endpoint: '/api/admin/analytics/revenue', name: '收入分析数据' },
        { endpoint: '/api/admin/system/health', name: '系统健康状态' }
    ];
    
    let passed = 0;
    let total = apiTests.length;
    
    for (const test of apiTests) {
        try {
            const response = await fetch(`http://localhost:3001${test.endpoint}`, {
                headers: {
                    'Authorization': `Bearer ${adminToken}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                console.log(`✅ ${test.name}: API响应正常`);
                passed++;
            } else {
                console.log(`❌ ${test.name}: HTTP ${response.status}`);
            }
        } catch (error) {
            console.log(`❌ ${test.name}: ${error.message}`);
        }
    }
    
    console.log(`\n📊 API集成测试结果: ${passed}/${total} 通过 (${((passed/total)*100).toFixed(1)}%)`);
    
    return passed === total;
}

async function testSystemIntegration() {
    console.log('\n🔧 开始系统集成测试...\n');
    
    const tests = [
        {
            name: '后端服务可用性',
            test: async () => {
                const response = await fetch('http://localhost:3001/api/admin/system/health', {
                    headers: {
                        'Authorization': `Bearer ${Buffer.from('<EMAIL>').toString('base64')}`,
                        'Content-Type': 'application/json'
                    }
                });
                return response.ok;
            }
        },
        {
            name: '前端服务可用性',
            test: async () => {
                const response = await fetch('http://localhost:3002/');
                return response.ok;
            }
        },
        {
            name: '跨域请求支持',
            test: async () => {
                try {
                    const response = await fetch('http://localhost:3001/api/admin/dashboard/stats', {
                        headers: {
                            'Authorization': `Bearer ${Buffer.from('<EMAIL>').toString('base64')}`,
                            'Content-Type': 'application/json',
                            'Origin': 'http://localhost:3002'
                        }
                    });
                    return response.ok;
                } catch (error) {
                    return false;
                }
            }
        },
        {
            name: '管理员认证机制',
            test: async () => {
                // Test without auth
                const noAuthResponse = await fetch('http://localhost:3001/api/admin/dashboard/stats');
                if (noAuthResponse.status !== 401) return false;
                
                // Test with auth
                const authResponse = await fetch('http://localhost:3001/api/admin/dashboard/stats', {
                    headers: {
                        'Authorization': `Bearer ${Buffer.from('<EMAIL>').toString('base64')}`,
                        'Content-Type': 'application/json'
                    }
                });
                return authResponse.ok;
            }
        }
    ];
    
    let passed = 0;
    let total = tests.length;
    
    for (const test of tests) {
        try {
            const result = await test.test();
            if (result) {
                console.log(`✅ ${test.name}: 通过`);
                passed++;
            } else {
                console.log(`❌ ${test.name}: 失败`);
            }
        } catch (error) {
            console.log(`❌ ${test.name}: 错误 - ${error.message}`);
        }
    }
    
    console.log(`\n📊 系统集成测试结果: ${passed}/${total} 通过 (${((passed/total)*100).toFixed(1)}%)`);
    
    return passed === total;
}

async function runAllTests() {
    console.log('🚀 开始运行完整的管理员面板测试套件...\n');
    console.log('=' * 60);
    
    const startTime = Date.now();
    
    // Run all test suites
    const frontendResult = await testFrontendPages();
    const apiResult = await testAPIIntegration();
    const systemResult = await testSystemIntegration();
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    // Print final summary
    console.log('\n' + '=' * 60);
    console.log('📋 最终测试总结');
    console.log('=' * 60);
    
    const allPassed = frontendResult && apiResult && systemResult;
    
    console.log(`前端页面测试: ${frontendResult ? '✅ 通过' : '❌ 失败'}`);
    console.log(`API集成测试: ${apiResult ? '✅ 通过' : '❌ 失败'}`);
    console.log(`系统集成测试: ${systemResult ? '✅ 通过' : '❌ 失败'}`);
    console.log(`总体结果: ${allPassed ? '✅ 全部通过' : '❌ 部分失败'}`);
    console.log(`测试时间: ${duration.toFixed(2)}秒`);
    
    if (allPassed) {
        console.log('\n🎉 恭喜！管理员面板系统完全就绪！');
        console.log('🌐 前端地址: http://localhost:3002/admin/dashboard');
        console.log('📚 API文档: http://localhost:3001/documentation');
    } else {
        console.log('\n⚠️ 部分测试失败，请检查相关组件');
    }
    
    console.log('\n✨ 测试完成！');
}

// Check if fetch is available
if (typeof fetch === 'undefined') {
    console.log('正在导入fetch...');
    import('node-fetch').then(({ default: fetch }) => {
        global.fetch = fetch;
        runAllTests();
    }).catch(() => {
        console.log('请安装node-fetch: npm install node-fetch');
    });
} else {
    runAllTests();
}
