http:
  routers:
    test-http:
      entryPoints:
        - "http"
      rule: "Host (`test.example.com`)"
      middlewares:
        - https-redirect
      service: llm
    test-https:
      entryPoints:
        - "https"
      rule: "Host (`test.example.com`)"
      tls:
        certResolver: le
      service: llm
  services:
    test:
      loadBalancer:
        servers:
          - url: "http://127.0.0.1:18080"
  middlewares:
    https-redirect:
      redirectScheme:
        scheme: https
        permanent: true

