#!/bin/bash

# 开发环境启动脚本

echo "🚀 Starting RSDH Backend Development Server..."

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the rsdh-backend directory."
    exit 1
fi

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "⚠️  Warning: .env file not found. Please copy .env.example to .env and configure it."
    echo "   cp .env.example .env"
    exit 1
fi

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    pnpm install
fi

# 生成Prisma客户端
echo "🔧 Generating Prisma client..."
npx prisma generate

# 检查数据库连接并同步
echo "🗄️  Checking database connection..."
npx prisma db push

# 构建项目
echo "🔨 Building project..."
npm run build

# 启动开发服务器
echo "🌟 Starting development server on http://localhost:3001"
echo "📚 API Documentation: http://localhost:3001/documentation"
echo "🔍 Health Check: http://localhost:3001/health"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

npm run dev
