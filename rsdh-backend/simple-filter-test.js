const fetch = require('node-fetch');

const BASE_URL = 'http://127.0.0.1:3003';

async function login(email, password) {
  const response = await fetch(`${BASE_URL}/api/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email, password })
  });

  if (!response.ok) {
    throw new Error(`Login failed: ${response.status}`);
  }

  const data = await response.json();
  return data.token;
}

async function testFilterFunctionality(token) {
  console.log('🧪 Testing filter functionality with existing data...\n');

  const tests = [
    {
      name: 'All tutors (baseline)',
      url: '/api/tutors',
      description: 'Get all approved tutors'
    },
    {
      name: 'Filter by 985 universities',
      url: '/api/tutors?is985=true',
      description: 'Should return tutors from 985 universities'
    },
    {
      name: 'Filter by 211 universities',
      url: '/api/tutors?is211=true',
      description: 'Should return tutors from 211 universities'
    },
    {
      name: 'Filter by institution (Tsinghua)',
      url: '/api/tutors?institution=Tsinghua',
      description: 'Should return tutors from Tsinghua University'
    },
    {
      name: 'Filter by institution (partial - Beijing)',
      url: '/api/tutors?institution=Beijing',
      description: 'Should return tutors from institutions containing "Beijing"'
    },
    {
      name: 'Filter by career (Software)',
      url: '/api/tutors?career=Software',
      description: 'Should return tutors with Software in their career'
    },
    {
      name: 'Filter by career (Teacher)',
      url: '/api/tutors?career=Teacher',
      description: 'Should return tutors with Teacher in their career'
    },
    {
      name: 'Filter by degree (Bachelor)',
      url: '/api/tutors?degree=Bachelor',
      description: 'Should return tutors with Bachelor degree'
    },
    {
      name: 'Filter by degree (Master)',
      url: '/api/tutors?degree=Master',
      description: 'Should return tutors with Master degree'
    },
    {
      name: 'Filter by degree (PhD)',
      url: '/api/tutors?degree=PhD',
      description: 'Should return tutors with PhD degree'
    },
    {
      name: 'Filter by graduation year (2019)',
      url: '/api/tutors?graduationYearFrom=2019&graduationYearTo=2019',
      description: 'Should return tutors who graduated in 2019'
    },
    {
      name: 'Filter by graduation year range (2018-2020)',
      url: '/api/tutors?graduationYearFrom=2018&graduationYearTo=2020',
      description: 'Should return tutors who graduated between 2018-2020'
    },
    {
      name: 'Filter by price range (100-200)',
      url: '/api/tutors?priceFrom=100&priceTo=200',
      description: 'Should return tutors with hourly rate between 100-200'
    },
    {
      name: 'Filter by minimum price (200)',
      url: '/api/tutors?priceFrom=200',
      description: 'Should return tutors with hourly rate >= 200'
    },
    {
      name: 'Filter by maximum price (200)',
      url: '/api/tutors?priceTo=200',
      description: 'Should return tutors with hourly rate <= 200'
    },
    {
      name: 'Combined filter (985 + Software)',
      url: '/api/tutors?is985=true&career=Software',
      description: 'Should return tutors from 985 universities with Software career'
    },
    {
      name: 'Combined filter (211 + price 100-300)',
      url: '/api/tutors?is211=true&priceFrom=100&priceTo=300',
      description: 'Should return tutors from 211 universities with price 100-300'
    },
    {
      name: 'Search with basic text',
      url: '/api/tutors?search=software',
      description: 'Should return tutors with "software" in title, bio, or name'
    },
    {
      name: 'No results filter (very high price)',
      url: '/api/tutors?priceFrom=10000',
      description: 'Should return no tutors (price too high)'
    },
    {
      name: 'Pagination test',
      url: '/api/tutors?page=1&limit=2',
      description: 'Should return paginated results'
    }
  ];

  let successCount = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}`);
      console.log(`  Description: ${test.description}`);
      console.log(`  URL: ${test.url}`);

      const response = await fetch(`${BASE_URL}${test.url}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        console.log(`  ❌ HTTP Error: ${response.status}`);
        const errorText = await response.text();
        console.log(`  Error details: ${errorText}`);
        continue;
      }

      const data = await response.json();
      console.log(`  ✅ Success: Found ${data.tutors.length} tutors`);
      console.log(`  Total: ${data.total}, Page: ${data.page}, Pages: ${data.totalPages}`);
      
      // Show first tutor details if available
      if (data.tutors.length > 0) {
        const tutor = data.tutors[0];
        console.log(`  Sample: ${tutor.user.name} - ${tutor.title} (Rate: ¥${tutor.hourlyRate})`);
        if (tutor.education && tutor.education.length > 0) {
          const edu = tutor.education[0];
          console.log(`    Education: ${edu.degree} in ${edu.fieldOfStudy} from ${edu.institution} (985: ${edu.is985}, 211: ${edu.is211})`);
        }
        if (tutor.career && tutor.career.length > 0) {
          const career = tutor.career[0];
          console.log(`    Career: ${career.title} at ${career.company}`);
        }
      }
      
      successCount++;
      console.log('');
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
      console.log('');
    }
  }

  console.log(`📊 Test Summary: ${successCount}/${totalTests} tests completed successfully`);
  
  if (successCount === totalTests) {
    console.log('🎉 All filter tests passed!');
  } else {
    console.log('⚠️  Some tests had issues, but this might be expected if there\'s limited test data.');
  }
}

async function main() {
  try {
    console.log('🚀 Starting simple filter functionality test...\n');

    // Login as regular user
    const userToken = await login('<EMAIL>', 'Test1234');
    console.log('✅ User logged in successfully\n');

    // Test all filter functionality
    await testFilterFunctionality(userToken);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

main();
