{"name": "fastify-prisma-app", "version": "1.0.0", "description": "Fastify API with TypeScript and Prisma", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "lint": "eslint . --ext .ts", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "keywords": ["fastify", "typescript", "prisma", "postgresql"], "author": "", "license": "MIT", "packageManager": "pnpm@10.10.0", "dependencies": {"@aws-sdk/client-s3": "^3.705.0", "@aws-sdk/s3-request-presigner": "^3.705.0", "@fastify/cors": "^11.0.1", "@fastify/env": "^5.0.2", "@fastify/helmet": "^13.0.1", "@fastify/multipart": "^9.0.1", "@fastify/static": "^8.2.0", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@prisma/client": "6.10.1", "@radix-ui/react-progress": "^1.1.7", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.10", "@types/mime-types": "^2.1.4", "@types/nodemailer": "^6.4.17", "@types/validator": "^13.15.2", "bcrypt": "^6.0.0", "better-auth": "^1.2.12", "dotenv": "^16.4.1", "fastify": "^5.4.0", "fastify-plugin": "^5.0.1", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "node-fetch": "^2.7.0", "nodemailer": "^7.0.3", "recharts": "^3.0.2", "validator": "^13.15.15"}, "devDependencies": {"@types/node": "^20.11.0", "@types/pino-http": "^5.0.4", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "c8": "^10.1.3", "form-data": "^4.0.3", "prisma": "6.10.1", "tsx": "^4.7.1", "typescript": "^5.3.3", "vitest": "^3.2.4"}}