import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import { createApp } from '../../src/app';
import { prisma } from '../../src/lib/prisma';
import { TestUtils } from '../helpers/testUtils';
import { FastifyInstance } from 'fastify';
import jwt from 'jsonwebtoken';

describe('Feedback Routes', () => {
  let app: FastifyInstance;
  let testUser: any;
  let testAdmin: any;
  let testTutor: any;
  let testAppointment: any;
  let testFile: any;
  let userToken: string;
  let adminToken: string;

  beforeAll(async () => {
    app = await createApp();
    await app.ready();

    // Create test users
    testUser = await TestUtils.createTestUser('<EMAIL>', 'Feedback Route User');
    testAdmin = await TestUtils.createTestUser('<EMAIL>', 'Feedback Route Admin');

    // Update admin role
    await prisma.user.update({
      where: { id: testAdmin.id },
      data: { role: 'admin' }
    });

    // Create test tutor
    testTutor = await TestUtils.createTestTutor(testAdmin.id, {
      title: 'Test Tutor for Feedback Routes'
    });

    // Create test appointment
    testAppointment = await TestUtils.createTestAppointment(testTutor.id, testUser.id, {
      status: 'completed'
    });

    // Create test file
    testFile = await prisma.file.create({
      data: {
        originalName: 'feedback-route-attachment.jpg',
        fileName: 'feedback-route-attachment-123.jpg',
        mimeType: 'image/jpeg',
        size: 1024,
        s3Key: 'test/feedback-route-attachment-123.jpg',
        s3Bucket: 'test-bucket',
        s3Region: 'us-east-1',
        uploadedById: testUser.id,
        category: 'feedback'
      }
    });

    // Get auth tokens
    userToken = jwt.sign({ userId: testUser.id }, process.env.JWT_SECRET || 'test-secret');
    adminToken = jwt.sign({ userId: testAdmin.id }, process.env.JWT_SECRET || 'test-secret');
  });

  afterAll(async () => {
    await TestUtils.cleanDatabase();
    await app.close();
  });

  beforeEach(async () => {
    // Clean up feedback data before each test
    await prisma.feedbackFile.deleteMany({});
    await prisma.feedback.deleteMany({});
  });

  describe('POST /api/feedback', () => {
    it('should create basic feedback successfully', async () => {
      const feedbackData = {
        type: 'general',
        title: 'Test Feedback',
        content: 'This is a test feedback',
        category: 'general'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/feedback',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: feedbackData
      });

      expect(response.statusCode).toBe(201);
      const feedback = JSON.parse(response.payload);
      expect(feedback.type).toBe('general');
      expect(feedback.title).toBe('Test Feedback');
      expect(feedback.content).toBe('This is a test feedback');
      expect(feedback.status).toBe('pending');
      expect(feedback.user.id).toBe(testUser.id);
    });

    it('should create feedback with file attachments', async () => {
      const feedbackData = {
        content: 'Feedback with attachments',
        type: 'bug',
        fileIds: [testFile.id]
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/feedback',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: feedbackData
      });

      expect(response.statusCode).toBe(201);
      const feedback = JSON.parse(response.payload);
      expect(feedback.files).toBeDefined();
      expect(feedback.files.length).toBe(1);
      expect(feedback.files[0].file.id).toBe(testFile.id);
    });

    it('should create feedback related to tutor', async () => {
      const feedbackData = {
        content: 'Feedback about tutor',
        type: 'tutor_feedback',
        relatedTutorId: testTutor.id
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/feedback',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: feedbackData
      });

      expect(response.statusCode).toBe(201);
      const feedback = JSON.parse(response.payload);
      expect(feedback.relatedTutorId).toBe(testTutor.id);
      expect(feedback.relatedTutor).toBeDefined();
    });

    it('should create feedback related to appointment', async () => {
      const feedbackData = {
        content: 'Feedback about appointment',
        type: 'appointment_feedback',
        relatedAppointmentId: testAppointment.id
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/feedback',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: feedbackData
      });

      expect(response.statusCode).toBe(201);
      const feedback = JSON.parse(response.payload);
      expect(feedback.relatedAppointmentId).toBe(testAppointment.id);
      expect(feedback.relatedAppointment).toBeDefined();
    });

    it('should return 400 for missing content', async () => {
      const feedbackData = {
        type: 'general'
        // Missing content
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/feedback',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: feedbackData
      });

      expect(response.statusCode).toBe(400);
    });

    it('should return 400 for non-existent tutor', async () => {
      const feedbackData = {
        content: 'Feedback about non-existent tutor',
        relatedTutorId: 'non-existent-id'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/feedback',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: feedbackData
      });

      expect(response.statusCode).toBe(400);
      const error = JSON.parse(response.payload);
      expect(error.message).toBe('Related tutor not found');
    });

    it('should return 401 for unauthenticated request', async () => {
      const feedbackData = {
        content: 'Unauthorized feedback'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/feedback',
        payload: feedbackData
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe('GET /api/feedback/:id', () => {
    let testFeedback: any;

    beforeEach(async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/feedback',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          content: 'Test feedback for retrieval'
        }
      });
      testFeedback = JSON.parse(response.payload);
    });

    it('should get feedback by ID successfully', async () => {
      const response = await app.inject({
        method: 'GET',
        url: `/api/feedback/${testFeedback.id}`,
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const feedback = JSON.parse(response.payload);
      expect(feedback.id).toBe(testFeedback.id);
      expect(feedback.content).toBe('Test feedback for retrieval');
    });

    it('should allow admin to access any feedback', async () => {
      const response = await app.inject({
        method: 'GET',
        url: `/api/feedback/${testFeedback.id}`,
        headers: {
          authorization: `Bearer ${adminToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const feedback = JSON.parse(response.payload);
      expect(feedback.id).toBe(testFeedback.id);
    });

    it('should return 404 for non-existent feedback', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/feedback/non-existent-id',
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(response.statusCode).toBe(404);
    });

    it('should return 401 for unauthenticated request', async () => {
      const response = await app.inject({
        method: 'GET',
        url: `/api/feedback/${testFeedback.id}`
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe('GET /api/feedback', () => {
    beforeEach(async () => {
      // Create some test feedback
      await app.inject({
        method: 'POST',
        url: '/api/feedback',
        headers: { authorization: `Bearer ${userToken}` },
        payload: { content: 'First feedback', type: 'general' }
      });
      await app.inject({
        method: 'POST',
        url: '/api/feedback',
        headers: { authorization: `Bearer ${userToken}` },
        payload: { content: 'Second feedback', type: 'bug' }
      });
      await app.inject({
        method: 'POST',
        url: '/api/feedback',
        headers: { authorization: `Bearer ${adminToken}` },
        payload: { content: 'Admin feedback', type: 'suggestion' }
      });
    });

    it('should get paginated feedback list', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/feedback?page=1&limit=2',
        headers: {
          authorization: `Bearer ${adminToken}` // Admin can see all
        }
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.payload);
      expect(result.items).toHaveLength(2);
      expect(result.total).toBe(3);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(2);
    });

    it('should filter user feedback for non-admin', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/feedback',
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.payload);
      expect(result.items).toHaveLength(2); // Only user's feedback
      expect(result.items.every((item: any) => item.userId === testUser.id)).toBe(true);
    });

    it('should filter feedback by type', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/feedback?type=bug',
        headers: {
          authorization: `Bearer ${adminToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.payload);
      expect(result.items).toHaveLength(1);
      expect(result.items[0].type).toBe('bug');
    });

    it('should return 401 for unauthenticated request', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/feedback'
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe('PATCH /api/feedback/:id', () => {
    let testFeedback: any;

    beforeEach(async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/feedback',
        headers: { authorization: `Bearer ${userToken}` },
        payload: { content: 'Feedback to update' }
      });
      testFeedback = JSON.parse(response.payload);
    });

    it('should update feedback status by admin', async () => {
      const response = await app.inject({
        method: 'PATCH',
        url: `/api/feedback/${testFeedback.id}`,
        headers: {
          authorization: `Bearer ${adminToken}`
        },
        payload: {
          status: 'in_progress',
          priority: 'high'
        }
      });

      expect(response.statusCode).toBe(200);
      const feedback = JSON.parse(response.payload);
      expect(feedback.status).toBe('in_progress');
      expect(feedback.priority).toBe('high');
    });

    it('should return 403 for non-admin user', async () => {
      const response = await app.inject({
        method: 'PATCH',
        url: `/api/feedback/${testFeedback.id}`,
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          status: 'resolved'
        }
      });

      expect(response.statusCode).toBe(403);
    });
  });

  describe('DELETE /api/feedback/:id', () => {
    let testFeedback: any;

    beforeEach(async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/feedback',
        headers: { authorization: `Bearer ${userToken}` },
        payload: { content: 'Feedback to delete' }
      });
      testFeedback = JSON.parse(response.payload);
    });

    it('should allow user to delete their own pending feedback', async () => {
      const response = await app.inject({
        method: 'DELETE',
        url: `/api/feedback/${testFeedback.id}`,
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.payload);
      expect(result.success).toBe(true);
    });

    it('should return 404 for non-existent feedback', async () => {
      const response = await app.inject({
        method: 'DELETE',
        url: '/api/feedback/non-existent-id',
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(response.statusCode).toBe(404);
    });
  });

  describe('GET /api/feedback/admin/stats', () => {
    beforeEach(async () => {
      // Create various feedback for stats
      await app.inject({
        method: 'POST',
        url: '/api/feedback',
        headers: { authorization: `Bearer ${userToken}` },
        payload: { content: 'Pending feedback', type: 'general' }
      });

      const highPriorityResponse = await app.inject({
        method: 'POST',
        url: '/api/feedback',
        headers: { authorization: `Bearer ${userToken}` },
        payload: { content: 'High priority feedback', type: 'bug' }
      });
      const highPriorityFeedback = JSON.parse(highPriorityResponse.payload);

      // Update priority to high
      await app.inject({
        method: 'PATCH',
        url: `/api/feedback/${highPriorityFeedback.id}`,
        headers: { authorization: `Bearer ${adminToken}` },
        payload: { priority: 'high' }
      });
    });

    it('should get comprehensive feedback statistics', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/feedback/admin/stats',
        headers: {
          authorization: `Bearer ${adminToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const stats = JSON.parse(response.payload);
      expect(stats.total).toBeGreaterThan(0);
      expect(stats.pending).toBeGreaterThan(0);
      expect(stats.highPriority).toBeGreaterThan(0);
      expect(Array.isArray(stats.byType)).toBe(true);
      expect(Array.isArray(stats.byCategory)).toBe(true);
    });

    it('should return 403 for non-admin user', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/feedback/admin/stats',
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(response.statusCode).toBe(403);
    });
  });
});
