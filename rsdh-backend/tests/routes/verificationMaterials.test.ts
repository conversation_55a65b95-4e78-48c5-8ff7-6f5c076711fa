import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { createApp } from '../../src/app';
import { prisma } from '../../src/lib/prisma';
import { UserService } from '../../src/services/userService';
import { TutorService } from '../../src/services/tutorService';
import { FastifyInstance } from 'fastify';

describe('Verification Materials Routes', () => {
  let app: FastifyInstance;
  let testUser: any;
  let testAdmin: any;
  let testTutor: any;
  let testEducation: any;
  let testCareer: any;
  let testFile: any;
  let userToken: string;
  let adminToken: string;

  beforeEach(async () => {
    app = await createApp();
    await app.ready();

    // Create test user
    testUser = await UserService.createUser({
      email: '<EMAIL>',
      name: 'Test Tutor',
      password: 'password123'
    });

    // Create admin user
    testAdmin = await UserService.createUser({
      email: '<EMAIL>',
      name: 'Admin User',
      password: 'password123',
      role: 'admin'
    });

    // Login users to get tokens
    const userLoginResponse = await app.inject({
      method: 'POST',
      url: '/api/auth/login',
      payload: {
        email: '<EMAIL>',
        password: 'password123'
      }
    });
    userToken = JSON.parse(userLoginResponse.body).token;

    const adminLoginResponse = await app.inject({
      method: 'POST',
      url: '/api/auth/login',
      payload: {
        email: '<EMAIL>',
        password: 'password123'
      }
    });
    adminToken = JSON.parse(adminLoginResponse.body).token;

    // Create test tutor profile
    testTutor = await TutorService.applyToBecomeTutor({
      userId: testUser.id,
      title: 'Test Tutor',
      bio: 'Test bio',
      rate: 100,
      education: [{
        degree: 'Bachelor',
        fieldOfStudy: 'Computer Science',
        institution: 'Test University',
        startYear: 2018,
        endYear: 2022
      }],
      career: [{
        title: 'Software Engineer',
        company: 'Test Company',
        startYear: 2022,
        current: true
      }]
    });

    testEducation = testTutor.education[0];
    testCareer = testTutor.career[0];

    // Create test file
    testFile = await prisma.file.create({
      data: {
        originalName: 'test-diploma.pdf',
        fileName: 'test-diploma-123.pdf',
        mimeType: 'application/pdf',
        size: 1024,
        s3Key: 'verification/test-diploma-123.pdf',
        s3Bucket: 'test-bucket',
        s3Region: 'us-east-1',
        uploadedById: testUser.id,
        category: 'verification'
      }
    });
  });

  afterEach(async () => {
    // Clean up test data
    await prisma.educationVerification.deleteMany({});
    await prisma.careerVerification.deleteMany({});
    await prisma.file.deleteMany({});
    await prisma.tutorEducation.deleteMany({});
    await prisma.tutorCareer.deleteMany({});
    await prisma.tutorProfile.deleteMany({});
    await prisma.user.deleteMany({});
    await app.close();
  });

  describe('POST /api/verification-materials/education', () => {
    it('should add education verification material successfully', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/verification-materials/education',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          educationId: testEducation.id,
          fileId: testFile.id,
          materialType: 'diploma',
          description: 'University diploma'
        }
      });

      expect(response.statusCode).toBe(201);
      const verification = JSON.parse(response.body);
      expect(verification.educationId).toBe(testEducation.id);
      expect(verification.fileId).toBe(testFile.id);
      expect(verification.materialType).toBe('diploma');
      expect(verification.status).toBe('pending');
    });

    it('should return 400 for invalid education ID', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/verification-materials/education',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          educationId: 'invalid-id',
          fileId: testFile.id,
          materialType: 'diploma'
        }
      });

      expect(response.statusCode).toBe(400);
    });

    it('should return 401 for unauthenticated request', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/verification-materials/education',
        payload: {
          educationId: testEducation.id,
          fileId: testFile.id,
          materialType: 'diploma'
        }
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe('POST /api/verification-materials/career', () => {
    it('should add career verification material successfully', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/verification-materials/career',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          careerId: testCareer.id,
          fileId: testFile.id,
          materialType: 'work_certificate',
          description: 'Employment certificate'
        }
      });

      expect(response.statusCode).toBe(201);
      const verification = JSON.parse(response.body);
      expect(verification.careerId).toBe(testCareer.id);
      expect(verification.fileId).toBe(testFile.id);
      expect(verification.materialType).toBe('work_certificate');
    });
  });

  describe('GET /api/verification-materials/education/:educationId', () => {
    it('should get education verification materials', async () => {
      // First add a verification material
      await app.inject({
        method: 'POST',
        url: '/api/verification-materials/education',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          educationId: testEducation.id,
          fileId: testFile.id,
          materialType: 'diploma'
        }
      });

      const response = await app.inject({
        method: 'GET',
        url: `/api/verification-materials/education/${testEducation.id}`,
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const verifications = JSON.parse(response.body);
      expect(verifications).toHaveLength(1);
      expect(verifications[0].educationId).toBe(testEducation.id);
    });
  });

  describe('GET /api/verification-materials/tutor/my-verifications', () => {
    it('should get all verification materials for current tutor', async () => {
      // Add education verification
      await app.inject({
        method: 'POST',
        url: '/api/verification-materials/education',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          educationId: testEducation.id,
          fileId: testFile.id,
          materialType: 'diploma'
        }
      });

      const response = await app.inject({
        method: 'GET',
        url: '/api/verification-materials/tutor/my-verifications',
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const verifications = JSON.parse(response.body);
      expect(verifications.educationVerifications).toHaveLength(1);
      expect(verifications.careerVerifications).toHaveLength(0);
    });

    it('should return 404 for user without tutor profile', async () => {
      // Create user without tutor profile
      const regularUser = await UserService.createUser({
        email: '<EMAIL>',
        name: 'Regular User',
        password: 'password123'
      });

      const loginResponse = await app.inject({
        method: 'POST',
        url: '/api/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'password123'
        }
      });
      const regularToken = JSON.parse(loginResponse.body).token;

      const response = await app.inject({
        method: 'GET',
        url: '/api/verification-materials/tutor/my-verifications',
        headers: {
          authorization: `Bearer ${regularToken}`
        }
      });

      expect(response.statusCode).toBe(404);
    });
  });

  describe('DELETE /api/verification-materials/education/:verificationId', () => {
    it('should delete education verification material successfully', async () => {
      // First add a verification material
      const addResponse = await app.inject({
        method: 'POST',
        url: '/api/verification-materials/education',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          educationId: testEducation.id,
          fileId: testFile.id,
          materialType: 'diploma'
        }
      });
      const verification = JSON.parse(addResponse.body);

      const response = await app.inject({
        method: 'DELETE',
        url: `/api/verification-materials/education/${verification.id}`,
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(response.statusCode).toBe(204);
    });

    it('should return 404 for non-existent verification', async () => {
      const response = await app.inject({
        method: 'DELETE',
        url: '/api/verification-materials/education/non-existent-id',
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(response.statusCode).toBe(404);
    });
  });

  describe('Admin Routes', () => {
    describe('GET /api/verification-materials/admin/pending', () => {
      it('should get pending verifications for admin', async () => {
        // Add verification material
        await app.inject({
          method: 'POST',
          url: '/api/verification-materials/education',
          headers: {
            authorization: `Bearer ${userToken}`
          },
          payload: {
            educationId: testEducation.id,
            fileId: testFile.id,
            materialType: 'diploma'
          }
        });

        const response = await app.inject({
          method: 'GET',
          url: '/api/verification-materials/admin/pending',
          headers: {
            authorization: `Bearer ${adminToken}`
          }
        });

        expect(response.statusCode).toBe(200);
        const verifications = JSON.parse(response.body);
        expect(verifications.educationVerifications).toHaveLength(1);
      });

      it('should return 403 for non-admin user', async () => {
        const response = await app.inject({
          method: 'GET',
          url: '/api/verification-materials/admin/pending',
          headers: {
            authorization: `Bearer ${userToken}`
          }
        });

        expect(response.statusCode).toBe(403);
      });
    });

    describe('PUT /api/verification-materials/admin/education/:verificationId/status', () => {
      it('should update education verification status', async () => {
        // Add verification material
        const addResponse = await app.inject({
          method: 'POST',
          url: '/api/verification-materials/education',
          headers: {
            authorization: `Bearer ${userToken}`
          },
          payload: {
            educationId: testEducation.id,
            fileId: testFile.id,
            materialType: 'diploma'
          }
        });
        const verification = JSON.parse(addResponse.body);

        const response = await app.inject({
          method: 'PUT',
          url: `/api/verification-materials/admin/education/${verification.id}/status`,
          headers: {
            authorization: `Bearer ${adminToken}`
          },
          payload: {
            status: 'approved',
            reviewNotes: 'Looks good'
          }
        });

        expect(response.statusCode).toBe(200);
        const updatedVerification = JSON.parse(response.body);
        expect(updatedVerification.status).toBe('approved');
        expect(updatedVerification.reviewNotes).toBe('Looks good');
      });

      it('should return 403 for non-admin user', async () => {
        const response = await app.inject({
          method: 'PUT',
          url: '/api/verification-materials/admin/education/some-id/status',
          headers: {
            authorization: `Bearer ${userToken}`
          },
          payload: {
            status: 'approved'
          }
        });

        expect(response.statusCode).toBe(403);
      });
    });
  });
});
