import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { FastifyInstance } from 'fastify';
import { buildApp } from '../helpers/testUtils';
import { UserService } from '../../src/services/userService';
import { SMSService } from '../../src/services/smsService';
import { testPrisma } from '../setup';

describe('Auth Extended Routes', () => {
  let app: FastifyInstance;

  beforeEach(async () => {
    app = await buildApp();
    // Clean up database
    await testPrisma.user.deleteMany();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('POST /api/auth/phone/send-code', () => {
    it('should send verification code for valid phone number', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/send-code',
        payload: {
          phoneNumber: '+*************',
          type: 'register',
        },
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.message).toBe('Verification code sent successfully');
    });

    it('should reject invalid phone number', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/send-code',
        payload: {
          phoneNumber: '12345',
          type: 'register',
        },
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Invalid Phone Number');
    });

    it('should reject registration for existing phone number', async () => {
      // Create a user with phone number first
      await UserService.createUserWithPhone({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
        phoneNumber: '+*************',
      });

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/send-code',
        payload: {
          phoneNumber: '+*************',
          type: 'register',
        },
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Phone Number Exists');
    });

    it('should reject login for non-existing phone number', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/send-code',
        payload: {
          phoneNumber: '+*************',
          type: 'login',
        },
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Phone Number Not Found');
    });

    it('should normalize phone number format', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/send-code',
        payload: {
          phoneNumber: '***********', // Without country code
          type: 'register',
        },
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
    });
  });

  describe('POST /api/auth/phone/login', () => {
    let testUser: any;

    beforeEach(async () => {
      testUser = await UserService.createUserWithPhone({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Phone User',
        phoneNumber: '+*************',
      });
    });

    it('should login with phone number and password', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/login',
        payload: {
          phoneNumber: '+*************',
          password: 'password123',
          loginType: 'password',
        },
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body.message).toBe('Login successful');
      expect(body.token).toBeDefined();
      expect(body.user.phoneNumber).toBe('+*************');
    });

    it('should reject login with wrong password', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/login',
        payload: {
          phoneNumber: '+*************',
          password: 'wrongpassword',
          loginType: 'password',
        },
      });

      expect(response.statusCode).toBe(401);
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Invalid Credentials');
    });

    it('should reject login for non-existing phone number', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/login',
        payload: {
          phoneNumber: '+8613800138001',
          password: 'password123',
          loginType: 'password',
        },
      });

      expect(response.statusCode).toBe(401);
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Invalid Credentials');
    });

    it('should require password for password login', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/login',
        payload: {
          phoneNumber: '+*************',
          loginType: 'password',
        },
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Missing Password');
    });

    it('should require code for code login', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/login',
        payload: {
          phoneNumber: '+*************',
          loginType: 'code',
        },
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Missing Code');
    });

    it('should reject invalid phone number format', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/login',
        payload: {
          phoneNumber: '12345',
          password: 'password123',
          loginType: 'password',
        },
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Invalid Phone Number');
    });

    it('should reject invalid login type', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/login',
        payload: {
          phoneNumber: '+*************',
          password: 'password123',
          loginType: 'invalid',
        },
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Bad Request'); // Fastify validation error
    });
  });

  describe('POST /api/auth/phone/bind', () => {
    let testUser: any;
    let authToken: string;

    beforeEach(async () => {
      testUser = await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Bind User',
      });
      // Create a simple auth token
      authToken = Buffer.from(testUser.email).toString('base64');
    });

    it('should require authentication', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/bind',
        payload: {
          phoneNumber: '+*************',
          code: '123456',
        },
      });

      expect(response.statusCode).toBe(401);
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Unauthorized');
    });

    it('should reject invalid phone number', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/bind',
        headers: {
          authorization: `Bearer ${authToken}`,
        },
        payload: {
          phoneNumber: '12345',
          code: '123456',
        },
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Invalid Phone Number');
    });

    it('should reject phone number already bound to another account', async () => {
      // Create another user with the phone number
      await UserService.createUserWithPhone({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Other User',
        phoneNumber: '+*************',
      });

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/phone/bind',
        headers: {
          authorization: `Bearer ${authToken}`,
        },
        payload: {
          phoneNumber: '+*************',
          code: '123456',
        },
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.error).toBe('Phone Number Taken');
    });
  });

  describe('Phone number validation and normalization', () => {
    it('should handle various phone number formats', async () => {
      const formats = [
        '***********',
        '+*************',
        '*************',
        '138-0013-8000',
        '138 0013 8000',
      ];

      for (const format of formats) {
        const response = await app.inject({
          method: 'POST',
          url: '/api/auth/phone/send-code',
          payload: {
            phoneNumber: format,
            type: 'register',
          },
        });

        expect(response.statusCode).toBe(200);
      }
    });

    it('should reject invalid phone number formats', async () => {
      const invalidFormats = [
        '12800138000', // Invalid prefix
        '1380013800', // Too short
        '***********0', // Too long
        'abc***********', // Contains letters
        '', // Empty
        '1234567890', // Wrong format
      ];

      for (const format of invalidFormats) {
        const response = await app.inject({
          method: 'POST',
          url: '/api/auth/phone/send-code',
          payload: {
            phoneNumber: format,
            type: 'register',
          },
        });

        expect(response.statusCode).toBe(400);
        const body = JSON.parse(response.body);
        expect(body.error).toBe('Invalid Phone Number');
      }
    });
  });
});
