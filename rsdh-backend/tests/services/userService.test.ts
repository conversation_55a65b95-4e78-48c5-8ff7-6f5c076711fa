import { describe, it, expect, beforeEach } from 'vitest';
import { UserService } from '../../src/services/userService';
import { testPrisma } from '../setup';

describe('UserService', () => {
  const testUser = {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test User',
  };

  beforeEach(async () => {
    // Clean up before each test - use the global cleanup function
    // which handles foreign key constraints properly
    try {
      // The global setup.ts cleanup will handle this properly
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  describe('createUser', () => {
    it('should create a new user with hashed password', async () => {
      const user = await UserService.createUser(testUser);

      expect(user).toMatchObject({
        email: testUser.email,
        name: testUser.name,
        role: 'user',
        disabled: false,
        emailVerified: false,
      });
      expect(user.id).toBeDefined();
      expect(user.createdAt).toBeDefined();
      expect(user.updatedAt).toBeDefined();

      // Verify password is hashed
      const dbUser = await testPrisma.user.findUnique({
        where: { email: testUser.email },
      });
      expect(dbUser?.password).not.toBe(testUser.password);
      expect(dbUser?.password).toBeDefined();
    });

    it('should create user with admin role when specified', async () => {
      const adminUser = await UserService.createUser({
        ...testUser,
        email: '<EMAIL>',
        role: 'admin',
      });

      expect(adminUser.role).toBe('admin');
    });

    it('should create user with avatar when provided', async () => {
      const userWithAvatar = await UserService.createUser({
        ...testUser,
        email: '<EMAIL>',
        avatar: 'https://example.com/avatar.jpg',
      });

      expect(userWithAvatar.avatar).toBe('https://example.com/avatar.jpg');
    });

    it('should throw error for duplicate email', async () => {
      await UserService.createUser(testUser);

      await expect(UserService.createUser(testUser)).rejects.toThrow();
    });
  });

  describe('findByEmail', () => {
    it('should find user by email', async () => {
      await UserService.createUser(testUser);
      const user = await UserService.findByEmail(testUser.email);

      expect(user).toBeDefined();
      expect(user?.email).toBe(testUser.email);
    });

    it('should return null for non-existent email', async () => {
      const user = await UserService.findByEmail('<EMAIL>');
      expect(user).toBeNull();
    });
  });

  describe('findById', () => {
    it('should find user by ID', async () => {
      const createdUser = await UserService.createUser(testUser);
      const user = await UserService.findById(createdUser.id);

      expect(user).toBeDefined();
      expect(user?.id).toBe(createdUser.id);
      expect(user?.email).toBe(testUser.email);
    });

    it('should return null for non-existent ID', async () => {
      const user = await UserService.findById('non-existent-id');
      expect(user).toBeNull();
    });
  });

  describe('updateUser', () => {
    it('should update user name', async () => {
      const createdUser = await UserService.createUser(testUser);
      const updatedUser = await UserService.updateUser(createdUser.id, {
        name: 'Updated Name',
      });

      expect(updatedUser.name).toBe('Updated Name');
      expect(updatedUser.email).toBe(testUser.email);
    });

    it('should update user avatar', async () => {
      const createdUser = await UserService.createUser(testUser);
      const updatedUser = await UserService.updateUser(createdUser.id, {
        avatar: 'https://example.com/new-avatar.jpg',
      });

      expect(updatedUser.avatar).toBe('https://example.com/new-avatar.jpg');
    });

    it('should update user disabled status', async () => {
      const createdUser = await UserService.createUser(testUser);
      const updatedUser = await UserService.updateUser(createdUser.id, {
        disabled: true,
      });

      expect(updatedUser.disabled).toBe(true);
    });
  });

  describe('deleteUser', () => {
    it('should delete user', async () => {
      const createdUser = await UserService.createUser(testUser);
      await UserService.deleteUser(createdUser.id);

      const user = await UserService.findById(createdUser.id);
      expect(user).toBeNull();
    });

    it('should throw error for non-existent user', async () => {
      await expect(UserService.deleteUser('non-existent-id')).rejects.toThrow();
    });
  });

  describe('getAllUsers', () => {
    it('should return paginated users', async () => {
      // Ensure clean state
      await testPrisma.user.deleteMany();

      // Create multiple users
      await UserService.createUser({ ...testUser, email: '<EMAIL>' });
      await UserService.createUser({ ...testUser, email: '<EMAIL>' });
      await UserService.createUser({ ...testUser, email: '<EMAIL>' });

      const result = await UserService.getAllUsers(1, 2);

      expect(result.users).toHaveLength(2);
      expect(result.total).toBe(3);
    });

    it('should return empty array when no users exist', async () => {
      const result = await UserService.getAllUsers();

      expect(result.users).toHaveLength(0);
      expect(result.total).toBe(0);
    });
  });

  describe('userExists', () => {
    it('should return true for existing user', async () => {
      await UserService.createUser(testUser);
      const exists = await UserService.userExists(testUser.email);

      expect(exists).toBe(true);
    });

    it('should return false for non-existent user', async () => {
      const exists = await UserService.userExists('<EMAIL>');

      expect(exists).toBe(false);
    });
  });

  describe('toggleUserStatus', () => {
    it('should disable user', async () => {
      // Ensure clean state
      await testPrisma.user.deleteMany();

      const createdUser = await UserService.createUser({ ...testUser, email: '<EMAIL>' });
      const updatedUser = await UserService.toggleUserStatus(createdUser.id, true);

      expect(updatedUser.disabled).toBe(true);
    });

    it('should enable user', async () => {
      const createdUser = await UserService.createUser(testUser);
      await UserService.toggleUserStatus(createdUser.id, true);
      const updatedUser = await UserService.toggleUserStatus(createdUser.id, false);

      expect(updatedUser.disabled).toBe(false);
    });
  });

  describe('updateUserRole', () => {
    it('should update user role to admin', async () => {
      const createdUser = await UserService.createUser(testUser);
      const updatedUser = await UserService.updateUserRole(createdUser.id, 'admin');

      expect(updatedUser.role).toBe('admin');
    });

    it('should update user role to user', async () => {
      const createdUser = await UserService.createUser({ ...testUser, role: 'admin' });
      const updatedUser = await UserService.updateUserRole(createdUser.id, 'user');

      expect(updatedUser.role).toBe('user');
    });
  });

  describe('hashPassword and verifyPassword', () => {
    it('should hash and verify password correctly', async () => {
      const password = 'testpassword123';
      const hashedPassword = await UserService.hashPassword(password);

      expect(hashedPassword).not.toBe(password);
      expect(hashedPassword).toBeDefined();

      const isValid = await UserService.verifyPassword(password, hashedPassword);
      expect(isValid).toBe(true);

      const isInvalid = await UserService.verifyPassword('wrongpassword', hashedPassword);
      expect(isInvalid).toBe(false);
    });
  });

  describe('Phone number operations', () => {
    describe('findByPhoneNumber', () => {
      it('should find user by phone number', async () => {
        const userWithPhone = await UserService.createUserWithPhone({
          ...testUser,
          email: '<EMAIL>',
          phoneNumber: '+*************',
        });

        const foundUser = await UserService.findByPhoneNumber('+*************');
        expect(foundUser).toBeDefined();
        expect(foundUser?.phoneNumber).toBe('+*************');
        expect(foundUser?.id).toBe(userWithPhone.id);
      });

      it('should return null for non-existent phone number', async () => {
        const user = await UserService.findByPhoneNumber('+8613800138001');
        expect(user).toBeNull();
      });
    });

    describe('findByPhoneNumberForAuth', () => {
      it('should find user with password for authentication', async () => {
        await UserService.createUserWithPhone({
          ...testUser,
          email: '<EMAIL>',
          phoneNumber: '+*************',
        });

        const user = await UserService.findByPhoneNumberForAuth('+*************');
        expect(user).toBeDefined();
        expect(user?.phoneNumber).toBe('+*************');
        expect(user?.password).toBeDefined();
      });
    });

    describe('createUserWithPhone', () => {
      it('should create user with phone number', async () => {
        const user = await UserService.createUserWithPhone({
          ...testUser,
          email: '<EMAIL>',
          phoneNumber: '+*************',
        });

        expect(user.phoneNumber).toBe('+*************');
        expect(user.phoneNumberVerified).toBe(true);
      });

      it('should create user without phone number', async () => {
        const user = await UserService.createUserWithPhone({
          ...testUser,
          email: '<EMAIL>',
        });

        expect(user.phoneNumber).toBeNull();
        expect(user.phoneNumberVerified).toBe(false);
      });
    });

    describe('updatePhoneNumber', () => {
      it('should update user phone number', async () => {
        const createdUser = await UserService.createUser({
          ...testUser,
          email: '<EMAIL>',
        });

        const updatedUser = await UserService.updatePhoneNumber(
          createdUser.id,
          '+*************',
          true
        );

        expect(updatedUser.phoneNumber).toBe('+*************');
        expect(updatedUser.phoneNumberVerified).toBe(true);
      });
    });

    describe('phoneNumberExists', () => {
      it('should return true for existing phone number', async () => {
        await UserService.createUserWithPhone({
          ...testUser,
          email: '<EMAIL>',
          phoneNumber: '+*************',
        });

        const exists = await UserService.phoneNumberExists('+*************');
        expect(exists).toBe(true);
      });

      it('should return false for non-existent phone number', async () => {
        const exists = await UserService.phoneNumberExists('+8613800138001');
        expect(exists).toBe(false);
      });
    });

    describe('verifyPhoneNumber', () => {
      it('should verify user phone number', async () => {
        const createdUser = await UserService.createUserWithPhone({
          ...testUser,
          email: '<EMAIL>',
          phoneNumber: '+*************',
        });

        // Set as unverified first
        await UserService.updatePhoneNumber(createdUser.id, '+*************', false);

        const verifiedUser = await UserService.verifyPhoneNumber(createdUser.id);
        expect(verifiedUser.phoneNumberVerified).toBe(true);
      });
    });
  });

  describe('WeChat operations', () => {
    describe('bindWeChatAccount', () => {
      it('should bind WeChat account to user', async () => {
        const createdUser = await UserService.createUser({
          ...testUser,
          email: '<EMAIL>',
        });

        await expect(
          UserService.bindWeChatAccount(createdUser.id, {
            openid: 'wx_openid_123',
            unionid: 'wx_unionid_456',
            nickname: 'WeChat User',
            headimgurl: 'https://example.com/avatar.jpg',
          })
        ).resolves.not.toThrow();
      });
    });

    describe('findByWeChatOpenId', () => {
      it('should find user by WeChat OpenID', async () => {
        const createdUser = await UserService.createUser({
          ...testUser,
          email: '<EMAIL>',
        });

        await UserService.bindWeChatAccount(createdUser.id, {
          openid: 'wx_openid_find',
          nickname: 'WeChat Find User',
        });

        const foundUser = await UserService.findByWeChatOpenId('wx_openid_find');
        expect(foundUser).toBeDefined();
        expect(foundUser?.id).toBe(createdUser.id);
      });

      it('should return null for non-existent WeChat OpenID', async () => {
        const user = await UserService.findByWeChatOpenId('non_existent_openid');
        expect(user).toBeNull();
      });
    });
  });
});
