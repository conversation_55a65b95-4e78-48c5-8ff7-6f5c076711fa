import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { prisma } from '../../src/lib/prisma';
import { TutorService } from '../../src/services/tutorService';

describe('TutorService - Simple Filter Test', () => {
  let testUser: any;
  let testTutor: any;

  beforeAll(async () => {
    // Clean up any existing test data
    await prisma.tutorEducation.deleteMany({});
    await prisma.tutorCareer.deleteMany({});
    await prisma.tutorProfile.deleteMany({});
    await prisma.user.deleteMany({
      where: {
        email: {
          contains: 'simplefilter.test'
        }
      }
    });

    // Create test user
    testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test Tutor',
        emailVerified: true
      }
    });

    // Create test tutor
    testTutor = await prisma.tutorProfile.create({
      data: {
        userId: testUser.id,
        title: 'Software Engineer',
        bio: 'Experienced software engineer',
        hourlyRate: 200,
        halfHourRate: 100,
        status: 'approved'
      }
    });

    // Create education record
    await prisma.tutorEducation.create({
      data: {
        tutorId: testTutor.id,
        degree: 'Bachelor',
        fieldOfStudy: 'Computer Science',
        institution: 'Tsinghua University',
        startYear: 2015,
        endYear: 2019,
        is985: true,
        is211: true
      }
    });

    // Create career record
    await prisma.tutorCareer.create({
      data: {
        tutorId: testTutor.id,
        title: 'Software Engineer',
        company: 'ByteDance',
        startYear: 2019,
        endYear: null,
        current: true
      }
    });
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.tutorEducation.deleteMany({});
    await prisma.tutorCareer.deleteMany({});
    await prisma.tutorProfile.deleteMany({});
    await prisma.user.deleteMany({
      where: {
        email: {
          contains: 'simplefilter.test'
        }
      }
    });
  });

  beforeEach(async () => {
    // Ensure test data exists (might have been cleaned up by global cleanup)
    const existingUser = await prisma.user.findUnique({ where: { id: testUser.id } });
    if (!existingUser) {
      // Recreate test data
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test Tutor',
          emailVerified: true
        }
      });

      testTutor = await prisma.tutorProfile.create({
        data: {
          userId: testUser.id,
          title: 'Software Engineer',
          bio: 'Experienced software engineer',
          hourlyRate: 200,
          halfHourRate: 100,
          status: 'approved'
        }
      });

      await prisma.tutorEducation.create({
        data: {
          tutorId: testTutor.id,
          degree: 'Bachelor',
          fieldOfStudy: 'Computer Science',
          institution: 'Tsinghua University',
          startYear: 2015,
          endYear: 2019,
          is985: true,
          is211: true
        }
      });

      await prisma.tutorCareer.create({
        data: {
          tutorId: testTutor.id,
          title: 'Software Engineer',
          company: 'Tech Corp',
          startYear: 2019,
          current: true
        }
      });
    }
  });

  it('should get all tutors without filters', async () => {
    const result = await TutorService.getAllTutors({
      status: 'approved'
    });

    expect(result.tutors).toHaveLength(1);
    expect(result.tutors[0].user.name).toBe('Test Tutor');
  });

  it('should filter by 985 university', async () => {
    const result = await TutorService.getAllTutors({
      status: 'approved',
      is985: true
    });

    expect(result.tutors).toHaveLength(1);
    expect(result.tutors[0].user.name).toBe('Test Tutor');
  });

  it('should filter by institution name', async () => {
    const result = await TutorService.getAllTutors({
      status: 'approved',
      institution: 'Tsinghua'
    });

    expect(result.tutors).toHaveLength(1);
    expect(result.tutors[0].user.name).toBe('Test Tutor');
  });

  it('should filter by price range', async () => {
    const result = await TutorService.getAllTutors({
      status: 'approved',
      priceFrom: 150,
      priceTo: 250
    });

    expect(result.tutors).toHaveLength(1);
    expect(result.tutors[0].user.name).toBe('Test Tutor');
  });

  it('should return empty when price filter excludes tutor', async () => {
    const result = await TutorService.getAllTutors({
      status: 'approved',
      priceFrom: 300
    });

    expect(result.tutors).toHaveLength(0);
  });

  it('should filter by career', async () => {
    const result = await TutorService.getAllTutors({
      status: 'approved',
      career: 'Software'
    });

    expect(result.tutors).toHaveLength(1);
    expect(result.tutors[0].user.name).toBe('Test Tutor');
  });

  it('should filter by company', async () => {
    const result = await TutorService.getAllTutors({
      status: 'approved',
      career: 'Tech Corp'
    });

    expect(result.tutors).toHaveLength(1);
    expect(result.tutors[0].user.name).toBe('Test Tutor');
  });

  it('should filter by degree', async () => {
    const result = await TutorService.getAllTutors({
      status: 'approved',
      degree: 'Bachelor'
    });

    expect(result.tutors).toHaveLength(1);
    expect(result.tutors[0].user.name).toBe('Test Tutor');
  });

  it('should combine multiple filters', async () => {
    const result = await TutorService.getAllTutors({
      status: 'approved',
      is985: true,
      priceFrom: 100,
      priceTo: 300,
      career: 'Software'
    });

    expect(result.tutors).toHaveLength(1);
    expect(result.tutors[0].user.name).toBe('Test Tutor');
  });

  it('should return empty when combined filters exclude tutor', async () => {
    const result = await TutorService.getAllTutors({
      status: 'approved',
      is985: true,
      priceFrom: 500 // Price too high
    });

    expect(result.tutors).toHaveLength(0);
  });
});
