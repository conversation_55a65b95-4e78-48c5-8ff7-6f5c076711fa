import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { prisma } from '../../src/lib/prisma';
import { AppointmentService, CreateAppointmentData } from '../../src/services/appointmentService';

describe('AppointmentService', () => {
  let testStudentId: string;
  let testTutorUserId: string;
  let testTutorId: string;

  beforeEach(async () => {
    // Create a test student
    const testStudent = await prisma.user.create({
      data: {
        email: `student-${Date.now()}@example.com`,
        name: 'Test Student',
        password: 'hashedpassword',
        role: 'user'
      }
    });
    testStudentId = testStudent.id;

    // Create a test tutor user
    const testTutorUser = await prisma.user.create({
      data: {
        email: `tutor-${Date.now()}@example.com`,
        name: 'Test Tutor',
        password: 'hashedpassword',
        role: 'user'
      }
    });
    testTutorUserId = testTutorUser.id;

    // Create tutor profile
    const tutorProfile = await prisma.tutorProfile.create({
      data: {
        userId: testTutorUserId,
        title: 'Test Tutor',
        bio: 'Test bio',
        hourlyRate: BigInt(10000), // 100 yuan in fen
        halfHourRate: BigInt(5000), // 50 yuan in fen
        status: 'approved'
      }
    });
    testTutorId = tutorProfile.id;

    // Add availability for the tutor (Monday 9-17)
    await prisma.tutorAvailability.create({
      data: {
        tutorId: testTutorId,
        dayOfWeek: 1, // Monday
        startTime: '09:00',
        endTime: '17:00'
      }
    });
  });

  afterEach(async () => {
    // Clean up test data in correct order
    try {
      await prisma.appointment.deleteMany({
        where: {
          OR: [
            { tutorId: testTutorId },
            { studentId: testStudentId }
          ]
        }
      });

      await prisma.tutorAvailability.deleteMany({
        where: { tutorId: testTutorId }
      });

      await prisma.tutorProfile.delete({
        where: { id: testTutorId }
      });

      await prisma.user.deleteMany({
        where: {
          id: { in: [testStudentId, testTutorUserId] }
        }
      });
    } catch (error) {
      // Ignore cleanup errors in tests
    }
  });

  describe('createAppointment', () => {
    it('should create appointment successfully', async () => {
      // Create appointment for next Monday 10-11 AM
      const nextMonday = getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      const appointmentData: CreateAppointmentData = {
        tutorId: testTutorId,
        studentId: testStudentId,
        startTime,
        endTime,
        meetingType: 'online',
        meetingLink: 'https://zoom.us/j/123456789',
        notes: 'Test appointment'
      };

      const result = await AppointmentService.createAppointment(appointmentData);

      expect(result).toBeDefined();
      expect(result.appointment).toBeDefined();
      expect(result.appointment.tutorId).toBe(testTutorId);
      expect(result.appointment.studentId).toBe(testStudentId);
      expect(result.appointment.meetingType).toBe('online');
      expect(result.appointment.meetingLink).toBe('https://zoom.us/j/123456789');
      expect(result.appointment.status).toBe('scheduled');
    });

    it('should throw error if tutor not found', async () => {
      const nextMonday = getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      const appointmentData: CreateAppointmentData = {
        tutorId: 'non-existent-tutor',
        studentId: testStudentId,
        startTime,
        endTime,
        meetingType: 'online',
        meetingLink: 'https://zoom.us/j/123456789'
      };

      await expect(AppointmentService.createAppointment(appointmentData))
        .rejects.toThrow('Tutor not found');
    });

    it('should throw error if tutor is not approved', async () => {
      // Create unapproved tutor
      const unapprovedTutorUser = await prisma.user.create({
        data: {
          email: `unapproved-tutor-${Date.now()}@example.com`,
          name: 'Unapproved Tutor',
          password: 'hashedpassword'
        }
      });

      const unapprovedTutor = await prisma.tutorProfile.create({
        data: {
          userId: unapprovedTutorUser.id,
          title: 'Unapproved Tutor',
          status: 'pending'
        }
      });

      const nextMonday = getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      const appointmentData: CreateAppointmentData = {
        tutorId: unapprovedTutor.id,
        studentId: testStudentId,
        startTime,
        endTime,
        meetingType: 'online',
        meetingLink: 'https://zoom.us/j/123456789'
      };

      await expect(AppointmentService.createAppointment(appointmentData))
        .rejects.toThrow('Tutor is not approved');

      // Clean up
      await prisma.tutorProfile.delete({ where: { id: unapprovedTutor.id } });
      await prisma.user.delete({ where: { id: unapprovedTutorUser.id } });
    });

    it('should throw error if student not found', async () => {
      const nextMonday = getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      const appointmentData: CreateAppointmentData = {
        tutorId: testTutorId,
        studentId: 'non-existent-student',
        startTime,
        endTime,
        meetingType: 'online',
        meetingLink: 'https://zoom.us/j/123456789'
      };

      await expect(AppointmentService.createAppointment(appointmentData))
        .rejects.toThrow('Student not found');
    });

    it('should throw error if start time is after end time', async () => {
      const nextMonday = getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(11, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(10, 0, 0, 0); // End before start

      const appointmentData: CreateAppointmentData = {
        tutorId: testTutorId,
        studentId: testStudentId,
        startTime,
        endTime,
        meetingType: 'online',
        meetingLink: 'https://zoom.us/j/123456789'
      };

      await expect(AppointmentService.createAppointment(appointmentData))
        .rejects.toThrow('Start time must be before end time');
    });

    it('should throw error if appointment is in the past', async () => {
      const pastTime = new Date();
      pastTime.setHours(pastTime.getHours() - 1);
      const pastEndTime = new Date();
      pastEndTime.setHours(pastEndTime.getHours() - 1);
      pastEndTime.setMinutes(pastEndTime.getMinutes() + 30);

      const appointmentData: CreateAppointmentData = {
        tutorId: testTutorId,
        studentId: testStudentId,
        startTime: pastTime,
        endTime: pastEndTime,
        meetingType: 'online',
        meetingLink: 'https://zoom.us/j/123456789'
      };

      await expect(AppointmentService.createAppointment(appointmentData))
        .rejects.toThrow('Appointment must be scheduled for future time');
    });

    it('should throw error if tutor has conflicting appointment', async () => {
      const nextMonday = getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      // Create first appointment
      const firstAppointmentData: CreateAppointmentData = {
        tutorId: testTutorId,
        studentId: testStudentId,
        startTime,
        endTime,
        meetingType: 'online',
        meetingLink: 'https://zoom.us/j/123456789'
      };

      await AppointmentService.createAppointment(firstAppointmentData);

      // Try to create overlapping appointment
      const overlappingStartTime = new Date(nextMonday);
      overlappingStartTime.setHours(10, 30, 0, 0);
      const overlappingEndTime = new Date(nextMonday);
      overlappingEndTime.setHours(11, 30, 0, 0);

      const overlappingAppointmentData: CreateAppointmentData = {
        tutorId: testTutorId,
        studentId: testStudentId,
        startTime: overlappingStartTime,
        endTime: overlappingEndTime,
        meetingType: 'online',
        meetingLink: 'https://zoom.us/j/123456789'
      };

      await expect(AppointmentService.createAppointment(overlappingAppointmentData))
        .rejects.toThrow('Tutor is not available at the requested time');
    });

    it('should throw error if appointment is outside tutor availability', async () => {
      // Try to book on Tuesday (tutor only available on Monday)
      const nextTuesday = getNextTuesday();
      const startTime = new Date(nextTuesday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextTuesday);
      endTime.setHours(11, 0, 0, 0);

      const appointmentData: CreateAppointmentData = {
        tutorId: testTutorId,
        studentId: testStudentId,
        startTime,
        endTime,
        meetingType: 'online',
        meetingLink: 'https://zoom.us/j/123456789'
      };

      await expect(AppointmentService.createAppointment(appointmentData))
        .rejects.toThrow('Appointment time is outside tutor\'s available hours');
    });

    it('should throw error if online meeting lacks meeting link', async () => {
      const nextMonday = getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      const appointmentData: CreateAppointmentData = {
        tutorId: testTutorId,
        studentId: testStudentId,
        startTime,
        endTime,
        meetingType: 'online'
        // Missing meetingLink
      };

      await expect(AppointmentService.createAppointment(appointmentData))
        .rejects.toThrow('Meeting link is required for online appointments');
    });
  });

  describe('getAppointmentById', () => {
    let appointmentId: string;

    beforeEach(async () => {
      const nextMonday = getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      const result = await AppointmentService.createAppointment({
        tutorId: testTutorId,
        studentId: testStudentId,
        startTime,
        endTime,
        meetingType: 'online',
        meetingLink: 'https://zoom.us/j/123456789'
      });
      appointmentId = result.appointment.id;
    });

    it('should return appointment with details', async () => {
      const appointment = await AppointmentService.getAppointmentById(appointmentId);

      expect(appointment).toBeDefined();
      expect(appointment!.id).toBe(appointmentId);
      expect(appointment!.tutor).toBeDefined();
      expect(appointment!.tutor.user).toBeDefined();
      expect(appointment!.student).toBeDefined();
      expect(appointment!.tutor.user.name).toBe('Test Tutor');
      expect(appointment!.student.name).toBe('Test Student');
    });

    it('should return null for non-existent appointment', async () => {
      const appointment = await AppointmentService.getAppointmentById('non-existent-id');
      expect(appointment).toBeNull();
    });
  });

  describe('getAvailableTimeSlots', () => {
    it('should return available time slots', async () => {
      const nextMonday = getNextMonday();

      const slots = await AppointmentService.getAvailableTimeSlots(testTutorId, nextMonday, 60);

      expect(slots).toBeDefined();
      expect(slots.length).toBeGreaterThan(0);
      expect(slots[0]).toHaveProperty('startTime');
      expect(slots[0]).toHaveProperty('endTime');

      // Should have slots from 9:00 to 16:00 (8 one-hour slots)
      expect(slots.length).toBe(8);
      expect(slots[0].startTime).toBe('09:00');
      expect(slots[0].endTime).toBe('10:00');
    });

    it('should return empty array for day with no availability', async () => {
      const nextTuesday = getNextTuesday();

      const slots = await AppointmentService.getAvailableTimeSlots(testTutorId, nextTuesday, 60);

      expect(slots).toEqual([]);
    });

    it('should exclude booked time slots', async () => {
      const nextMonday = getNextMonday();

      // First get available slots to ensure we have some
      const initialSlots = await AppointmentService.getAvailableTimeSlots(testTutorId, nextMonday, 60);
      expect(initialSlots.length).toBeGreaterThan(0);

      // Book 10-11 AM slot
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      await AppointmentService.createAppointment({
        tutorId: testTutorId,
        studentId: testStudentId,
        startTime,
        endTime,
        meetingType: 'online',
        meetingLink: 'https://zoom.us/j/123456789'
      });

      const slots = await AppointmentService.getAvailableTimeSlots(testTutorId, nextMonday, 60);

      // Should have one less slot than before (10-11 AM is booked)
      expect(slots.length).toBe(initialSlots.length - 1);
      expect(slots.some(slot => slot.startTime === '10:00')).toBe(false);
    });
  });
});

// Helper functions
function getNextMonday(): Date {
  const today = new Date();
  const nextMonday = new Date(today);
  nextMonday.setDate(today.getDate() + (1 + 7 - today.getDay()) % 7);
  if (nextMonday <= today) {
    nextMonday.setDate(nextMonday.getDate() + 7);
  }
  return nextMonday;
}

function getNextTuesday(): Date {
  const today = new Date();
  const nextTuesday = new Date(today);
  nextTuesday.setDate(today.getDate() + (2 + 7 - today.getDay()) % 7);
  if (nextTuesday <= today) {
    nextTuesday.setDate(nextTuesday.getDate() + 7);
  }
  return nextTuesday;
}
