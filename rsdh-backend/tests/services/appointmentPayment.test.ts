import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AppointmentService } from '../../src/services/appointmentService';
import { PaymentService } from '../../src/services/paymentService';
import { NotificationService } from '../../src/services/notificationService';
import { prisma } from '../../src/lib/prisma';
import { FastifyInstance } from 'fastify';

// Mock dependencies
vi.mock('../../src/lib/prisma', () => ({
  prisma: {
    tutorProfile: {
      findUnique: vi.fn(),
    },
    user: {
      findUnique: vi.fn(),
    },
    appointment: {
      create: vi.fn(),
      findFirst: vi.fn(),
    },
    tutorAvailability: {
      findFirst: vi.fn(),
    },
    $transaction: vi.fn(),
  },
}));

vi.mock('../../src/services/paymentService');
vi.mock('../../src/services/notificationService');

describe('AppointmentService - Payment Integration', () => {
  let mockApp: FastifyInstance;

  beforeEach(() => {
    mockApp = {
      config: {
        APP_URL: 'http://localhost:3000',
      },
      log: {
        info: vi.fn(),
        error: vi.fn(),
      },
    } as any;

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('createAppointment with payment integration', () => {
    const mockFreeTutor = {
      id: 'tutor-1',
      status: 'approved',
      isFree: true,
      hourlyRate: 0,
      halfHourRate: 0,
      currency: 'CNY',
      user: {
        id: 'tutor-user-1',
        email: '<EMAIL>',
        name: 'Free Tutor',
      },
    };

    const mockPaidTutor = {
      id: 'tutor-2',
      status: 'approved',
      isFree: false,
      hourlyRate: 100,
      halfHourRate: 50,
      currency: 'CNY',
      user: {
        id: 'tutor-user-2',
        email: '<EMAIL>',
        name: 'Paid Tutor',
      },
    };

    const mockStudent = {
      id: 'student-1',
      email: '<EMAIL>',
      name: 'Test Student',
    };

    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1); // Tomorrow
    const startTime = new Date(futureDate);
    startTime.setHours(10, 0, 0, 0);
    const endTime = new Date(futureDate);
    endTime.setHours(11, 0, 0, 0);

    const appointmentData = {
      tutorId: 'tutor-1',
      studentId: 'student-1',
      startTime,
      endTime,
      meetingType: 'online' as const,
      meetingLink: 'https://zoom.us/j/123456789',
      notes: 'Test appointment',
    };

    beforeEach(() => {
      (prisma.user.findUnique as any).mockResolvedValue(mockStudent);
      (prisma.appointment.findFirst as any).mockResolvedValue(null); // No conflicting appointments
      (prisma.tutorAvailability.findFirst as any).mockResolvedValue({
        id: 'availability-1',
        dayOfWeek: 1, // Monday
        startTime: '09:00',
        endTime: '18:00',
        isAvailable: true
      });
    });

    it('should create free appointment without payment', async () => {
      (prisma.tutorProfile.findUnique as any).mockResolvedValue(mockFreeTutor);
      (PaymentService.calculateAppointmentPrice as any).mockReturnValue(0);

      const mockAppointment = {
        id: 'appointment-1',
        ...appointmentData,
        duration: 60,
        price: 0,
        requiresPayment: false,
        paymentStatus: 'not_required',
        confirmationStatus: 'confirmed',
      };

      (prisma.$transaction as any).mockImplementation(async (callback) => {
        const mockTx = {
          appointment: {
            create: vi.fn().mockResolvedValue(mockAppointment),
          },
        };
        return callback(mockTx);
      });

      const mockNotificationService = {
        sendAppointmentNotification: vi.fn(),
      };
      (NotificationService as any).mockImplementation(() => mockNotificationService);

      const result = await AppointmentService.createAppointment(appointmentData, mockApp);

      expect(result).toEqual({
        appointment: mockAppointment,
        requiresPayment: false,
        paymentId: undefined,
        price: 0,
      });

      expect(PaymentService.createPayment).not.toHaveBeenCalled();
      expect(mockNotificationService.sendAppointmentNotification).toHaveBeenCalledWith({
        appointmentId: 'appointment-1',
        tutorId: 'tutor-1',
        studentId: 'student-1',
        appointmentDate: expect.any(String),
        appointmentTime: expect.any(String),
        duration: 60,
        meetingType: 'online',
        price: 0,
        isFree: true,
      });
    });

    it('should create paid appointment with payment record', async () => {
      const paidAppointmentData = { ...appointmentData, tutorId: 'tutor-2' };
      (prisma.tutorProfile.findUnique as any).mockResolvedValue(mockPaidTutor);
      (PaymentService.calculateAppointmentPrice as any).mockReturnValue(100);
      (PaymentService.createPayment as any).mockResolvedValue({
        id: 'payment-1',
      });

      const mockAppointment = {
        id: 'appointment-1',
        ...paidAppointmentData,
        duration: 60,
        price: 100,
        requiresPayment: true,
        paymentStatus: 'pending',
        confirmationStatus: 'pending',
      };

      (prisma.$transaction as any).mockImplementation(async (callback) => {
        const mockTx = {
          appointment: {
            create: vi.fn().mockResolvedValue(mockAppointment),
          },
        };
        await callback(mockTx);
        return { appointment: mockAppointment, paymentId: 'payment-1' };
      });

      const mockNotificationService = {
        sendAppointmentNotification: vi.fn(),
      };
      (NotificationService as any).mockImplementation(() => mockNotificationService);

      const result = await AppointmentService.createAppointment(paidAppointmentData, mockApp);

      expect(result).toEqual({
        appointment: mockAppointment,
        requiresPayment: true,
        paymentId: 'payment-1',
        price: 100,
      });

      expect(PaymentService.createPayment).toHaveBeenCalledWith({
        appointmentId: 'appointment-1',
        tutorId: 'tutor-2',
        studentId: 'student-1',
        amount: 100,
        currency: 'CNY',
      });

      expect(mockNotificationService.sendAppointmentNotification).toHaveBeenCalledWith({
        appointmentId: 'appointment-1',
        tutorId: 'tutor-2',
        studentId: 'student-1',
        appointmentDate: expect.any(String),
        appointmentTime: expect.any(String),
        duration: 60,
        meetingType: 'online',
        price: 100,
        isFree: false,
      });
    });

    it('should calculate correct price for half-hour appointment', async () => {
      const halfHourEndTime = new Date(startTime);
      halfHourEndTime.setMinutes(halfHourEndTime.getMinutes() + 30);

      const halfHourData = {
        ...appointmentData,
        tutorId: 'tutor-2',
        endTime: halfHourEndTime, // 30 minutes
      };

      (prisma.tutorProfile.findUnique as any).mockResolvedValue(mockPaidTutor);
      (PaymentService.calculateAppointmentPrice as any).mockReturnValue(50);

      const mockAppointment = {
        id: 'appointment-1',
        ...halfHourData,
        duration: 30,
        price: 50,
        requiresPayment: true,
      };

      (prisma.$transaction as any).mockImplementation(async (callback) => {
        const mockTx = {
          appointment: {
            create: vi.fn().mockResolvedValue(mockAppointment),
          },
        };
        return { appointment: mockAppointment, paymentId: 'payment-1' };
      });

      const result = await AppointmentService.createAppointment(halfHourData, mockApp);

      expect(PaymentService.calculateAppointmentPrice).toHaveBeenCalledWith(mockPaidTutor, 30);
      expect(result.price).toBe(50);
    });

    it('should handle different currencies', async () => {
      const usdTutor = { ...mockPaidTutor, currency: 'USD' };
      (prisma.tutorProfile.findUnique as any).mockResolvedValue(usdTutor);
      (PaymentService.calculateAppointmentPrice as any).mockReturnValue(15);
      (PaymentService.createPayment as any).mockResolvedValue({
        id: 'payment-1',
      });

      const mockAppointment = {
        id: 'appointment-1',
        ...appointmentData,
        duration: 60,
        price: 15,
        currency: 'USD',
        requiresPayment: true,
      };

      (prisma.$transaction as any).mockImplementation(async (callback) => {
        const mockTx = {
          appointment: {
            create: vi.fn().mockResolvedValue(mockAppointment),
          },
        };
        await callback(mockTx);
        return { appointment: mockAppointment, paymentId: 'payment-1' };
      });

      const result = await AppointmentService.createAppointment(appointmentData, mockApp);

      expect(PaymentService.createPayment).toHaveBeenCalledWith({
        appointmentId: 'appointment-1',
        tutorId: 'tutor-1',
        studentId: 'student-1',
        amount: 15,
        currency: 'USD',
      });
    });

    it('should set correct appointment statuses based on payment requirement', async () => {
      // Test free appointment
      (prisma.tutorProfile.findUnique as any).mockResolvedValue(mockFreeTutor);
      (PaymentService.calculateAppointmentPrice as any).mockReturnValue(0);

      (prisma.$transaction as any).mockImplementation(async (callback) => {
        const mockTx = {
          appointment: {
            create: vi.fn().mockImplementation((data) => {
              expect(data.data.paymentStatus).toBe('not_required');
              expect(data.data.confirmationStatus).toBe('confirmed');
              return Promise.resolve({ id: 'appointment-1', ...data.data });
            }),
          },
        };
        return callback(mockTx);
      });

      await AppointmentService.createAppointment(appointmentData, mockApp);

      // Test paid appointment
      (prisma.tutorProfile.findUnique as any).mockResolvedValue(mockPaidTutor);
      (PaymentService.calculateAppointmentPrice as any).mockReturnValue(100);

      (prisma.$transaction as any).mockImplementation(async (callback) => {
        const mockTx = {
          appointment: {
            create: vi.fn().mockImplementation((data) => {
              expect(data.data.paymentStatus).toBe('pending');
              expect(data.data.confirmationStatus).toBe('pending');
              return Promise.resolve({ id: 'appointment-2', ...data.data });
            }),
          },
        };
        return { appointment: { id: 'appointment-2' }, paymentId: 'payment-1' };
      });

      await AppointmentService.createAppointment(appointmentData, mockApp);
    });
  });
});
