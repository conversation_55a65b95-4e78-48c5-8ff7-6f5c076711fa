import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SMSService } from '../../src/services/smsService';

describe('SMSService', () => {
  let mockApp: any;
  let smsService: SMSService;

  beforeEach(() => {
    mockApp = {
      config: {
        NODE_ENV: 'test',
        SMS_PROVIDER: 'mock',
        SMS_ACCESS_KEY_ID: 'test-key-id',
        SMS_ACCESS_KEY_SECRET: 'test-key-secret',
        SMS_SIGN_NAME: '测试签名',
        SMS_TEMPLATE_CODE: 'SMS_TEST_123456',
      },
      log: {
        info: vi.fn(),
        error: vi.fn(),
      },
    };
    smsService = new SMSService(mockApp);
  });

  describe('sendVerificationCode', () => {
    it('should send SMS successfully with mock provider', async () => {
      const result = await smsService.sendVerificationCode({
        phoneNumber: '+8613800138000',
        code: '123456',
      });

      expect(result).toBe(true);
      expect(mockApp.log.info).toHaveBeenCalledWith(
        '[Mock SMS] Sending code 123456 to +8613800138000'
      );
    });

    it('should handle SMS sending failure', async () => {
      // Mock a failure scenario
      const failingApp = {
        ...mockApp,
        config: {
          ...mockApp.config,
          SMS_PROVIDER: 'aliyun',
          NODE_ENV: 'production',
        },
      };
      const failingSmsService = new SMSService(failingApp);

      const result = await failingSmsService.sendVerificationCode({
        phoneNumber: '+8613800138000',
        code: '123456',
      });

      expect(result).toBe(false);
      expect(mockApp.log.error).toHaveBeenCalled();
    });

    it('should log code in development mode', async () => {
      const devApp = {
        ...mockApp,
        config: {
          ...mockApp.config,
          NODE_ENV: 'development',
        },
      };
      const devSmsService = new SMSService(devApp);

      // Mock console.log
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      await devSmsService.sendVerificationCode({
        phoneNumber: '+8613800138000',
        code: '123456',
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        '📱 SMS Verification Code for +8613800138000: 123456'
      );

      consoleSpy.mockRestore();
    });
  });

  describe('validatePhoneNumber', () => {
    it('should validate Chinese mobile numbers correctly', () => {
      // Valid formats
      expect(SMSService.validatePhoneNumber('13800138000')).toBe(true);
      expect(SMSService.validatePhoneNumber('+8613800138000')).toBe(true);
      expect(SMSService.validatePhoneNumber('8613800138000')).toBe(true);
      expect(SMSService.validatePhoneNumber('15912345678')).toBe(true);
      expect(SMSService.validatePhoneNumber('18888888888')).toBe(true);

      // Invalid formats
      expect(SMSService.validatePhoneNumber('12800138000')).toBe(false); // Invalid prefix
      expect(SMSService.validatePhoneNumber('1380013800')).toBe(false); // Too short
      expect(SMSService.validatePhoneNumber('138001380000')).toBe(false); // Too long
      expect(SMSService.validatePhoneNumber('abc13800138000')).toBe(false); // Contains letters
      expect(SMSService.validatePhoneNumber('')).toBe(false); // Empty
      expect(SMSService.validatePhoneNumber('**********')).toBe(false); // Wrong format
    });
  });

  describe('normalizePhoneNumber', () => {
    it('should normalize phone numbers to standard format', () => {
      expect(SMSService.normalizePhoneNumber('13800138000')).toBe('+8613800138000');
      expect(SMSService.normalizePhoneNumber('+8613800138000')).toBe('+8613800138000');
      expect(SMSService.normalizePhoneNumber('8613800138000')).toBe('+8613800138000');
      expect(SMSService.normalizePhoneNumber('138-0013-8000')).toBe('+8613800138000');
      expect(SMSService.normalizePhoneNumber('138 0013 8000')).toBe('+8613800138000');
      expect(SMSService.normalizePhoneNumber('(138) 0013-8000')).toBe('+8613800138000');
    });

    it('should handle edge cases', () => {
      expect(SMSService.normalizePhoneNumber('**********')).toBe('**********'); // Invalid format, return as-is
      expect(SMSService.normalizePhoneNumber('+**********')).toBe('+**********'); // Non-Chinese format
      expect(SMSService.normalizePhoneNumber('')).toBe(''); // Empty string
    });
  });

  describe('provider-specific methods', () => {
    it('should handle Aliyun SMS in development', async () => {
      const aliyunApp = {
        ...mockApp,
        config: {
          ...mockApp.config,
          SMS_PROVIDER: 'aliyun',
          NODE_ENV: 'development',
        },
      };
      const aliyunSmsService = new SMSService(aliyunApp);

      const result = await aliyunSmsService.sendVerificationCode({
        phoneNumber: '+8613800138000',
        code: '123456',
      });

      expect(result).toBe(true);
      expect(mockApp.log.info).toHaveBeenCalledWith(
        '[Aliyun SMS] Would send code 123456 to +8613800138000'
      );
    });

    it('should handle Tencent SMS in development', async () => {
      const tencentApp = {
        ...mockApp,
        config: {
          ...mockApp.config,
          SMS_PROVIDER: 'tencent',
          NODE_ENV: 'development',
        },
      };
      const tencentSmsService = new SMSService(tencentApp);

      const result = await tencentSmsService.sendVerificationCode({
        phoneNumber: '+8613800138000',
        code: '123456',
      });

      expect(result).toBe(true);
      expect(mockApp.log.info).toHaveBeenCalledWith(
        '[Tencent SMS] Would send code 123456 to +8613800138000'
      );
    });
  });

  describe('error handling', () => {
    it('should handle network delays gracefully', async () => {
      const start = Date.now();
      await smsService.sendVerificationCode({
        phoneNumber: '+8613800138000',
        code: '123456',
      });
      const end = Date.now();

      // Should complete within reasonable time (mock has 100ms delay)
      expect(end - start).toBeGreaterThanOrEqual(100);
      expect(end - start).toBeLessThan(1000);
    });

    it('should handle invalid configuration gracefully', async () => {
      const invalidApp = {
        ...mockApp,
        config: {
          ...mockApp.config,
          SMS_PROVIDER: 'invalid-provider' as any,
        },
      };
      const invalidSmsService = new SMSService(invalidApp);

      const result = await invalidSmsService.sendVerificationCode({
        phoneNumber: '+8613800138000',
        code: '123456',
      });

      // Should fall back to mock provider
      expect(result).toBe(true);
    });
  });
});
