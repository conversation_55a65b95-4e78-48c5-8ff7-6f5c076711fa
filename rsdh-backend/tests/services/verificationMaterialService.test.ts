import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { prisma } from '../../src/lib/prisma';
import { VerificationMaterialService } from '../../src/services/verificationMaterialService';
import { UserService } from '../../src/services/userService';
import { TutorService } from '../../src/services/tutorService';

describe('VerificationMaterialService', () => {
  let testUser: any;
  let testTutor: any;
  let testEducation: any;
  let testCareer: any;
  let testFile: any;

  beforeEach(async () => {
    // Create test user
    testUser = await UserService.createUser({
      email: '<EMAIL>',
      name: 'Test Tutor',
      password: 'password123'
    });

    // Create test tutor profile with education and career
    testTutor = await TutorService.applyToBecomeTutor({
      userId: testUser.id,
      title: 'Test Tutor',
      bio: 'Test bio',
      rate: 100,
      education: [{
        degree: 'Bachelor',
        fieldOfStudy: 'Computer Science',
        institution: 'Test University',
        startYear: 2018,
        endYear: 2022
      }],
      career: [{
        title: 'Software Engineer',
        company: 'Test Company',
        startYear: 2022,
        current: true
      }]
    });

    // Get the created education and career records
    const tutorProfile = await prisma.tutorProfile.findUnique({
      where: { id: testTutor.id },
      include: {
        education: true,
        career: true
      }
    });

    testEducation = tutorProfile!.education[0];
    testCareer = tutorProfile!.career[0];

    // Create test file
    testFile = await prisma.file.create({
      data: {
        originalName: 'test-diploma.pdf',
        fileName: 'test-diploma-123.pdf',
        mimeType: 'application/pdf',
        size: 1024,
        s3Key: 'verification/test-diploma-123.pdf',
        s3Bucket: 'test-bucket',
        s3Region: 'us-east-1',
        uploadedById: testUser.id,
        category: 'verification'
      }
    });
  });

  afterEach(async () => {
    // Clean up test data
    await prisma.educationVerification.deleteMany({});
    await prisma.careerVerification.deleteMany({});
    await prisma.file.deleteMany({});
    await prisma.tutorEducation.deleteMany({});
    await prisma.tutorCareer.deleteMany({});
    await prisma.tutorProfile.deleteMany({});
    await prisma.user.deleteMany({});
  });

  describe('addEducationVerification', () => {
    it('should add education verification material successfully', async () => {
      const verificationData = {
        educationId: testEducation.id,
        fileId: testFile.id,
        materialType: 'diploma',
        description: 'University diploma'
      };

      const verification = await VerificationMaterialService.addEducationVerification(verificationData);

      expect(verification).toBeDefined();
      expect(verification.educationId).toBe(testEducation.id);
      expect(verification.fileId).toBe(testFile.id);
      expect(verification.materialType).toBe('diploma');
      expect(verification.description).toBe('University diploma');
      expect(verification.status).toBe('pending');
    });

    it('should throw error for non-existent education record', async () => {
      const verificationData = {
        educationId: 'non-existent-id',
        fileId: testFile.id,
        materialType: 'diploma'
      };

      await expect(
        VerificationMaterialService.addEducationVerification(verificationData)
      ).rejects.toThrow('Education record not found');
    });

    it('should throw error for non-existent file', async () => {
      const verificationData = {
        educationId: testEducation.id,
        fileId: 'non-existent-file-id',
        materialType: 'diploma'
      };

      await expect(
        VerificationMaterialService.addEducationVerification(verificationData)
      ).rejects.toThrow('File not found');
    });
  });

  describe('addCareerVerification', () => {
    it('should add career verification material successfully', async () => {
      const verificationData = {
        careerId: testCareer.id,
        fileId: testFile.id,
        materialType: 'work_certificate',
        description: 'Employment certificate'
      };

      const verification = await VerificationMaterialService.addCareerVerification(verificationData);

      expect(verification).toBeDefined();
      expect(verification.careerId).toBe(testCareer.id);
      expect(verification.fileId).toBe(testFile.id);
      expect(verification.materialType).toBe('work_certificate');
      expect(verification.description).toBe('Employment certificate');
      expect(verification.status).toBe('pending');
    });

    it('should throw error for non-existent career record', async () => {
      const verificationData = {
        careerId: 'non-existent-id',
        fileId: testFile.id,
        materialType: 'work_certificate'
      };

      await expect(
        VerificationMaterialService.addCareerVerification(verificationData)
      ).rejects.toThrow('Career record not found');
    });
  });

  describe('getEducationVerifications', () => {
    it('should return education verification materials', async () => {
      // Add verification material
      await VerificationMaterialService.addEducationVerification({
        educationId: testEducation.id,
        fileId: testFile.id,
        materialType: 'diploma'
      });

      const verifications = await VerificationMaterialService.getEducationVerifications(testEducation.id);

      expect(verifications).toHaveLength(1);
      expect(verifications[0].educationId).toBe(testEducation.id);
      expect(verifications[0].file).toBeDefined();
    });

    it('should return empty array for education with no verifications', async () => {
      const verifications = await VerificationMaterialService.getEducationVerifications(testEducation.id);

      expect(verifications).toHaveLength(0);
    });
  });

  describe('getCareerVerifications', () => {
    it('should return career verification materials', async () => {
      // Add verification material
      await VerificationMaterialService.addCareerVerification({
        careerId: testCareer.id,
        fileId: testFile.id,
        materialType: 'work_certificate'
      });

      const verifications = await VerificationMaterialService.getCareerVerifications(testCareer.id);

      expect(verifications).toHaveLength(1);
      expect(verifications[0].careerId).toBe(testCareer.id);
      expect(verifications[0].file).toBeDefined();
    });
  });

  describe('getTutorVerifications', () => {
    it('should return all verification materials for a tutor', async () => {
      // Add education verification
      await VerificationMaterialService.addEducationVerification({
        educationId: testEducation.id,
        fileId: testFile.id,
        materialType: 'diploma'
      });

      // Create another file for career verification
      const careerFile = await prisma.file.create({
        data: {
          originalName: 'work-cert.pdf',
          fileName: 'work-cert-123.pdf',
          mimeType: 'application/pdf',
          size: 512,
          s3Key: 'verification/work-cert-123.pdf',
          s3Bucket: 'test-bucket',
          s3Region: 'us-east-1',
          uploadedById: testUser.id,
          category: 'verification'
        }
      });

      // Add career verification
      await VerificationMaterialService.addCareerVerification({
        careerId: testCareer.id,
        fileId: careerFile.id,
        materialType: 'work_certificate'
      });

      const verifications = await VerificationMaterialService.getTutorVerifications(testTutor.id);

      expect(verifications.educationVerifications).toHaveLength(1);
      expect(verifications.careerVerifications).toHaveLength(1);
    });
  });

  describe('updateEducationVerificationStatus', () => {
    it('should update education verification status successfully', async () => {
      // Create admin user
      const adminUser = await UserService.createUser({
        email: '<EMAIL>',
        name: 'Admin User',
        password: 'password123',
        role: 'admin'
      });

      // Add verification material
      const verification = await VerificationMaterialService.addEducationVerification({
        educationId: testEducation.id,
        fileId: testFile.id,
        materialType: 'diploma'
      });

      // Update status
      const updatedVerification = await VerificationMaterialService.updateEducationVerificationStatus(
        verification.id,
        {
          status: 'approved',
          reviewNotes: 'Looks good',
          reviewedById: adminUser.id
        }
      );

      expect(updatedVerification.status).toBe('approved');
      expect(updatedVerification.reviewNotes).toBe('Looks good');
      expect(updatedVerification.reviewedById).toBe(adminUser.id);
      expect(updatedVerification.reviewedAt).toBeDefined();
    });

    it('should throw error for non-existent verification', async () => {
      const adminUser = await UserService.createUser({
        email: '<EMAIL>',
        name: 'Admin User',
        password: 'password123',
        role: 'admin'
      });

      await expect(
        VerificationMaterialService.updateEducationVerificationStatus(
          'non-existent-id',
          {
            status: 'approved',
            reviewedById: adminUser.id
          }
        )
      ).rejects.toThrow('Education verification not found');
    });
  });

  describe('deleteEducationVerification', () => {
    it('should delete education verification successfully', async () => {
      // Add verification material
      const verification = await VerificationMaterialService.addEducationVerification({
        educationId: testEducation.id,
        fileId: testFile.id,
        materialType: 'diploma'
      });

      // Delete verification
      await VerificationMaterialService.deleteEducationVerification(verification.id, testUser.id);

      // Verify deletion
      const verifications = await VerificationMaterialService.getEducationVerifications(testEducation.id);
      expect(verifications).toHaveLength(0);
    });

    it('should throw error when unauthorized user tries to delete', async () => {
      // Create another user
      const otherUser = await UserService.createUser({
        email: '<EMAIL>',
        name: 'Other User',
        password: 'password123'
      });

      // Add verification material
      const verification = await VerificationMaterialService.addEducationVerification({
        educationId: testEducation.id,
        fileId: testFile.id,
        materialType: 'diploma'
      });

      // Try to delete with wrong user
      await expect(
        VerificationMaterialService.deleteEducationVerification(verification.id, otherUser.id)
      ).rejects.toThrow('Unauthorized to delete this verification');
    });
  });

  describe('getPendingVerifications', () => {
    it('should return pending verifications for admin review', async () => {
      // Add education verification
      await VerificationMaterialService.addEducationVerification({
        educationId: testEducation.id,
        fileId: testFile.id,
        materialType: 'diploma'
      });

      // Create another file for career verification
      const careerFile = await prisma.file.create({
        data: {
          originalName: 'work-cert.pdf',
          fileName: 'work-cert-123.pdf',
          mimeType: 'application/pdf',
          size: 512,
          s3Key: 'verification/work-cert-123.pdf',
          s3Bucket: 'test-bucket',
          s3Region: 'us-east-1',
          uploadedById: testUser.id,
          category: 'verification'
        }
      });

      // Add career verification
      await VerificationMaterialService.addCareerVerification({
        careerId: testCareer.id,
        fileId: careerFile.id,
        materialType: 'work_certificate'
      });

      const pendingVerifications = await VerificationMaterialService.getPendingVerifications();

      expect(pendingVerifications.educationVerifications).toHaveLength(1);
      expect(pendingVerifications.careerVerifications).toHaveLength(1);
      expect(pendingVerifications.educationVerifications[0].status).toBe('pending');
      expect(pendingVerifications.careerVerifications[0].status).toBe('pending');
    });
  });
});
