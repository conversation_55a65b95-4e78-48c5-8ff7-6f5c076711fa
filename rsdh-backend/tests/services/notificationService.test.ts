import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NotificationService } from '../../src/services/notificationService';
import { EmailService } from '../../src/services/emailService';
import { prisma } from '../../src/lib/prisma';
import { FastifyInstance } from 'fastify';

// Mock dependencies
vi.mock('../../src/lib/prisma', () => ({
  prisma: {
    appointment: {
      findUnique: vi.fn(),
      update: vi.fn(),
      updateMany: vi.fn(),
    },
  },
}));

vi.mock('../../src/services/emailService');

describe('NotificationService', () => {
  let mockApp: FastifyInstance;
  let notificationService: NotificationService;
  let mockEmailService: any;

  beforeEach(() => {
    mockApp = {
      config: {
        APP_URL: 'http://localhost:3000',
      },
      log: {
        info: vi.fn(),
        error: vi.fn(),
      },
    } as any;

    mockEmailService = {
      sendAppointmentNotification: vi.fn(),
      sendAppointmentConfirmation: vi.fn(),
    };

    (EmailService as any).mockImplementation(() => mockEmailService);
    notificationService = new NotificationService(mockApp);

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('sendAppointmentNotification', () => {
    it('should send free appointment notification', async () => {
      const mockAppointment = {
        id: 'appointment-1',
        tutor: {
          user: {
            email: '<EMAIL>',
            name: 'Test Tutor',
          },
        },
        student: {
          name: 'Test Student',
        },
      };

      (prisma.appointment.findUnique as any).mockResolvedValue(mockAppointment);

      await notificationService.sendAppointmentNotification({
        appointmentId: 'appointment-1',
        tutorId: 'tutor-1',
        studentId: 'student-1',
        appointmentDate: '2024-01-01',
        appointmentTime: '10:00',
        duration: 60,
        meetingType: 'online',
        price: 0,
        isFree: true,
      });

      expect(mockEmailService.sendAppointmentNotification).toHaveBeenCalledWith({
        tutorEmail: '<EMAIL>',
        tutorName: 'Test Tutor',
        studentName: 'Test Student',
        appointmentDate: '2024-01-01',
        appointmentTime: '10:00',
        duration: 60,
        meetingType: 'online',
        price: 0,
        isFree: true,
        confirmationUrl: undefined,
      });
    });

    it('should send paid appointment notification with confirmation URL', async () => {
      const mockAppointment = {
        id: 'appointment-1',
        tutor: {
          user: {
            email: '<EMAIL>',
            name: 'Test Tutor',
          },
        },
        student: {
          name: 'Test Student',
        },
      };

      (prisma.appointment.findUnique as any).mockResolvedValue(mockAppointment);
      (prisma.appointment.update as any).mockResolvedValue({});

      await notificationService.sendAppointmentNotification({
        appointmentId: 'appointment-1',
        tutorId: 'tutor-1',
        studentId: 'student-1',
        appointmentDate: '2024-01-01',
        appointmentTime: '10:00',
        duration: 60,
        meetingType: 'online',
        price: 100,
        isFree: false,
      });

      expect(prisma.appointment.update).toHaveBeenCalledWith({
        where: { id: 'appointment-1' },
        data: {
          confirmationToken: expect.any(String),
          confirmationExpiresAt: expect.any(Date),
        },
      });

      expect(mockEmailService.sendAppointmentNotification).toHaveBeenCalledWith({
        tutorEmail: '<EMAIL>',
        tutorName: 'Test Tutor',
        studentName: 'Test Student',
        appointmentDate: '2024-01-01',
        appointmentTime: '10:00',
        duration: 60,
        meetingType: 'online',
        price: 100,
        isFree: false,
        confirmationUrl: expect.stringContaining('http://localhost:3000/api/appointments/appointment-1/confirm?token='),
      });
    });

    it('should throw error when appointment not found', async () => {
      (prisma.appointment.findUnique as any).mockResolvedValue(null);

      await expect(
        notificationService.sendAppointmentNotification({
          appointmentId: 'invalid-appointment',
          tutorId: 'tutor-1',
          studentId: 'student-1',
          appointmentDate: '2024-01-01',
          appointmentTime: '10:00',
          duration: 60,
          meetingType: 'online',
          price: 100,
          isFree: false,
        })
      ).rejects.toThrow('Appointment not found');
    });
  });

  describe('sendAppointmentConfirmation', () => {
    it('should send confirmation email to student', async () => {
      const mockAppointment = {
        id: 'appointment-1',
        startTime: new Date('2024-01-01T10:00:00Z'),
        tutor: {
          user: {
            name: 'Test Tutor',
          },
        },
        student: {
          email: '<EMAIL>',
          name: 'Test Student',
        },
      };

      (prisma.appointment.findUnique as any).mockResolvedValue(mockAppointment);

      await notificationService.sendAppointmentConfirmation('appointment-1', 'confirmed');

      expect(mockEmailService.sendAppointmentConfirmation).toHaveBeenCalledWith({
        studentEmail: '<EMAIL>',
        studentName: 'Test Student',
        tutorName: 'Test Tutor',
        appointmentDate: expect.any(String),
        appointmentTime: expect.any(String),
        status: 'confirmed',
      });
    });
  });

  describe('confirmAppointment', () => {
    it('should confirm appointment with valid token', async () => {
      const mockAppointment = {
        id: 'appointment-1',
        confirmationToken: 'valid-token',
        confirmationExpiresAt: new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
        tutor: { user: { name: 'Test Tutor' } },
        student: { name: 'Test Student' },
      };

      (prisma.appointment.findUnique as any).mockResolvedValue(mockAppointment);
      (prisma.appointment.update as any).mockResolvedValue({});

      const result = await NotificationService.confirmAppointment('appointment-1', 'valid-token');

      expect(result).toBe(true);
      expect(prisma.appointment.update).toHaveBeenCalledWith({
        where: { id: 'appointment-1' },
        data: {
          confirmationStatus: 'confirmed',
          confirmationToken: null,
          confirmationExpiresAt: null,
        },
      });
    });

    it('should throw error for invalid token', async () => {
      const mockAppointment = {
        id: 'appointment-1',
        confirmationToken: 'valid-token',
        confirmationExpiresAt: new Date(Date.now() + 60 * 60 * 1000),
      };

      (prisma.appointment.findUnique as any).mockResolvedValue(mockAppointment);

      await expect(
        NotificationService.confirmAppointment('appointment-1', 'invalid-token')
      ).rejects.toThrow('Invalid confirmation token');
    });

    it('should throw error for expired token', async () => {
      const mockAppointment = {
        id: 'appointment-1',
        confirmationToken: 'valid-token',
        confirmationExpiresAt: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
      };

      (prisma.appointment.findUnique as any).mockResolvedValue(mockAppointment);

      await expect(
        NotificationService.confirmAppointment('appointment-1', 'valid-token')
      ).rejects.toThrow('Confirmation token has expired');
    });
  });

  describe('rejectAppointment', () => {
    it('should reject appointment with valid token', async () => {
      const mockAppointment = {
        id: 'appointment-1',
        confirmationToken: 'valid-token',
        confirmationExpiresAt: new Date(Date.now() + 60 * 60 * 1000),
      };

      (prisma.appointment.findUnique as any).mockResolvedValue(mockAppointment);
      (prisma.appointment.update as any).mockResolvedValue({});

      const result = await NotificationService.rejectAppointment('appointment-1', 'valid-token');

      expect(result).toBe(true);
      expect(prisma.appointment.update).toHaveBeenCalledWith({
        where: { id: 'appointment-1' },
        data: {
          confirmationStatus: 'rejected',
          status: 'cancelled',
          confirmationToken: null,
          confirmationExpiresAt: null,
        },
      });
    });
  });

  describe('cleanupExpiredTokens', () => {
    it('should clean up expired confirmation tokens', async () => {
      (prisma.appointment.updateMany as any).mockResolvedValue({ count: 2 });

      await NotificationService.cleanupExpiredTokens();

      expect(prisma.appointment.updateMany).toHaveBeenCalledWith({
        where: {
          confirmationExpiresAt: {
            lt: expect.any(Date),
          },
          confirmationStatus: 'pending',
        },
        data: {
          confirmationStatus: 'rejected',
          status: 'cancelled',
          confirmationToken: null,
          confirmationExpiresAt: null,
        },
      });
    });
  });
});
