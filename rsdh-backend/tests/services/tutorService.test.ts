import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { prisma } from '../../src/lib/prisma';
import { TutorService, CreateTutorApplicationData } from '../../src/services/tutorService';

describe('TutorService', () => {
  let testUserId: string;
  let testTutorId: string;

  beforeEach(async () => {
    // Create a test user
    const testUser = await prisma.user.create({
      data: {
        email: `tutor-test-${Date.now()}@example.com`,
        name: 'Test Tutor User',
        password: 'hashedpassword',
        role: 'user'
      }
    });
    testUserId = testUser.id;
  });

  afterEach(async () => {
    // Clean up test data in correct order
    try {
      if (testTutorId) {
        // Delete related records first
        await prisma.tutorEducation.deleteMany({
          where: { tutorId: testTutorId }
        });
        await prisma.tutorCareer.deleteMany({
          where: { tutorId: testTutorId }
        });
        await prisma.tutorProfile.deleteMany({
          where: { id: testTutorId }
        });
      }

      await prisma.user.deleteMany({
        where: { id: testUserId }
      });
    } catch (error) {
      // Ignore cleanup errors in tests
    }
  });

  describe('applyToBecomeTutor', () => {
    it('should create a tutor profile with education and career', async () => {
      const applicationData: CreateTutorApplicationData = {
        userId: testUserId,
        title: 'Senior Software Engineer',
        bio: 'Experienced software engineer with 10+ years in the industry',
        hourlyRate: 100, // 100 yuan (will be converted to fen by service)
        halfHourRate: 50, // 50 yuan (will be converted to fen by service)
        education: [
          {
            degree: 'Bachelor of Science',
            fieldOfStudy: 'Computer Science',
            institution: 'MIT',
            startYear: 2010,
            endYear: 2014,
            description: 'Focused on algorithms and data structures'
          }
        ],
        career: [
          {
            title: 'Senior Software Engineer',
            company: 'Tech Corp',
            startYear: 2018,
            current: true,
            description: 'Leading development of web applications'
          }
        ]
      };

      const tutorProfile = await TutorService.applyToBecomeTutor(applicationData);
      testTutorId = tutorProfile.id;

      expect(tutorProfile).toBeDefined();
      expect(tutorProfile.userId).toBe(testUserId);
      expect(tutorProfile.title).toBe('Senior Software Engineer');
      expect(tutorProfile.bio).toBe('Experienced software engineer with 10+ years in the industry');
      expect(tutorProfile.hourlyRate).toBe(BigInt(10000)); // 100 yuan in fen
      expect(tutorProfile.status).toBe('pending');

      // Verify education and career were created
      const fullProfile = await TutorService.getTutorById(tutorProfile.id);
      expect(fullProfile).toBeDefined();
      expect(fullProfile!.education).toHaveLength(1);
      expect(fullProfile!.career).toHaveLength(1);
      expect(fullProfile!.education[0].degree).toBe('Bachelor of Science');
      expect(fullProfile!.career[0].title).toBe('Senior Software Engineer');
    });

    it('should throw error if user already has a tutor profile', async () => {
      const applicationData: CreateTutorApplicationData = {
        userId: testUserId,
        title: 'Test Title',
        education: [
          {
            degree: 'Bachelor',
            fieldOfStudy: 'CS',
            institution: 'University',
            startYear: 2020
          }
        ],
        career: [
          {
            title: 'Developer',
            company: 'Company',
            startYear: 2022,
            current: true
          }
        ]
      };

      // Create first profile
      const firstProfile = await TutorService.applyToBecomeTutor(applicationData);
      testTutorId = firstProfile.id;

      // Try to create second profile
      await expect(TutorService.applyToBecomeTutor(applicationData))
        .rejects.toThrow('User already has a tutor profile');
    });

    it('should throw error if user does not exist', async () => {
      const applicationData: CreateTutorApplicationData = {
        userId: 'non-existent-user-id',
        title: 'Test Title',
        education: [
          {
            degree: 'Bachelor',
            fieldOfStudy: 'CS',
            institution: 'University',
            startYear: 2020
          }
        ],
        career: [
          {
            title: 'Developer',
            company: 'Company',
            startYear: 2022,
            current: true
          }
        ]
      };

      await expect(TutorService.applyToBecomeTutor(applicationData))
        .rejects.toThrow('User not found');
    });
  });

  describe('getTutorByUserId', () => {
    beforeEach(async () => {
      const applicationData: CreateTutorApplicationData = {
        userId: testUserId,
        title: 'Test Tutor',
        bio: 'Test bio',
        rate: 50,
        education: [
          {
            degree: 'Master',
            fieldOfStudy: 'Education',
            institution: 'University',
            startYear: 2015,
            endYear: 2017
          }
        ],
        career: [
          {
            title: 'Teacher',
            company: 'School',
            startYear: 2017,
            current: true
          }
        ]
      };

      const profile = await TutorService.applyToBecomeTutor(applicationData);
      testTutorId = profile.id;
    });

    it('should return tutor profile with all related data', async () => {
      const tutor = await TutorService.getTutorByUserId(testUserId);

      expect(tutor).toBeDefined();
      expect(tutor!.id).toBe(testTutorId);
      expect(tutor!.userId).toBe(testUserId);
      expect(tutor!.title).toBe('Test Tutor');
      expect(tutor!.user).toBeDefined();
      expect(tutor!.user.email).toContain('tutor-test-');
      expect(tutor!.education).toHaveLength(1);
      expect(tutor!.career).toHaveLength(1);
      expect(tutor!.averageRating).toBe(0); // No reviews yet
      expect(tutor!._count.reviews).toBe(0);
      expect(tutor!._count.appointments).toBe(0);
    });

    it('should return null if tutor profile does not exist', async () => {
      // Create another user without tutor profile
      const anotherUser = await prisma.user.create({
        data: {
          email: `no-tutor-${Date.now()}@example.com`,
          name: 'No Tutor User',
          password: 'hashedpassword'
        }
      });

      const tutor = await TutorService.getTutorByUserId(anotherUser.id);
      expect(tutor).toBeNull();

      // Clean up
      await prisma.user.delete({ where: { id: anotherUser.id } });
    });
  });

  describe('updateTutorProfile', () => {
    beforeEach(async () => {
      const applicationData: CreateTutorApplicationData = {
        userId: testUserId,
        title: 'Original Title',
        bio: 'Original bio',
        rate: 75,
        education: [
          {
            degree: 'Bachelor',
            fieldOfStudy: 'CS',
            institution: 'University',
            startYear: 2020
          }
        ],
        career: [
          {
            title: 'Developer',
            company: 'Company',
            startYear: 2022,
            current: true
          }
        ]
      };

      const profile = await TutorService.applyToBecomeTutor(applicationData);
      testTutorId = profile.id;
    });

    it('should update tutor profile successfully', async () => {
      const updateData = {
        title: 'Updated Title',
        bio: 'Updated bio',
        hourlyRate: 100, // 100 yuan (will be converted to fen by service)
        halfHourRate: 50 // 50 yuan (will be converted to fen by service)
      };

      const updatedProfile = await TutorService.updateTutorProfile(testTutorId, updateData);

      expect(updatedProfile.title).toBe('Updated Title');
      expect(updatedProfile.bio).toBe('Updated bio');
      expect(updatedProfile.hourlyRate).toBe(BigInt(10000)); // 100 yuan in fen
      expect(updatedProfile.id).toBe(testTutorId);
    });

    it('should throw error if tutor profile does not exist', async () => {
      const updateData = {
        title: 'Updated Title'
      };

      await expect(TutorService.updateTutorProfile('non-existent-id', updateData))
        .rejects.toThrow('Tutor profile not found');
    });
  });

  describe('updateTutorStatus', () => {
    beforeEach(async () => {
      const applicationData: CreateTutorApplicationData = {
        userId: testUserId,
        title: 'Test Tutor',
        education: [
          {
            degree: 'Bachelor',
            fieldOfStudy: 'CS',
            institution: 'University',
            startYear: 2020
          }
        ],
        career: [
          {
            title: 'Developer',
            company: 'Company',
            startYear: 2022,
            current: true
          }
        ]
      };

      const profile = await TutorService.applyToBecomeTutor(applicationData);
      testTutorId = profile.id;
    });

    it('should approve tutor application', async () => {
      const updatedProfile = await TutorService.updateTutorStatus(testTutorId, 'approved');

      expect(updatedProfile.status).toBe('approved');
      expect(updatedProfile.id).toBe(testTutorId);
    });

    it('should reject tutor application', async () => {
      const updatedProfile = await TutorService.updateTutorStatus(testTutorId, 'rejected');

      expect(updatedProfile.status).toBe('rejected');
      expect(updatedProfile.id).toBe(testTutorId);
    });

    it('should throw error if tutor profile does not exist', async () => {
      await expect(TutorService.updateTutorStatus('non-existent-id', 'approved'))
        .rejects.toThrow('Tutor profile not found');
    });
  });
});
