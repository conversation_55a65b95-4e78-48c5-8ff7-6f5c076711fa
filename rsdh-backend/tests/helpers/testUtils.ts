import { prisma } from '../../src/lib/prisma';
import { FastifyInstance } from 'fastify';
import { createApp } from '../../src/app';

/**
 * Test utilities for database operations and cleanup
 */
export class TestUtils {
  /**
   * Clean all test data from database in correct order
   */
  static async cleanDatabase(): Promise<void> {
    try {
      // Delete in correct order to respect foreign key constraints
      await prisma.review.deleteMany();
      await prisma.appointment.deleteMany();
      await prisma.tutorAvailability.deleteMany();
      await prisma.tutorCareer.deleteMany();
      await prisma.tutorEducation.deleteMany();
      await prisma.tutorProfile.deleteMany();
      await prisma.user.deleteMany();
    } catch (error) {
      console.error('Error cleaning database:', error);
      // Don't throw error to avoid breaking tests
    }
  }

  /**
   * Create a test user
   */
  static async createTestUser(email?: string, name?: string) {
    return await prisma.user.create({
      data: {
        email: email || `test-user-${Date.now()}-${Math.random()}@example.com`,
        name: name || 'Test User',
        password: 'hashedpassword',
        role: 'user'
      }
    });
  }

  /**
   * Create a test tutor profile
   */
  static async createTestTutor(userId: string, data?: Partial<any>) {
    return await prisma.tutorProfile.create({
      data: {
        userId,
        title: data?.title || 'Test Tutor',
        bio: data?.bio || 'Test bio',
        status: data?.status || 'approved',
        ...data
      }
    });
  }

  /**
   * Create test tutor availability
   */
  static async createTestAvailability(tutorId: string, dayOfWeek: number, startTime: string, endTime: string) {
    return await prisma.tutorAvailability.create({
      data: {
        tutorId,
        dayOfWeek,
        startTime,
        endTime
      }
    });
  }

  /**
   * Create test appointment
   */
  static async createTestAppointment(tutorId: string, studentId: string, data?: Partial<any>) {
    const startTime = data?.startTime || new Date('2023-01-01T10:00:00Z');
    const endTime = data?.endTime || new Date('2023-01-01T11:00:00Z');
    const durationMinutes = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));

    return await prisma.appointment.create({
      data: {
        tutorId,
        studentId,
        startTime,
        endTime,
        duration: durationMinutes,
        meetingType: data?.meetingType || 'online',
        meetingLink: data?.meetingLink || 'https://zoom.us/j/123456789',
        status: data?.status || 'completed',
        notes: data?.notes,
        ...data
      }
    });
  }

  /**
   * Create test review
   */
  static async createTestReview(appointmentId: string, tutorId: string, data?: Partial<any>) {
    return await prisma.review.create({
      data: {
        appointmentId,
        tutorId,
        rating: data?.rating || 5,
        comment: data?.comment || 'Test review',
        ...data
      }
    });
  }

  /**
   * Create test education record
   */
  static async createTestEducation(tutorId: string, data?: Partial<any>) {
    return await prisma.tutorEducation.create({
      data: {
        tutorId,
        institution: data?.institution || 'Test University',
        degree: data?.degree || 'Bachelor',
        fieldOfStudy: data?.fieldOfStudy || 'Computer Science',
        startYear: data?.startYear || 2018,
        endYear: data?.endYear || 2022,
        ...data
      }
    });
  }

  /**
   * Create test career record
   */
  static async createTestCareer(tutorId: string, data?: Partial<any>) {
    return await prisma.tutorCareer.create({
      data: {
        tutorId,
        title: data?.title || 'Software Engineer',
        company: data?.company || 'Test Company',
        startYear: data?.startYear || 2020,
        endYear: data?.endYear,
        current: data?.current || false,
        description: data?.description,
        ...data
      }
    });
  }

  /**
   * Get next Monday date for testing
   */
  static getNextMonday(): Date {
    const today = new Date();
    const nextMonday = new Date(today);
    nextMonday.setDate(today.getDate() + (1 + 7 - today.getDay()) % 7);
    if (nextMonday <= today) {
      nextMonday.setDate(nextMonday.getDate() + 7);
    }
    return nextMonday;
  }

  /**
   * Get next Tuesday date for testing
   */
  static getNextTuesday(): Date {
    const today = new Date();
    const nextTuesday = new Date(today);
    nextTuesday.setDate(today.getDate() + (2 + 7 - today.getDay()) % 7);
    if (nextTuesday <= today) {
      nextTuesday.setDate(nextTuesday.getDate() + 7);
    }
    return nextTuesday;
  }

  /**
   * Wait for a specified amount of time
   */
  static async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generate unique email for testing
   */
  static generateUniqueEmail(prefix: string = 'test'): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(7)}@example.com`;
  }

  /**
   * Generate unique name for testing
   */
  static generateUniqueName(prefix: string = 'Test'): string {
    return `${prefix} User ${Date.now()}`;
  }

  /**
   * Safe delete with error handling
   */
  static async safeDelete<T>(deleteOperation: () => Promise<T>): Promise<T | null> {
    try {
      return await deleteOperation();
    } catch (error) {
      // Ignore delete errors in tests
      return null;
    }
  }

  /**
   * Create a complete test setup with user, tutor, and availability
   */
  static async createCompleteTestTutor() {
    const user = await this.createTestUser();
    const tutor = await this.createTestTutor(user.id);

    // Add availability for Monday 9-17
    await this.createTestAvailability(tutor.id, 1, '09:00', '17:00');

    return {
      user,
      tutor,
      userId: user.id,
      tutorId: tutor.id
    };
  }

  /**
   * Create a complete test setup with student and tutor
   */
  static async createTestStudentAndTutor() {
    const student = await this.createTestUser(
      this.generateUniqueEmail('student'),
      'Test Student'
    );

    const tutorUser = await this.createTestUser(
      this.generateUniqueEmail('tutor'),
      'Test Tutor'
    );

    const tutor = await this.createTestTutor(tutorUser.id);

    // Add availability
    await this.createTestAvailability(tutor.id, 1, '09:00', '17:00');

    return {
      student,
      tutorUser,
      tutor,
      studentId: student.id,
      tutorUserId: tutorUser.id,
      tutorId: tutor.id
    };
  }

  /**
   * Create a test appointment with all dependencies
   */
  static async createTestAppointmentWithDependencies(data?: Partial<any>) {
    const { student, tutor, studentId, tutorId } = await this.createTestStudentAndTutor();

    const appointment = await this.createTestAppointment(tutorId, studentId, data);

    return {
      student,
      tutor,
      appointment,
      studentId,
      tutorId,
      appointmentId: appointment.id
    };
  }

  /**
   * Create a test review with all dependencies
   */
  static async createTestReviewWithDependencies(data?: Partial<any>) {
    const { appointment, tutorId, appointmentId } = await this.createTestAppointmentWithDependencies({
      status: 'completed'
    });

    const review = await this.createTestReview(appointmentId, tutorId, data);

    return {
      appointment,
      review,
      tutorId,
      appointmentId,
      reviewId: review.id
    };
  }
}

/**
 * Build a test Fastify app instance
 */
export async function buildApp(): Promise<FastifyInstance> {
  const app = await createApp();

  // Override some configurations for testing
  app.config.NODE_ENV = 'test';
  app.config.DATABASE_URL = process.env.DATABASE_URL || 'postgresql://rsdh_bot:tahshoo4sal5Fael@127.0.0.1:15432/rsdh_test';

  return app;
}
