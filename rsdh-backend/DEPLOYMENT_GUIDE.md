# S3存储功能部署指南

## 概述

本指南将帮助您部署和配置S3存储功能，包括文件上传、管理和CDN加速等功能。

## 前置要求

1. Node.js 18+ 
2. PostgreSQL 数据库
3. S3兼容存储服务账户（AWS S3、阿里云OSS、腾讯云COS、Backblaze B2等）
4. （可选）CDN服务

## 部署步骤

### 1. 环境配置

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下关键变量：

#### 基础配置
```bash
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"
JWT_SECRET="your-super-secret-jwt-key-here-make-it-long-and-random"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="secure_admin_password"
```

#### 邮件配置
```bash
EMAIL_HOST="smtp.yourdomain.com"
EMAIL_PORT=587
EMAIL_USER="<EMAIL>"
EMAIL_PASS="your_email_password"
EMAIL_FROM="<EMAIL>"
```

#### S3存储配置
```bash
# 选择存储提供商：aws, backblaze, aliyun, tencent, minio
S3_PROVIDER=aws
S3_REGION=us-east-1
S3_ACCESS_KEY_ID=your_access_key_id
S3_SECRET_ACCESS_KEY=your_secret_access_key
S3_BUCKET_NAME=your_bucket_name

# 自定义端点（非AWS服务商需要）
S3_ENDPOINT=https://s3.us-west-002.backblazeb2.com

# 强制路径样式（MinIO等需要）
S3_FORCE_PATH_STYLE=false

# CDN加速域名（可选）
CDN_BASE_URL=https://cdn.yourdomain.com
```

#### 文件上传配置
```bash
# 最大文件大小（字节）
MAX_FILE_SIZE=10485760

# 允许的文件类型
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,video/mp4,video/webm,audio/mp3,audio/wav,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 数据库设置

生成Prisma客户端：
```bash
npx prisma generate
```

运行数据库迁移：
```bash
npx prisma migrate deploy
```

或者推送数据库结构（开发环境）：
```bash
npx prisma db push
```

### 4. 构建应用

```bash
npm run build
```

### 5. 启动服务

开发环境：
```bash
npm run dev
```

生产环境：
```bash
npm start
```

## S3存储桶配置

### AWS S3配置

1. **创建存储桶**
   - 登录AWS控制台
   - 创建新的S3存储桶
   - 配置区域和访问权限

2. **设置CORS策略**
```json
[
    {
        "AllowedHeaders": ["*"],
        "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
        "AllowedOrigins": ["*"],
        "ExposeHeaders": ["ETag"]
    }
]
```

3. **配置存储桶策略**（公开文件访问）
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::your-bucket-name/public/*"
        }
    ]
}
```

### 阿里云OSS配置

1. **创建Bucket**
   - 登录阿里云控制台
   - 创建新的OSS Bucket
   - 选择合适的地域

2. **配置跨域设置**
   - 来源：*
   - 允许Methods：GET, PUT, POST, DELETE
   - 允许Headers：*

3. **设置访问权限**
   - 私有读写（推荐）
   - 或公共读私有写

### 腾讯云COS配置

1. **创建存储桶**
   - 登录腾讯云控制台
   - 创建COS存储桶
   - 选择地域和访问权限

2. **配置跨域访问**
   - 设置CORS规则
   - 允许的来源：*
   - 允许的方法：GET, PUT, POST, DELETE

## CDN配置

### AWS CloudFront

1. **创建分发**
   - 源域名：S3存储桶域名
   - 查看器协议策略：Redirect HTTP to HTTPS
   - 缓存行为设置

2. **配置自定义域名**（可选）
   - 添加CNAME记录
   - 配置SSL证书

### 阿里云CDN

1. **添加加速域名**
   - 加速域名：cdn.yourdomain.com
   - 源站类型：OSS域名
   - 源站地址：OSS Bucket域名

2. **配置缓存规则**
   - 图片文件：缓存30天
   - 视频文件：缓存7天
   - 文档文件：缓存1天

## 测试部署

### 1. 健康检查

```bash
curl http://localhost:3001/health
```

### 2. API文档

访问 `http://localhost:3001/documentation` 查看Swagger文档

### 3. 运行测试

```bash
node test-file-upload.js
```

### 4. 前端测试

启动前端应用并测试文件上传功能：
- 访问 `/files` 页面
- 测试文件上传
- 验证文件列表和删除功能

## 生产环境部署

### 使用PM2

1. **安装PM2**
```bash
npm install -g pm2
```

2. **创建PM2配置文件** `ecosystem.config.js`
```javascript
module.exports = {
  apps: [{
    name: 'rsdh-backend',
    script: 'dist/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    }
  }]
};
```

3. **启动应用**
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 使用Docker

1. **创建Dockerfile**
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
COPY prisma ./prisma
RUN npx prisma generate
EXPOSE 3001
CMD ["node", "dist/index.js"]
```

2. **构建和运行**
```bash
docker build -t rsdh-backend .
docker run -d -p 3001:3001 --env-file .env rsdh-backend
```

## 监控和维护

### 日志监控

- 应用日志：检查文件上传/下载错误
- S3访问日志：监控存储使用情况
- CDN日志：分析访问模式

### 性能监控

- 文件上传速度
- 存储使用量
- CDN命中率
- API响应时间

### 定期维护

- 清理未使用的文件
- 检查存储配额
- 更新访问密钥
- 备份重要文件

## 故障排除

### 常见问题

1. **文件上传失败**
   - 检查S3配置和权限
   - 验证文件类型和大小限制
   - 查看应用日志

2. **文件访问403错误**
   - 检查存储桶权限设置
   - 验证预签名URL是否过期
   - 确认CORS配置

3. **CDN缓存问题**
   - 清除CDN缓存
   - 检查缓存规则设置
   - 验证源站配置

### 调试命令

```bash
# 检查数据库连接
npx prisma db pull

# 测试S3连接
node -e "console.log(process.env.S3_ACCESS_KEY_ID)"

# 查看应用日志
pm2 logs rsdh-backend
```

## 安全建议

1. **访问控制**
   - 使用最小权限原则
   - 定期轮换访问密钥
   - 启用MFA认证

2. **文件安全**
   - 验证文件类型和内容
   - 扫描恶意文件
   - 限制文件大小

3. **网络安全**
   - 使用HTTPS传输
   - 配置防火墙规则
   - 启用访问日志

## 支持

如有问题，请查看：
- API文档：`/documentation`
- 配置指南：`S3_CONFIGURATION.md`
- 实现总结：`S3_IMPLEMENTATION_SUMMARY.md`
