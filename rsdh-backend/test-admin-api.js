const { createApp } = require('./dist/app.js');

async function testAdminAPI() {
  try {
    console.log('Starting admin API test...');
    
    const app = await createApp();
    
    // Start the server
    await app.listen({ port: 3001, host: '0.0.0.0' });
    console.log('✅ Server started successfully on port 3001');
    
    // Test admin dashboard stats endpoint
    const adminToken = Buffer.from('<EMAIL>').toString('base64');
    
    const response = await fetch('http://localhost:3001/api/admin/dashboard/stats', {
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Admin dashboard stats API working:');
      console.log(JSON.stringify(data, null, 2));
    } else {
      console.log('❌ Admin dashboard stats API failed:', response.status, await response.text());
    }
    
    // Test Swagger documentation
    const swaggerResponse = await fetch('http://localhost:3001/documentation');
    if (swaggerResponse.ok) {
      console.log('✅ Swagger documentation available at http://localhost:3001/documentation');
    } else {
      console.log('❌ Swagger documentation not available');
    }
    
    console.log('\n🎉 Admin panel backend is ready!');
    console.log('📊 Admin Dashboard: http://localhost:3000/admin/dashboard');
    console.log('📚 API Documentation: http://localhost:3001/documentation');
    console.log('🔧 Available admin endpoints:');
    console.log('  - GET /api/admin/dashboard/stats');
    console.log('  - GET /api/admin/dashboard/user-growth');
    console.log('  - GET /api/admin/dashboard/appointment-trends');
    console.log('  - GET /api/admin/tutors/low-rating');
    console.log('  - PATCH /api/admin/tutors/:id/status');
    console.log('  - GET /api/admin/analytics/revenue');
    console.log('  - GET /api/admin/system/health');
    
  } catch (error) {
    console.error('❌ Error testing admin API:', error);
    process.exit(1);
  }
}

testAdminAPI();
