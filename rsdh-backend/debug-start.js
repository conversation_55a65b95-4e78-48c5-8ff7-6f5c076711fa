// Debug script to check what's preventing the server from starting
const { spawn } = require('child_process');

console.log('🔍 调试后端启动问题...\n');

// Try to run the server with more verbose output
const child = spawn('node', ['dist/index.js'], {
    stdio: ['inherit', 'pipe', 'pipe'],
    env: { ...process.env, DEBUG: '*' }
});

child.stdout.on('data', (data) => {
    console.log('STDOUT:', data.toString());
});

child.stderr.on('data', (data) => {
    console.error('STDERR:', data.toString());
});

child.on('close', (code) => {
    console.log(`\n进程退出，代码: ${code}`);
});

child.on('error', (error) => {
    console.error('启动错误:', error);
});

// Kill after 10 seconds if still running
setTimeout(() => {
    if (!child.killed) {
        console.log('10秒后强制终止...');
        child.kill();
    }
}, 10000);
