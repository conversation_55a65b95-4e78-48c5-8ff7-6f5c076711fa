// Simple server to test admin APIs
const http = require('http');
const url = require('url');

const server = http.createServer((req, res) => {
    // Enable CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(204);
        res.end();
        return;
    }
    
    const parsedUrl = url.parse(req.url, true);
    const path = parsedUrl.pathname;
    
    console.log(`${req.method} ${path}`);
    
    // Mock admin API responses
    if (path === '/api/admin/dashboard/stats') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            users: { total: 2, active: 2, newThisMonth: 2, growth: 0 },
            tutors: { total: 0, approved: 0, pending: 0, rejected: 0, averageRating: 0 },
            appointments: { total: 0, completed: 0, scheduled: 0, cancelled: 0, thisMonth: 0 },
            payments: { totalRevenue: 0, thisMonthRevenue: 0, totalTransactions: 0, averageOrderValue: 0 }
        }));
    } else if (path === '/api/admin/dashboard/user-growth') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify([
            { month: '2024-12', count: '2' },
            { month: '2025-01', count: '0' }
        ]));
    } else if (path === '/api/admin/dashboard/appointment-trends') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify([
            { month: '2024-12', total: '0', completed: '0', cancelled: '0' },
            { month: '2025-01', total: '0', completed: '0', cancelled: '0' }
        ]));
    } else if (path === '/api/admin/tutors/low-rating') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            tutors: [],
            pagination: { page: 1, limit: 10, total: 0, totalPages: 0 }
        }));
    } else if (path === '/api/admin/analytics/revenue') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            revenueTrends: [],
            paymentMethods: [],
            topTutors: []
        }));
    } else if (path === '/api/admin/system/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            database: { connected: true, responseTime: 5 },
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            activity: { activeUsers: 2, onlineUsers: 1 },
            pending: { verifications: 0, appointments: 0 }
        }));
    } else if (path === '/documentation') {
        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end('<html><body><h1>API Documentation</h1><p>Swagger UI would be here</p></body></html>');
    } else {
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not found' }));
    }
});

const PORT = 3001;
server.listen(PORT, () => {
    console.log(`🚀 Mock server running on http://localhost:${PORT}`);
    console.log(`📊 Admin APIs available:`);
    console.log(`  - GET /api/admin/dashboard/stats`);
    console.log(`  - GET /api/admin/dashboard/user-growth`);
    console.log(`  - GET /api/admin/dashboard/appointment-trends`);
    console.log(`  - GET /api/admin/tutors/low-rating`);
    console.log(`  - GET /api/admin/analytics/revenue`);
    console.log(`  - GET /api/admin/system/health`);
    console.log(`  - GET /documentation`);
});
