# S3存储配置指南

本应用支持多种S3兼容的存储服务，包括AWS S3、Backblaze B2、阿里云OSS、腾讯云COS、MinIO等。

## 环境变量配置

在 `.env` 文件中配置以下环境变量：

```bash
# S3存储配置
S3_PROVIDER=aws                    # 存储服务提供商
S3_REGION=us-east-1               # 存储区域
S3_ACCESS_KEY_ID=your_access_key   # 访问密钥ID
S3_SECRET_ACCESS_KEY=your_secret   # 访问密钥
S3_BUCKET_NAME=your_bucket_name    # 存储桶名称
S3_ENDPOINT=                       # 自定义端点（可选）
S3_FORCE_PATH_STYLE=false         # 强制路径样式（MinIO等需要）
CDN_BASE_URL=                      # CDN加速域名（可选）

# 文件上传配置
MAX_FILE_SIZE=10485760             # 最大文件大小（字节）
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,video/mp4,video/webm,audio/mp3,audio/wav,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document
```

## 不同服务商配置示例

### 1. AWS S3

```bash
S3_PROVIDER=aws
S3_REGION=us-east-1
S3_ACCESS_KEY_ID=AKIAIOSFODNN7EXAMPLE
S3_SECRET_ACCESS_KEY=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
S3_BUCKET_NAME=my-app-bucket
# S3_ENDPOINT 留空，使用默认AWS端点
S3_FORCE_PATH_STYLE=false
CDN_BASE_URL=https://d123456789.cloudfront.net
```

### 2. Backblaze B2

```bash
S3_PROVIDER=backblaze
S3_REGION=us-west-002
S3_ACCESS_KEY_ID=your_key_id
S3_SECRET_ACCESS_KEY=your_application_key
S3_BUCKET_NAME=your-bucket-name
S3_ENDPOINT=https://s3.us-west-002.backblazeb2.com
S3_FORCE_PATH_STYLE=false
CDN_BASE_URL=https://f002.backblazeb2.com/file/your-bucket-name
```

### 3. 阿里云OSS

```bash
S3_PROVIDER=aliyun
S3_REGION=oss-cn-hangzhou
S3_ACCESS_KEY_ID=your_access_key_id
S3_SECRET_ACCESS_KEY=your_access_key_secret
S3_BUCKET_NAME=your-bucket-name
S3_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com
S3_FORCE_PATH_STYLE=false
CDN_BASE_URL=https://your-cdn-domain.com
```

### 4. 腾讯云COS

```bash
S3_PROVIDER=tencent
S3_REGION=ap-beijing
S3_ACCESS_KEY_ID=your_secret_id
S3_SECRET_ACCESS_KEY=your_secret_key
S3_BUCKET_NAME=your-bucket-name-**********
S3_ENDPOINT=https://cos.ap-beijing.myqcloud.com
S3_FORCE_PATH_STYLE=false
CDN_BASE_URL=https://your-cdn-domain.com
```

### 5. MinIO (自建)

```bash
S3_PROVIDER=minio
S3_REGION=us-east-1
S3_ACCESS_KEY_ID=minioadmin
S3_SECRET_ACCESS_KEY=minioadmin
S3_BUCKET_NAME=my-bucket
S3_ENDPOINT=http://localhost:9000
S3_FORCE_PATH_STYLE=true
CDN_BASE_URL=
```

## 存储桶配置

### AWS S3存储桶策略示例

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::your-bucket-name/public/*"
        }
    ]
}
```

### CORS配置示例

```json
[
    {
        "AllowedHeaders": ["*"],
        "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
        "AllowedOrigins": ["*"],
        "ExposeHeaders": ["ETag"]
    }
]
```

## CDN配置

### CloudFront配置（AWS）

1. 创建CloudFront分发
2. 源域名设置为S3存储桶域名
3. 配置缓存行为
4. 设置自定义域名（可选）

### 阿里云CDN配置

1. 添加加速域名
2. 源站类型选择"OSS域名"
3. 配置缓存规则
4. 开启HTTPS（推荐）

## 文件分类说明

应用支持以下文件分类：

- `avatar`: 用户头像
- `document`: 文档文件
- `media`: 媒体文件（图片、视频、音频）
- `general`: 通用文件

文件将按分类存储在不同的目录中：
```
bucket/
├── avatar/
├── document/
├── media/
└── general/
```

## API使用示例

### 上传文件

```javascript
const formData = new FormData();
formData.append('file', file);
formData.append('category', 'avatar');
formData.append('isPublic', 'true');

const response = await fetch('/api/files/upload', {
  method: 'POST',
  body: formData,
  headers: {
    'Authorization': `Bearer ${token}`,
  },
});
```

### 获取预签名上传URL

```javascript
const response = await fetch('/api/files/upload-url', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  },
  body: JSON.stringify({
    fileName: 'example.jpg',
    contentType: 'image/jpeg',
    category: 'avatar',
  }),
});

const { uploadUrl, fileId } = await response.json();

// 直接上传到S3
await fetch(uploadUrl, {
  method: 'PUT',
  body: file,
  headers: {
    'Content-Type': 'image/jpeg',
  },
});

// 确认上传完成
await fetch(`/api/files/confirm-upload/${fileId}`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
  },
});
```

## 安全注意事项

1. **访问密钥安全**: 确保访问密钥不会泄露，定期轮换密钥
2. **存储桶权限**: 合理配置存储桶权限，避免公开敏感文件
3. **文件类型验证**: 严格验证上传文件的类型和大小
4. **HTTPS传输**: 生产环境中使用HTTPS传输文件
5. **CDN配置**: 合理配置CDN缓存策略，提高访问速度

## 故障排除

### 常见错误

1. **AccessDenied**: 检查访问密钥和存储桶权限
2. **NoSuchBucket**: 确认存储桶名称和区域配置正确
3. **SignatureDoesNotMatch**: 检查访问密钥和时间同步
4. **CORS错误**: 配置存储桶CORS策略

### 调试方法

1. 检查环境变量配置
2. 查看应用日志
3. 测试存储服务连接
4. 验证存储桶权限设置

## 性能优化

1. **CDN加速**: 配置CDN提高文件访问速度
2. **图片压缩**: 上传前压缩图片文件
3. **缓存策略**: 设置合理的缓存头
4. **分片上传**: 大文件使用分片上传（待实现）

## 监控和日志

建议监控以下指标：
- 存储使用量
- 请求次数和错误率
- 文件上传/下载速度
- CDN命中率

定期检查和清理：
- 未使用的文件
- 临时文件
- 过期的预签名URL
