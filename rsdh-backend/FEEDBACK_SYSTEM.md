# 反馈系统 (Feedback System)

## 概述

本项目已成功实现了一个完整的反馈系统，支持用户直接反馈、文件上传、以及对特定导师/预约的反馈功能。

## 功能特性

### 用户功能
- ✅ **创建反馈**: 支持多种类型的反馈（一般、错误报告、建议、投诉、导师反馈、预约反馈）
- ✅ **文件上传**: 支持上传多个图片/视频等文件作为反馈附件
- ✅ **关联反馈**: 可以对特定导师或预约进行反馈
- ✅ **查看反馈**: 用户可以查看自己提交的反馈列表
- ✅ **删除反馈**: 用户可以删除自己未处理的反馈

### 管理员功能
- ✅ **反馈管理**: 查看所有用户反馈
- ✅ **状态更新**: 更新反馈状态（待处理、处理中、已解决、已关闭）
- ✅ **优先级设置**: 设置反馈优先级（低、正常、高、紧急）
- ✅ **分配处理**: 将反馈分配给特定管理员
- ✅ **统计数据**: 获取反馈统计信息
- ✅ **文件管理**: 管理反馈附件

## 数据库模型

### Feedback 表
```sql
- id: 反馈ID (UUID)
- userId: 用户ID
- type: 反馈类型 (general, bug, suggestion, complaint, tutor_feedback, appointment_feedback)
- title: 反馈标题 (可选)
- content: 反馈内容
- contactInfo: 联系方式 (可选)
- status: 状态 (pending, in_progress, resolved, closed)
- priority: 优先级 (low, normal, high, urgent)
- category: 分类 (general, technical, service, billing, ui_ux, performance)
- relatedTutorId: 关联导师ID (可选)
- relatedAppointmentId: 关联预约ID (可选)
- assignedToId: 分配的管理员ID (可选)
- adminNotes: 管理员备注 (可选)
- resolvedAt: 解决时间 (可选)
- userAgent: 用户代理
- ipAddress: IP地址
- metadata: 元数据 (JSON)
- createdAt: 创建时间
- updatedAt: 更新时间
```

### FeedbackFile 表
```sql
- id: 关联ID (UUID)
- feedbackId: 反馈ID
- fileId: 文件ID
- description: 文件描述 (可选)
- createdAt: 创建时间
```

## API 接口

### 反馈相关接口

#### 1. 创建反馈
```
POST /api/feedback
Authorization: Bearer <token>
Content-Type: application/json

{
  "type": "general",
  "title": "反馈标题",
  "content": "反馈内容",
  "category": "general",
  "contactInfo": "联系方式",
  "relatedTutorId": "导师ID",
  "relatedAppointmentId": "预约ID",
  "fileIds": ["文件ID1", "文件ID2"]
}
```

#### 2. 获取反馈列表
```
GET /api/feedback?page=1&limit=20&type=general&status=pending
Authorization: Bearer <token>
```

#### 3. 获取反馈详情
```
GET /api/feedback/:id
Authorization: Bearer <token>
```

#### 4. 更新反馈 (管理员)
```
PATCH /api/feedback/:id
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "status": "in_progress",
  "priority": "high",
  "assignedToId": "管理员ID",
  "adminNotes": "管理员备注"
}
```

#### 5. 删除反馈
```
DELETE /api/feedback/:id
Authorization: Bearer <token>
```

#### 6. 附加文件到反馈
```
POST /api/feedback/files/attach
Authorization: Bearer <token>
Content-Type: application/json

{
  "feedbackId": "反馈ID",
  "fileId": "文件ID",
  "description": "文件描述"
}
```

#### 7. 从反馈中移除文件
```
DELETE /api/feedback/:feedbackId/files/:fileId
Authorization: Bearer <token>
```

#### 8. 获取反馈统计 (管理员)
```
GET /api/feedback/admin/stats
Authorization: Bearer <admin_token>
```

## 前端演示

访问 `http://localhost:3004/public/feedback-demo.html` 查看完整的前端演示页面，包括：

- 用户登录
- 反馈表单提交
- 文件上传
- 反馈列表查看
- 实时状态更新

## 测试

### 单元测试
- ✅ FeedbackService 测试 (`tests/services/feedbackService.test.ts`)
- ✅ Feedback 路由测试 (`tests/routes/feedback.test.ts`)

### API 测试示例
```bash
# 登录获取token
curl -X POST http://localhost:3004/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test1234"}'

# 创建反馈
curl -X POST http://localhost:3004/api/feedback \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"content":"测试反馈","type":"general","category":"general"}'

# 获取反馈列表
curl -X GET http://localhost:3004/api/feedback \
  -H "Authorization: Bearer <token>"
```

## 权限控制

### 用户权限
- 创建反馈
- 查看自己的反馈
- 删除自己未处理的反馈
- 上传文件附件

### 管理员权限
- 查看所有反馈
- 更新反馈状态和优先级
- 分配反馈处理人
- 删除任何反馈
- 查看统计数据

## 安全特性

- ✅ JWT 身份验证
- ✅ 权限验证
- ✅ 文件访问控制
- ✅ 输入验证
- ✅ SQL 注入防护 (Prisma ORM)
- ✅ XSS 防护
- ✅ CORS 配置

## 部署说明

1. 确保数据库迁移已执行：
   ```bash
   npx prisma migrate deploy
   ```

2. 启动服务器：
   ```bash
   npm run start
   ```

3. 访问 Swagger 文档：
   ```
   http://localhost:3001/documentation
   ```

## 扩展功能建议

### 短期扩展
- [ ] 邮件通知功能
- [ ] 反馈评分系统
- [ ] 批量操作功能
- [ ] 导出功能

### 长期扩展
- [ ] 反馈分析仪表板
- [ ] 自动分类功能
- [ ] 工作流自动化
- [ ] 移动端应用

## 技术栈

- **后端**: Node.js + Fastify + TypeScript
- **数据库**: PostgreSQL + Prisma ORM
- **认证**: JWT
- **文件存储**: S3兼容存储
- **测试**: Vitest
- **文档**: Swagger/OpenAPI

## 联系方式

如有问题或建议，请通过反馈系统提交或联系开发团队。
