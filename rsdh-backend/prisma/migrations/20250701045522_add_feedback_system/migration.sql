/*
  Warnings:

  - You are about to alter the column `price` on the `appointments` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `BigInt`.
  - You are about to alter the column `tutor_amount` on the `payment_splits` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `BigInt`.
  - You are about to alter the column `platform_amount` on the `payment_splits` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `BigInt`.
  - You are about to alter the column `amount` on the `payments` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `BigInt`.
  - You are about to alter the column `rate` on the `tutors` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `BigInt`.
  - You are about to alter the column `half_hour_rate` on the `tutors` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `BigInt`.
  - You are about to alter the column `hourly_rate` on the `tutors` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `BigInt`.

*/
-- AlterTable
ALTER TABLE "appointments" ALTER COLUMN "price" SET DEFAULT 0,
ALTER COLUMN "price" SET DATA TYPE BIGINT;

-- AlterTable
ALTER TABLE "payment_splits" ADD COLUMN     "processed_by_id" TEXT,
ALTER COLUMN "tutor_amount" SET DATA TYPE BIGINT,
ALTER COLUMN "platform_amount" SET DATA TYPE BIGINT;

-- AlterTable
ALTER TABLE "payments" ADD COLUMN     "refund_amount" BIGINT,
ADD COLUMN     "refund_reason" TEXT,
ADD COLUMN     "refunded_by_id" TEXT,
ALTER COLUMN "amount" SET DATA TYPE BIGINT;

-- AlterTable
ALTER TABLE "tutors" ALTER COLUMN "rate" SET DEFAULT 0,
ALTER COLUMN "rate" SET DATA TYPE BIGINT,
ALTER COLUMN "half_hour_rate" SET DEFAULT 0,
ALTER COLUMN "half_hour_rate" SET DATA TYPE BIGINT,
ALTER COLUMN "hourly_rate" SET DEFAULT 0,
ALTER COLUMN "hourly_rate" SET DATA TYPE BIGINT;

-- CreateTable
CREATE TABLE "feedback" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "type" TEXT NOT NULL DEFAULT 'general',
    "title" TEXT,
    "content" TEXT NOT NULL,
    "contact_info" TEXT,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "priority" TEXT NOT NULL DEFAULT 'normal',
    "category" TEXT NOT NULL DEFAULT 'general',
    "related_tutor_id" TEXT,
    "related_appointment_id" TEXT,
    "assigned_to_id" TEXT,
    "admin_notes" TEXT,
    "resolved_at" TIMESTAMP(3),
    "user_agent" TEXT,
    "ip_address" TEXT,
    "metadata" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "feedback_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feedback_files" (
    "id" TEXT NOT NULL,
    "feedback_id" TEXT NOT NULL,
    "file_id" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "feedback_files_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "feedback_user_id_idx" ON "feedback"("user_id");

-- CreateIndex
CREATE INDEX "feedback_type_idx" ON "feedback"("type");

-- CreateIndex
CREATE INDEX "feedback_status_idx" ON "feedback"("status");

-- CreateIndex
CREATE INDEX "feedback_priority_idx" ON "feedback"("priority");

-- CreateIndex
CREATE INDEX "feedback_category_idx" ON "feedback"("category");

-- CreateIndex
CREATE INDEX "feedback_related_tutor_id_idx" ON "feedback"("related_tutor_id");

-- CreateIndex
CREATE INDEX "feedback_related_appointment_id_idx" ON "feedback"("related_appointment_id");

-- CreateIndex
CREATE INDEX "feedback_assigned_to_id_idx" ON "feedback"("assigned_to_id");

-- CreateIndex
CREATE INDEX "feedback_created_at_idx" ON "feedback"("created_at");

-- CreateIndex
CREATE INDEX "feedback_files_feedback_id_idx" ON "feedback_files"("feedback_id");

-- CreateIndex
CREATE INDEX "feedback_files_file_id_idx" ON "feedback_files"("file_id");

-- CreateIndex
CREATE UNIQUE INDEX "feedback_files_feedback_id_file_id_key" ON "feedback_files"("feedback_id", "file_id");

-- AddForeignKey
ALTER TABLE "payments" ADD CONSTRAINT "payments_refunded_by_id_fkey" FOREIGN KEY ("refunded_by_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payment_splits" ADD CONSTRAINT "payment_splits_processed_by_id_fkey" FOREIGN KEY ("processed_by_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feedback" ADD CONSTRAINT "feedback_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feedback" ADD CONSTRAINT "feedback_related_tutor_id_fkey" FOREIGN KEY ("related_tutor_id") REFERENCES "tutors"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feedback" ADD CONSTRAINT "feedback_related_appointment_id_fkey" FOREIGN KEY ("related_appointment_id") REFERENCES "appointments"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feedback" ADD CONSTRAINT "feedback_assigned_to_id_fkey" FOREIGN KEY ("assigned_to_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feedback_files" ADD CONSTRAINT "feedback_files_feedback_id_fkey" FOREIGN KEY ("feedback_id") REFERENCES "feedback"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feedback_files" ADD CONSTRAINT "feedback_files_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE CASCADE ON UPDATE CASCADE;
