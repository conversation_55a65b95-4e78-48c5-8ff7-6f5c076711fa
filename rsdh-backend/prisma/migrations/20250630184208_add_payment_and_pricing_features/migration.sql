/*
  Warnings:

  - A unique constraint covering the columns `[confirmation_token]` on the table `appointments` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `duration` to the `appointments` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "appointments" ADD COLUMN     "confirmation_expires_at" TIMESTAMP(3),
ADD COLUMN     "confirmation_status" TEXT NOT NULL DEFAULT 'pending',
ADD COLUMN     "confirmation_token" TEXT,
ADD COLUMN     "currency" TEXT NOT NULL DEFAULT 'CNY',
ADD COLUMN     "duration" INTEGER NOT NULL,
ADD COLUMN     "payment_status" TEXT NOT NULL DEFAULT 'pending',
ADD COLUMN     "price" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
ADD COLUMN     "requires_payment" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "tutor_career" ADD COLUMN     "verificationStatus" TEXT NOT NULL DEFAULT 'pending';

-- AlterTable
ALTER TABLE "tutor_education" ADD COLUMN     "verificationStatus" TEXT NOT NULL DEFAULT 'pending';

-- AlterTable
ALTER TABLE "tutors" ADD COLUMN     "currency" TEXT NOT NULL DEFAULT 'CNY',
ADD COLUMN     "half_hour_rate" DOUBLE PRECISION DEFAULT 0.0,
ADD COLUMN     "hourly_rate" DOUBLE PRECISION DEFAULT 0.0,
ADD COLUMN     "is_free" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "education_verification" (
    "id" TEXT NOT NULL,
    "educationId" TEXT NOT NULL,
    "fileId" TEXT NOT NULL,
    "materialType" TEXT NOT NULL,
    "description" TEXT,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "reviewedById" TEXT,
    "reviewedAt" TIMESTAMP(3),
    "reviewNotes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "education_verification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "career_verification" (
    "id" TEXT NOT NULL,
    "careerId" TEXT NOT NULL,
    "fileId" TEXT NOT NULL,
    "materialType" TEXT NOT NULL,
    "description" TEXT,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "reviewedById" TEXT,
    "reviewedAt" TIMESTAMP(3),
    "reviewNotes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "career_verification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "payments" (
    "id" TEXT NOT NULL,
    "appointment_id" TEXT NOT NULL,
    "tutor_id" TEXT NOT NULL,
    "student_id" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'CNY',
    "status" TEXT NOT NULL DEFAULT 'pending',
    "payment_method" TEXT NOT NULL DEFAULT 'wechat',
    "transaction_id" TEXT,
    "wechat_order_id" TEXT,
    "paid_at" TIMESTAMP(3),
    "refunded_at" TIMESTAMP(3),
    "split_processed_at" TIMESTAMP(3),
    "metadata" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "payments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "payment_splits" (
    "id" TEXT NOT NULL,
    "payment_id" TEXT NOT NULL,
    "tutor_id" TEXT NOT NULL,
    "tutor_amount" DOUBLE PRECISION NOT NULL,
    "platform_amount" DOUBLE PRECISION NOT NULL,
    "tutor_percentage" DOUBLE PRECISION NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "transferred_at" TIMESTAMP(3),
    "wechat_transfer_id" TEXT,
    "failure_reason" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "payment_splits_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "education_verification_educationId_idx" ON "education_verification"("educationId");

-- CreateIndex
CREATE INDEX "education_verification_status_idx" ON "education_verification"("status");

-- CreateIndex
CREATE INDEX "career_verification_careerId_idx" ON "career_verification"("careerId");

-- CreateIndex
CREATE INDEX "career_verification_status_idx" ON "career_verification"("status");

-- CreateIndex
CREATE UNIQUE INDEX "payments_appointment_id_key" ON "payments"("appointment_id");

-- CreateIndex
CREATE UNIQUE INDEX "payments_transaction_id_key" ON "payments"("transaction_id");

-- CreateIndex
CREATE UNIQUE INDEX "payments_wechat_order_id_key" ON "payments"("wechat_order_id");

-- CreateIndex
CREATE INDEX "payments_status_idx" ON "payments"("status");

-- CreateIndex
CREATE INDEX "payments_tutor_id_idx" ON "payments"("tutor_id");

-- CreateIndex
CREATE INDEX "payments_student_id_idx" ON "payments"("student_id");

-- CreateIndex
CREATE INDEX "payments_created_at_idx" ON "payments"("created_at");

-- CreateIndex
CREATE UNIQUE INDEX "payment_splits_wechat_transfer_id_key" ON "payment_splits"("wechat_transfer_id");

-- CreateIndex
CREATE INDEX "payment_splits_payment_id_idx" ON "payment_splits"("payment_id");

-- CreateIndex
CREATE INDEX "payment_splits_tutor_id_idx" ON "payment_splits"("tutor_id");

-- CreateIndex
CREATE INDEX "payment_splits_status_idx" ON "payment_splits"("status");

-- CreateIndex
CREATE UNIQUE INDEX "appointments_confirmation_token_key" ON "appointments"("confirmation_token");

-- AddForeignKey
ALTER TABLE "education_verification" ADD CONSTRAINT "education_verification_educationId_fkey" FOREIGN KEY ("educationId") REFERENCES "tutor_education"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "education_verification" ADD CONSTRAINT "education_verification_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "files"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "education_verification" ADD CONSTRAINT "education_verification_reviewedById_fkey" FOREIGN KEY ("reviewedById") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career_verification" ADD CONSTRAINT "career_verification_careerId_fkey" FOREIGN KEY ("careerId") REFERENCES "tutor_career"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career_verification" ADD CONSTRAINT "career_verification_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "files"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career_verification" ADD CONSTRAINT "career_verification_reviewedById_fkey" FOREIGN KEY ("reviewedById") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payments" ADD CONSTRAINT "payments_appointment_id_fkey" FOREIGN KEY ("appointment_id") REFERENCES "appointments"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payments" ADD CONSTRAINT "payments_tutor_id_fkey" FOREIGN KEY ("tutor_id") REFERENCES "tutors"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payments" ADD CONSTRAINT "payments_student_id_fkey" FOREIGN KEY ("student_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payment_splits" ADD CONSTRAINT "payment_splits_payment_id_fkey" FOREIGN KEY ("payment_id") REFERENCES "payments"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payment_splits" ADD CONSTRAINT "payment_splits_tutor_id_fkey" FOREIGN KEY ("tutor_id") REFERENCES "tutors"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
