const jwt = require('jsonwebtoken');

// Create a test JWT token
const testUserId = 'test-user-id';
const token = jwt.sign({ userId: testUserId }, process.env.JWT_SECRET || 'test-secret');

console.log('Test JWT Token:', token);

// Test feedback creation
const testFeedback = {
  content: 'This is a test feedback from API test',
  type: 'general',
  category: 'general'
};

fetch('http://127.0.0.1:3004/api/feedback', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify(testFeedback)
})
.then(response => {
  console.log('Response status:', response.status);
  return response.json();
})
.then(data => {
  console.log('Response data:', JSON.stringify(data, null, 2));
})
.catch(error => {
  console.error('Error:', error);
});
