const fetch = require('node-fetch');

const BASE_URL = 'http://127.0.0.1:3003';

async function login(email, password) {
  const response = await fetch(`${BASE_URL}/api/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email, password })
  });

  if (!response.ok) {
    throw new Error(`Login failed: ${response.status}`);
  }

  const data = await response.json();
  return data.token;
}

async function createTestTutors(token, adminToken) {
  console.log('Creating multiple test tutors...');

  const tutors = [
    {
      title: 'Senior Software Engineer',
      bio: 'Experienced software engineer from top tech company',
      hourlyRate: 300,
      halfHourRate: 150,
      education: [{
        degree: 'Bachelor',
        fieldOfStudy: 'Computer Science',
        institution: 'Tsinghua University',
        startYear: 2015,
        endYear: 2019,
        is985: true,
        is211: true
      }],
      career: [{
        title: 'Software Engineer',
        company: 'ByteDance',
        startYear: 2019,
        current: true
      }]
    },
    {
      title: 'High School Teacher',
      bio: 'Dedicated teacher with 10 years experience',
      hourlyRate: 150,
      halfHourRate: 75,
      education: [{
        degree: 'Master',
        fieldOfStudy: 'Education',
        institution: 'Beijing Normal University',
        startYear: 2018,
        endYear: 2020,
        is985: false,
        is211: true
      }],
      career: [{
        title: 'Mathematics Teacher',
        company: 'Beijing High School',
        startYear: 2020,
        current: true
      }]
    },
    {
      title: 'Graduate Student',
      bio: 'PhD student in mathematics',
      hourlyRate: 80,
      halfHourRate: 40,
      education: [{
        degree: 'PhD',
        fieldOfStudy: 'Mathematics',
        institution: 'Local University',
        startYear: 2020,
        endYear: null,
        is985: false,
        is211: false
      }],
      career: [{
        title: 'Research Assistant',
        company: 'University Lab',
        startYear: 2021,
        current: true
      }]
    }
  ];

  const createdTutors = [];

  for (let i = 0; i < tutors.length; i++) {
    // Register user first
    const userResponse = await fetch(`${BASE_URL}/api/auth/register-with-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: `testtutor${i+1}@filtertest.com`,
        name: `Test Tutor ${i+1}`,
        password: 'Test1234',
        code: '123456' // Mock code for testing
      })
    });

    if (!userResponse.ok) {
      console.error(`Failed to create user ${i+1}:`, await userResponse.text());
      continue;
    }

    // Login as the new user
    const userToken = await login(`testtutor${i+1}@filtertest.com`, 'Test1234');

    // Create tutor profile
    const tutorResponse = await fetch(`${BASE_URL}/api/tutors/apply`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userToken}`
      },
      body: JSON.stringify(tutors[i])
    });

    if (!tutorResponse.ok) {
      console.error(`Failed to create tutor ${i+1}`);
      continue;
    }

    const tutor = await tutorResponse.json();

    // Approve tutor
    await fetch(`${BASE_URL}/api/tutors/admin/${tutor.id}/status`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      },
      body: JSON.stringify({ status: 'approved' })
    });

    createdTutors.push(tutor);
    console.log(`✅ Created and approved tutor ${i+1}: ${tutor.id}`);
  }

  return createdTutors;
}

async function testAllFilters(token) {
  console.log('\n🧪 Testing comprehensive filter functionality...\n');

  const tests = [
    {
      name: 'All tutors',
      url: '/api/tutors',
      expectedMin: 3
    },
    {
      name: '985 universities only',
      url: '/api/tutors?is985=true',
      expectedMin: 1,
      expectedMax: 1
    },
    {
      name: '211 universities only',
      url: '/api/tutors?is211=true',
      expectedMin: 2,
      expectedMax: 2
    },
    {
      name: 'Non-985 universities',
      url: '/api/tutors?is985=false',
      expectedMin: 2,
      expectedMax: 2
    },
    {
      name: 'Institution: Tsinghua',
      url: '/api/tutors?institution=Tsinghua',
      expectedMin: 1,
      expectedMax: 1
    },
    {
      name: 'Institution: Beijing (partial match)',
      url: '/api/tutors?institution=Beijing',
      expectedMin: 1,
      expectedMax: 1
    },
    {
      name: 'Graduation year 2019-2020',
      url: '/api/tutors?graduationYearFrom=2019&graduationYearTo=2020',
      expectedMin: 2,
      expectedMax: 2
    },
    {
      name: 'Career: Software',
      url: '/api/tutors?career=Software',
      expectedMin: 1,
      expectedMax: 1
    },
    {
      name: 'Career: Teacher',
      url: '/api/tutors?career=Teacher',
      expectedMin: 1,
      expectedMax: 1
    },
    {
      name: 'Degree: PhD',
      url: '/api/tutors?degree=PhD',
      expectedMin: 1,
      expectedMax: 1
    },
    {
      name: 'Price range 100-200',
      url: '/api/tutors?priceFrom=100&priceTo=200',
      expectedMin: 1,
      expectedMax: 1
    },
    {
      name: 'Price >= 250',
      url: '/api/tutors?priceFrom=250',
      expectedMin: 1,
      expectedMax: 1
    },
    {
      name: 'Price <= 100',
      url: '/api/tutors?priceTo=100',
      expectedMin: 1,
      expectedMax: 1
    },
    {
      name: 'Combined: 211 + price 100-200',
      url: '/api/tutors?is211=true&priceFrom=100&priceTo=200',
      expectedMin: 1,
      expectedMax: 1
    },
    {
      name: 'Combined: 985 + Software career',
      url: '/api/tutors?is985=true&career=Software',
      expectedMin: 1,
      expectedMax: 1
    },
    {
      name: 'No results: price >= 1000',
      url: '/api/tutors?priceFrom=1000',
      expectedMin: 0,
      expectedMax: 0
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      const response = await fetch(`${BASE_URL}${test.url}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        console.log(`❌ ${test.name}: HTTP ${response.status}`);
        continue;
      }

      const data = await response.json();
      const count = data.tutors.length;

      const passed = (test.expectedMin === undefined || count >= test.expectedMin) &&
                    (test.expectedMax === undefined || count <= test.expectedMax);

      if (passed) {
        console.log(`✅ ${test.name}: ${count} tutors`);
        passedTests++;
      } else {
        console.log(`❌ ${test.name}: ${count} tutors (expected ${test.expectedMin}-${test.expectedMax})`);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: Error - ${error.message}`);
    }
  }

  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  return passedTests === totalTests;
}

async function main() {
  try {
    console.log('🚀 Starting comprehensive filter test...\n');

    // Login as admin
    const adminToken = await login('<EMAIL>', 'Test12345');
    console.log('✅ Admin logged in successfully');

    // Login as regular user for testing
    const userToken = await login('<EMAIL>', 'Test1234');
    console.log('✅ User logged in successfully');

    // Create test tutors
    await createTestTutors(userToken, adminToken);

    // Test all filters
    const allTestsPassed = await testAllFilters(userToken);

    if (allTestsPassed) {
      console.log('\n🎉 All tests passed! Filter functionality is working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check the implementation.');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

main();
