<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>反馈系统演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 120px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .feedback-list {
            margin-top: 30px;
        }
        .feedback-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .feedback-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
        .feedback-content {
            color: #333;
        }
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
        }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-in_progress { background-color: #d1ecf1; color: #0c5460; }
        .status-resolved { background-color: #d4edda; color: #155724; }
        .status-closed { background-color: #f8d7da; color: #721c24; }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .file-upload {
            margin-top: 10px;
        }
        .file-list {
            margin-top: 10px;
        }
        .file-item {
            display: inline-block;
            background: #e9ecef;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>反馈系统演示</h1>
        
        <!-- 登录区域 -->
        <div id="loginSection">
            <h3>登录</h3>
            <div class="form-group">
                <label for="email">邮箱:</label>
                <input type="email" id="email" value="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="Test1234">
            </div>
            <button onclick="login()">登录</button>
        </div>

        <!-- 反馈表单 -->
        <div id="feedbackSection" style="display: none;">
            <h3>提交反馈</h3>
            <form id="feedbackForm">
                <div class="form-group">
                    <label for="type">反馈类型:</label>
                    <select id="type">
                        <option value="general">一般反馈</option>
                        <option value="bug">错误报告</option>
                        <option value="suggestion">建议</option>
                        <option value="complaint">投诉</option>
                        <option value="tutor_feedback">导师反馈</option>
                        <option value="appointment_feedback">预约反馈</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="category">分类:</label>
                    <select id="category">
                        <option value="general">一般</option>
                        <option value="technical">技术问题</option>
                        <option value="service">服务</option>
                        <option value="billing">计费</option>
                        <option value="ui_ux">界面体验</option>
                        <option value="performance">性能</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="title">标题 (可选):</label>
                    <input type="text" id="title" placeholder="请输入反馈标题">
                </div>
                
                <div class="form-group">
                    <label for="content">反馈内容:</label>
                    <textarea id="content" placeholder="请详细描述您的反馈..." required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="contactInfo">联系方式 (可选):</label>
                    <input type="text" id="contactInfo" placeholder="微信号、QQ号或其他联系方式">
                </div>
                
                <div class="form-group">
                    <label for="fileUpload">附件 (可选):</label>
                    <input type="file" id="fileUpload" multiple accept="image/*,video/*,.pdf,.doc,.docx" class="file-upload">
                    <div id="fileList" class="file-list"></div>
                </div>
                
                <button type="submit">提交反馈</button>
                <button type="button" onclick="loadFeedbackList()">刷新列表</button>
            </form>
        </div>

        <!-- 反馈列表 -->
        <div id="feedbackListSection" style="display: none;">
            <h3>我的反馈</h3>
            <div id="feedbackList" class="feedback-list"></div>
        </div>

        <!-- 消息显示区域 -->
        <div id="message"></div>
    </div>

    <script>
        let authToken = localStorage.getItem('authToken');
        let uploadedFiles = [];

        // 检查登录状态
        if (authToken) {
            showFeedbackSection();
        }

        // 登录函数
        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (response.ok) {
                    authToken = data.token;
                    localStorage.setItem('authToken', authToken);
                    showMessage('登录成功！', 'success');
                    showFeedbackSection();
                } else {
                    showMessage('登录失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('登录错误: ' + error.message, 'error');
            }
        }

        // 显示反馈区域
        function showFeedbackSection() {
            document.getElementById('loginSection').style.display = 'none';
            document.getElementById('feedbackSection').style.display = 'block';
            document.getElementById('feedbackListSection').style.display = 'block';
            loadFeedbackList();
        }

        // 文件上传处理
        document.getElementById('fileUpload').addEventListener('change', async function(e) {
            const files = Array.from(e.target.files);
            
            for (const file of files) {
                try {
                    const formData = new FormData();
                    formData.append('files', file);

                    const response = await fetch('/api/files/upload', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        },
                        body: formData
                    });

                    const data = await response.json();

                    if (response.ok) {
                        uploadedFiles.push(...data.files);
                        updateFileList();
                        showMessage(`文件 "${file.name}" 上传成功`, 'success');
                    } else {
                        showMessage(`文件 "${file.name}" 上传失败: ${data.message}`, 'error');
                    }
                } catch (error) {
                    showMessage(`文件 "${file.name}" 上传错误: ${error.message}`, 'error');
                }
            }
        });

        // 更新文件列表显示
        function updateFileList() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = uploadedFiles.map(file => 
                `<span class="file-item">${file.originalName} <button onclick="removeFile('${file.id}')" style="background:none;border:none;color:red;cursor:pointer;">×</button></span>`
            ).join('');
        }

        // 移除文件
        function removeFile(fileId) {
            uploadedFiles = uploadedFiles.filter(file => file.id !== fileId);
            updateFileList();
        }

        // 提交反馈表单
        document.getElementById('feedbackForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const feedbackData = {
                type: document.getElementById('type').value,
                category: document.getElementById('category').value,
                title: document.getElementById('title').value || undefined,
                content: document.getElementById('content').value,
                contactInfo: document.getElementById('contactInfo').value || undefined,
                fileIds: uploadedFiles.map(file => file.id)
            };

            try {
                const response = await fetch('/api/feedback', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(feedbackData)
                });

                const data = await response.json();

                if (response.ok) {
                    showMessage('反馈提交成功！', 'success');
                    document.getElementById('feedbackForm').reset();
                    uploadedFiles = [];
                    updateFileList();
                    loadFeedbackList();
                } else {
                    showMessage('反馈提交失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('提交错误: ' + error.message, 'error');
            }
        });

        // 加载反馈列表
        async function loadFeedbackList() {
            try {
                const response = await fetch('/api/feedback', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    displayFeedbackList(data.items);
                } else {
                    showMessage('加载反馈列表失败: ' + data.message, 'error');
                }
            } catch (error) {
                showMessage('加载错误: ' + error.message, 'error');
            }
        }

        // 显示反馈列表
        function displayFeedbackList(feedbacks) {
            const feedbackList = document.getElementById('feedbackList');
            
            if (feedbacks.length === 0) {
                feedbackList.innerHTML = '<p>暂无反馈记录</p>';
                return;
            }

            feedbackList.innerHTML = feedbacks.map(feedback => `
                <div class="feedback-item">
                    <div class="feedback-meta">
                        <span class="status-badge status-${feedback.status}">${getStatusText(feedback.status)}</span>
                        <span>类型: ${getTypeText(feedback.type)}</span>
                        <span>分类: ${getCategoryText(feedback.category)}</span>
                        <span>时间: ${new Date(feedback.createdAt).toLocaleString('zh-CN')}</span>
                    </div>
                    ${feedback.title ? `<div style="font-weight: 500; margin-bottom: 5px;">${feedback.title}</div>` : ''}
                    <div class="feedback-content">${feedback.content}</div>
                    ${feedback.files && feedback.files.length > 0 ? `
                        <div style="margin-top: 10px;">
                            <strong>附件:</strong> ${feedback.files.map(f => f.file.originalName).join(', ')}
                        </div>
                    ` : ''}
                </div>
            `).join('');
        }

        // 状态文本映射
        function getStatusText(status) {
            const statusMap = {
                'pending': '待处理',
                'in_progress': '处理中',
                'resolved': '已解决',
                'closed': '已关闭'
            };
            return statusMap[status] || status;
        }

        // 类型文本映射
        function getTypeText(type) {
            const typeMap = {
                'general': '一般反馈',
                'bug': '错误报告',
                'suggestion': '建议',
                'complaint': '投诉',
                'tutor_feedback': '导师反馈',
                'appointment_feedback': '预约反馈'
            };
            return typeMap[type] || type;
        }

        // 分类文本映射
        function getCategoryText(category) {
            const categoryMap = {
                'general': '一般',
                'technical': '技术问题',
                'service': '服务',
                'billing': '计费',
                'ui_ux': '界面体验',
                'performance': '性能'
            };
            return categoryMap[category] || category;
        }

        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 5000);
        }
    </script>
</body>
</html>
