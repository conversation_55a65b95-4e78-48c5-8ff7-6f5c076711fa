import { prisma } from '../lib/prisma';
import bcrypt from 'bcrypt';
import { CreateUserInput, UpdateUserInput, UserResponse } from '../types/user';

export class UserService {
  private static readonly SALT_ROUNDS = 12;

  /**
   * Hash a password
   */
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.SALT_ROUNDS);
  }

  /**
   * Verify a password
   */
  static async verifyPassword(password: string, hashedPassword: string | null): Promise<boolean> {
    if (!hashedPassword) return false;
    return bcrypt.compare(password, hashedPassword);
  }

  /**
   * Create a new user
   */
  static async createUser(input: CreateUserInput): Promise<UserResponse> {
    const hashedPassword = await this.hashPassword(input.password);

    const user = await prisma.user.create({
      data: {
        email: input.email,
        password: hashedPassword,
        name: input.name,
        avatar: input.avatar,
        role: input.role || 'user',
      },
    });

    return this.toUserResponse(user);
  }

  /**
   * Find user by email
   */
  static async findByEmail(email: string): Promise<UserResponse | null> {
    const user = await prisma.user.findUnique({
      where: { email },
    });

    return user ? this.toUserResponse(user) : null;
  }

  /**
   * Find user by email for authentication (includes password)
   */
  static async findByEmailForAuth(email: string): Promise<any | null> {
    const user = await prisma.user.findUnique({
      where: { email },
    });

    return user;
  }

  /**
   * Find user by ID
   */
  static async findById(id: string): Promise<UserResponse | null> {
    const user = await prisma.user.findUnique({
      where: { id },
    });

    return user ? this.toUserResponse(user) : null;
  }

  /**
   * Get user by ID (alias for findById)
   */
  static async getUserById(id: string): Promise<UserResponse | null> {
    return this.findById(id);
  }

  /**
   * Update user
   */
  static async updateUser(id: string, input: UpdateUserInput): Promise<UserResponse> {
    const user = await prisma.user.update({
      where: { id },
      data: input,
    });

    return this.toUserResponse(user);
  }

  /**
   * Delete user
   */
  static async deleteUser(id: string): Promise<void> {
    await prisma.user.delete({
      where: { id },
    });
  }

  /**
   * Get all users (admin only)
   */
  static async getAllUsers(page = 1, limit = 10): Promise<{ users: UserResponse[]; total: number }> {
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.user.count(),
    ]);

    return {
      users: users.map(this.toUserResponse),
      total,
    };
  }

  /**
   * Check if user exists
   */
  static async userExists(email: string): Promise<boolean> {
    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true },
    });
    return !!user;
  }



  /**
   * Enable/disable user
   */
  static async toggleUserStatus(id: string, disabled: boolean): Promise<UserResponse> {
    const user = await prisma.user.update({
      where: { id },
      data: { disabled },
    });

    return this.toUserResponse(user);
  }

  /**
   * Update user role
   */
  static async updateUserRole(id: string, role: string): Promise<UserResponse> {
    const user = await prisma.user.update({
      where: { id },
      data: { role },
    });

    return this.toUserResponse(user);
  }

  /**
   * Update user password
   */
  static async updatePassword(id: string, newPassword: string): Promise<void> {
    const hashedPassword = await this.hashPassword(newPassword);
    await prisma.user.update({
      where: { id },
      data: { password: hashedPassword },
    });
  }

  /**
   * Find user by phone number
   */
  static async findByPhoneNumber(phoneNumber: string): Promise<UserResponse | null> {
    const user = await prisma.user.findUnique({
      where: { phoneNumber },
    });

    return user ? this.toUserResponse(user) : null;
  }

  /**
   * Find user by phone number for authentication (includes password)
   */
  static async findByPhoneNumberForAuth(phoneNumber: string): Promise<any | null> {
    const user = await prisma.user.findUnique({
      where: { phoneNumber },
    });

    return user;
  }

  /**
   * Create user with phone number
   */
  static async createUserWithPhone(input: CreateUserInput & { phoneNumber?: string }): Promise<UserResponse> {
    const hashedPassword = input.password ? await this.hashPassword(input.password) : null;

    const user = await prisma.user.create({
      data: {
        email: input.email,
        password: hashedPassword,
        name: input.name,
        avatar: input.avatar,
        role: input.role || 'user',
        phoneNumber: input.phoneNumber,
        phoneNumberVerified: !!input.phoneNumber, // If phone number is provided, assume it's verified
      },
    });

    return this.toUserResponse(user);
  }

  /**
   * Update user's phone number
   */
  static async updatePhoneNumber(id: string, phoneNumber: string, verified: boolean = false): Promise<UserResponse> {
    const user = await prisma.user.update({
      where: { id },
      data: {
        phoneNumber,
        phoneNumberVerified: verified,
      },
    });

    return this.toUserResponse(user);
  }

  /**
   * Check if phone number exists
   */
  static async phoneNumberExists(phoneNumber: string): Promise<boolean> {
    const user = await prisma.user.findUnique({
      where: { phoneNumber },
      select: { id: true },
    });
    return !!user;
  }

  /**
   * Verify user's phone number
   */
  static async verifyPhoneNumber(id: string): Promise<UserResponse> {
    const user = await prisma.user.update({
      where: { id },
      data: { phoneNumberVerified: true },
    });

    return this.toUserResponse(user);
  }

  /**
   * Bind WeChat account to user
   */
  static async bindWeChatAccount(userId: string, wechatData: {
    openid: string;
    unionid?: string;
    nickname?: string;
    headimgurl?: string;
  }): Promise<void> {
    // This would typically be handled by Better-Auth's account linking
    // For now, we'll store it in the accounts table via Better-Auth
    await prisma.account.create({
      data: {
        userId,
        accountId: wechatData.openid,
        providerId: 'wechat',
        // Store additional WeChat data as JSON in a text field if needed
      },
    });
  }

  /**
   * Find user by WeChat OpenID
   */
  static async findByWeChatOpenId(openid: string): Promise<UserResponse | null> {
    const account = await prisma.account.findUnique({
      where: {
        providerId_accountId: {
          providerId: 'wechat',
          accountId: openid,
        },
      },
      include: {
        user: true,
      },
    });

    return account?.user ? this.toUserResponse(account.user) : null;
  }

  /**
   * Convert database user to response format (updated to include phone number)
   */
  private static toUserResponse(user: any): UserResponse {
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      emailVerified: user.emailVerified,
      disabled: user.disabled,
      role: user.role,
      phoneNumber: user.phoneNumber,
      phoneNumberVerified: user.phoneNumberVerified,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }
}
