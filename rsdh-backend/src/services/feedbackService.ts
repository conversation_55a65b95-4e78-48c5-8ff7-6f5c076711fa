import { prisma } from '../lib/prisma';
import { FileService } from './fileService';

export interface CreateFeedbackData {
  type?: string;
  title?: string;
  content: string;
  contactInfo?: string;
  category?: string;
  relatedTutorId?: string;
  relatedAppointmentId?: string;
  userAgent?: string;
  ipAddress?: string;
  metadata?: any;
}

export interface UpdateFeedbackData {
  status?: string;
  priority?: string;
  assignedToId?: string;
  adminNotes?: string;
}

export interface FeedbackListOptions {
  page?: number;
  limit?: number;
  userId?: string;
  type?: string;
  status?: string;
  priority?: string;
  category?: string;
  assignedToId?: string;
  relatedTutorId?: string;
  relatedAppointmentId?: string;
  search?: string;
}

export interface AttachFileToFeedbackData {
  feedbackId: string;
  fileId: string;
  description?: string;
}

export class FeedbackService {
  /**
   * Create a new feedback
   */
  static async createFeedback(data: CreateFeedbackData, userId: string, fileIds?: string[]) {
    // Validate related entities if provided
    if (data.relatedTutorId) {
      const tutor = await prisma.tutorProfile.findUnique({
        where: { id: data.relatedTutorId }
      });
      if (!tutor) {
        throw new Error('Related tutor not found');
      }
    }

    if (data.relatedAppointmentId) {
      const appointment = await prisma.appointment.findUnique({
        where: { id: data.relatedAppointmentId }
      });
      if (!appointment) {
        throw new Error('Related appointment not found');
      }

      // Verify user has access to this appointment
      if (appointment.studentId !== userId) {
        const tutorProfile = await prisma.tutorProfile.findUnique({
          where: { userId: userId }
        });
        if (!tutorProfile || appointment.tutorId !== tutorProfile.id) {
          throw new Error('You do not have access to this appointment');
        }
      }
    }

    // Validate file IDs if provided
    if (fileIds && fileIds.length > 0) {
      const files = await prisma.file.findMany({
        where: {
          id: { in: fileIds },
          uploadedById: userId
        }
      });

      if (files.length !== fileIds.length) {
        throw new Error('Some files not found or not owned by user');
      }
    }

    // Create feedback with transaction
    const feedback = await prisma.$transaction(async (tx) => {
      const newFeedback = await tx.feedback.create({
        data: {
          userId,
          type: data.type || 'general',
          title: data.title,
          content: data.content,
          contactInfo: data.contactInfo,
          category: data.category || 'general',
          relatedTutorId: data.relatedTutorId,
          relatedAppointmentId: data.relatedAppointmentId,
          userAgent: data.userAgent,
          ipAddress: data.ipAddress,
          metadata: data.metadata
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true
            }
          },
          relatedTutor: {
            select: {
              id: true,
              user: {
                select: {
                  name: true,
                  avatar: true
                }
              }
            }
          },
          relatedAppointment: {
            select: {
              id: true,
              startTime: true,
              endTime: true
            }
          }
        }
      });

      // Attach files if provided
      if (fileIds && fileIds.length > 0) {
        await tx.feedbackFile.createMany({
          data: fileIds.map(fileId => ({
            feedbackId: newFeedback.id,
            fileId
          }))
        });
      }

      return newFeedback;
    });

    return this.getFeedbackById(feedback.id);
  }

  /**
   * Get feedback by ID with full details
   */
  static async getFeedbackById(id: string, userId?: string) {
    const feedback = await prisma.feedback.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true
          }
        },
        relatedTutor: {
          select: {
            id: true,
            user: {
              select: {
                name: true,
                avatar: true
              }
            }
          }
        },
        relatedAppointment: {
          select: {
            id: true,
            startTime: true,
            endTime: true,
            tutor: {
              select: {
                user: {
                  select: {
                    name: true
                  }
                }
              }
            }
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        files: {
          include: {
            file: {
              select: {
                id: true,
                originalName: true,
                fileName: true,
                mimeType: true,
                size: true,
                category: true,
                cdnUrl: true,
                createdAt: true
              }
            }
          }
        }
      }
    });

    if (!feedback) {
      throw new Error('Feedback not found');
    }

    // Check access permissions
    if (userId) {
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Users can only see their own feedback unless they are admin
      if (user.role !== 'admin' && feedback.userId !== userId) {
        throw new Error('Access denied');
      }
    }

    return feedback;
  }

  /**
   * Get feedback list with filtering and pagination
   */
  static async getFeedbackList(options: FeedbackListOptions = {}) {
    const {
      page = 1,
      limit = 20,
      userId,
      type,
      status,
      priority,
      category,
      assignedToId,
      relatedTutorId,
      relatedAppointmentId,
      search
    } = options;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (userId) where.userId = userId;
    if (type) where.type = type;
    if (status) where.status = status;
    if (priority) where.priority = priority;
    if (category) where.category = category;
    if (assignedToId) where.assignedToId = assignedToId;
    if (relatedTutorId) where.relatedTutorId = relatedTutorId;
    if (relatedAppointmentId) where.relatedAppointmentId = relatedAppointmentId;

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } },
        { user: { name: { contains: search, mode: 'insensitive' } } },
        { user: { email: { contains: search, mode: 'insensitive' } } }
      ];
    }

    const [feedback, total] = await Promise.all([
      prisma.feedback.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true
            }
          },
          relatedTutor: {
            select: {
              id: true,
              user: {
                select: {
                  name: true,
                  avatar: true
                }
              }
            }
          },
          relatedAppointment: {
            select: {
              id: true,
              startTime: true,
              endTime: true
            }
          },
          assignedTo: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          files: {
            include: {
              file: {
                select: {
                  id: true,
                  originalName: true,
                  mimeType: true,
                  size: true,
                  category: true
                }
              }
            }
          }
        }
      }),
      prisma.feedback.count({ where })
    ]);

    return {
      items: feedback,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Update feedback (admin only)
   */
  static async updateFeedback(id: string, data: UpdateFeedbackData, adminId: string) {
    // Verify admin permissions
    const admin = await prisma.user.findUnique({
      where: { id: adminId }
    });

    if (!admin || admin.role !== 'admin') {
      throw new Error('Admin access required');
    }

    // Verify feedback exists
    const existingFeedback = await prisma.feedback.findUnique({
      where: { id }
    });

    if (!existingFeedback) {
      throw new Error('Feedback not found');
    }

    // Validate assignedToId if provided
    if (data.assignedToId) {
      const assignee = await prisma.user.findUnique({
        where: { id: data.assignedToId }
      });

      if (!assignee || assignee.role !== 'admin') {
        throw new Error('Assignee must be an admin user');
      }
    }

    const updateData: any = { ...data };

    // Set resolvedAt when status changes to resolved
    if (data.status === 'resolved' && existingFeedback.status !== 'resolved') {
      updateData.resolvedAt = new Date();
    }

    const feedback = await prisma.feedback.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true
          }
        },
        relatedTutor: {
          select: {
            id: true,
            user: {
              select: {
                name: true,
                avatar: true
              }
            }
          }
        },
        relatedAppointment: {
          select: {
            id: true,
            startTime: true,
            endTime: true
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        files: {
          include: {
            file: {
              select: {
                id: true,
                originalName: true,
                fileName: true,
                mimeType: true,
                size: true,
                category: true,
                cdnUrl: true,
                createdAt: true
              }
            }
          }
        }
      }
    });

    return feedback;
  }

  /**
   * Delete feedback (admin only or own feedback if not processed)
   */
  static async deleteFeedback(id: string, userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new Error('User not found');
    }

    const feedback = await prisma.feedback.findUnique({
      where: { id }
    });

    if (!feedback) {
      throw new Error('Feedback not found');
    }

    // Check permissions
    const isAdmin = user.role === 'admin';
    const isOwner = feedback.userId === userId;
    const isUnprocessed = feedback.status === 'pending';

    if (!isAdmin && (!isOwner || !isUnprocessed)) {
      throw new Error('Cannot delete this feedback');
    }

    await prisma.feedback.delete({
      where: { id }
    });

    return { success: true };
  }

  /**
   * Attach file to feedback
   */
  static async attachFileToFeedback(data: AttachFileToFeedbackData, userId: string) {
    // Verify feedback exists and user has access
    const feedback = await prisma.feedback.findUnique({
      where: { id: data.feedbackId }
    });

    if (!feedback) {
      throw new Error('Feedback not found');
    }

    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Check permissions
    const isAdmin = user.role === 'admin';
    const isOwner = feedback.userId === userId;

    if (!isAdmin && !isOwner) {
      throw new Error('Access denied');
    }

    // Verify file exists and user has access
    const file = await prisma.file.findUnique({
      where: { id: data.fileId }
    });

    if (!file) {
      throw new Error('File not found');
    }

    if (!isAdmin && file.uploadedById !== userId) {
      throw new Error('File access denied');
    }

    // Check if file is already attached
    const existingAttachment = await prisma.feedbackFile.findUnique({
      where: {
        feedbackId_fileId: {
          feedbackId: data.feedbackId,
          fileId: data.fileId
        }
      }
    });

    if (existingAttachment) {
      throw new Error('File already attached to this feedback');
    }

    const feedbackFile = await prisma.feedbackFile.create({
      data: {
        feedbackId: data.feedbackId,
        fileId: data.fileId,
        description: data.description
      },
      include: {
        file: {
          select: {
            id: true,
            originalName: true,
            fileName: true,
            mimeType: true,
            size: true,
            category: true,
            cdnUrl: true,
            createdAt: true
          }
        }
      }
    });

    return feedbackFile;
  }

  /**
   * Remove file from feedback
   */
  static async removeFileFromFeedback(feedbackId: string, fileId: string, userId: string) {
    // Verify feedback exists and user has access
    const feedback = await prisma.feedback.findUnique({
      where: { id: feedbackId }
    });

    if (!feedback) {
      throw new Error('Feedback not found');
    }

    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Check permissions
    const isAdmin = user.role === 'admin';
    const isOwner = feedback.userId === userId;

    if (!isAdmin && !isOwner) {
      throw new Error('Access denied');
    }

    // Verify attachment exists
    const feedbackFile = await prisma.feedbackFile.findUnique({
      where: {
        feedbackId_fileId: {
          feedbackId,
          fileId
        }
      }
    });

    if (!feedbackFile) {
      throw new Error('File attachment not found');
    }

    await prisma.feedbackFile.delete({
      where: {
        feedbackId_fileId: {
          feedbackId,
          fileId
        }
      }
    });

    return { success: true };
  }

  /**
   * Get feedback statistics (admin only)
   */
  static async getFeedbackStats(adminId: string) {
    const admin = await prisma.user.findUnique({
      where: { id: adminId }
    });

    if (!admin || admin.role !== 'admin') {
      throw new Error('Admin access required');
    }

    const [
      totalFeedback,
      pendingFeedback,
      inProgressFeedback,
      resolvedFeedback,
      closedFeedback,
      highPriorityFeedback,
      feedbackByType,
      feedbackByCategory,
      recentFeedback
    ] = await Promise.all([
      prisma.feedback.count(),
      prisma.feedback.count({ where: { status: 'pending' } }),
      prisma.feedback.count({ where: { status: 'in_progress' } }),
      prisma.feedback.count({ where: { status: 'resolved' } }),
      prisma.feedback.count({ where: { status: 'closed' } }),
      prisma.feedback.count({ where: { priority: 'high' } }),
      prisma.feedback.groupBy({
        by: ['type'],
        _count: { id: true }
      }),
      prisma.feedback.groupBy({
        by: ['category'],
        _count: { id: true }
      }),
      prisma.feedback.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
          }
        }
      })
    ]);

    return {
      total: totalFeedback,
      pending: pendingFeedback,
      inProgress: inProgressFeedback,
      resolved: resolvedFeedback,
      closed: closedFeedback,
      highPriority: highPriorityFeedback,
      recent: recentFeedback,
      byType: feedbackByType.map(item => ({
        type: item.type,
        count: item._count.id
      })),
      byCategory: feedbackByCategory.map(item => ({
        category: item.category,
        count: item._count.id
      }))
    };
  }
}