import { prisma } from '../lib/prisma';
import { EmailService } from './emailService';
import { FastifyInstance } from 'fastify';
import crypto from 'crypto';
import { fenToYuan } from '../utils/currency';

export interface AppointmentNotificationData {
  appointmentId: string;
  tutorId: string;
  studentId: string;
  appointmentDate: string;
  appointmentTime: string;
  duration: number;
  meetingType: string;
  price: bigint;
  isFree: boolean;
}

export class NotificationService {
  private emailService: EmailService;
  private app: FastifyInstance;

  constructor(app: FastifyInstance) {
    this.app = app;
    this.emailService = new EmailService(app);
  }

  /**
   * Send appointment notification to tutor
   */
  async sendAppointmentNotification(data: AppointmentNotificationData): Promise<void> {
    // Get appointment details with tutor and student info
    const appointment = await prisma.appointment.findUnique({
      where: { id: data.appointmentId },
      include: {
        tutor: {
          include: {
            user: true
          }
        },
        student: true
      }
    });

    if (!appointment) {
      throw new Error('Appointment not found');
    }

    const tutorEmail = appointment.tutor.user.email;
    const tutorName = appointment.tutor.user.name || '导师';
    const studentName = appointment.student.name || '学生';

    let confirmationUrl: string | undefined;

    // Generate confirmation token and URL for paid appointments
    if (!data.isFree) {
      const confirmationToken = this.generateConfirmationToken();
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // 24 hours to confirm

      // Update appointment with confirmation token
      await prisma.appointment.update({
        where: { id: data.appointmentId },
        data: {
          confirmationToken,
          confirmationExpiresAt: expiresAt
        }
      });

      confirmationUrl = `${this.app.config.APP_URL}/api/appointments/${data.appointmentId}/confirm?token=${confirmationToken}`;
    }

    // Send email notification
    await this.emailService.sendAppointmentNotification({
      tutorEmail,
      tutorName,
      studentName,
      appointmentDate: data.appointmentDate,
      appointmentTime: data.appointmentTime,
      duration: data.duration,
      meetingType: data.meetingType,
      price: data.price,
      isFree: data.isFree,
      confirmationUrl
    });

    this.app.log.info(`Appointment notification sent to tutor ${tutorEmail} for appointment ${data.appointmentId}`);
  }

  /**
   * Send appointment confirmation to student
   */
  async sendAppointmentConfirmation(appointmentId: string, status: 'confirmed' | 'rejected'): Promise<void> {
    const appointment = await prisma.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        tutor: {
          include: {
            user: true
          }
        },
        student: true
      }
    });

    if (!appointment) {
      throw new Error('Appointment not found');
    }

    const studentEmail = appointment.student.email;
    const studentName = appointment.student.name || '学生';
    const tutorName = appointment.tutor.user.name || '导师';
    const appointmentDate = appointment.startTime.toLocaleDateString('zh-CN');
    const appointmentTime = appointment.startTime.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });

    await this.emailService.sendAppointmentConfirmation({
      studentEmail,
      studentName,
      tutorName,
      appointmentDate,
      appointmentTime,
      status
    });

    this.app.log.info(`Appointment confirmation sent to student ${studentEmail} for appointment ${appointmentId} with status ${status}`);
  }

  /**
   * Confirm appointment via token
   */
  static async confirmAppointment(appointmentId: string, token: string): Promise<boolean> {
    const appointment = await prisma.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        tutor: {
          include: {
            user: true
          }
        },
        student: true
      }
    });

    if (!appointment) {
      throw new Error('Appointment not found');
    }

    // Check if token is valid and not expired
    if (appointment.confirmationToken !== token) {
      throw new Error('Invalid confirmation token');
    }

    if (!appointment.confirmationExpiresAt || appointment.confirmationExpiresAt < new Date()) {
      throw new Error('Confirmation token has expired');
    }

    // Update appointment status
    await prisma.appointment.update({
      where: { id: appointmentId },
      data: {
        confirmationStatus: 'confirmed',
        confirmationToken: null,
        confirmationExpiresAt: null
      }
    });

    return true;
  }

  /**
   * Reject appointment via token
   */
  static async rejectAppointment(appointmentId: string, token: string): Promise<boolean> {
    const appointment = await prisma.appointment.findUnique({
      where: { id: appointmentId }
    });

    if (!appointment) {
      throw new Error('Appointment not found');
    }

    // Check if token is valid and not expired
    if (appointment.confirmationToken !== token) {
      throw new Error('Invalid confirmation token');
    }

    if (!appointment.confirmationExpiresAt || appointment.confirmationExpiresAt < new Date()) {
      throw new Error('Confirmation token has expired');
    }

    // Update appointment status
    await prisma.appointment.update({
      where: { id: appointmentId },
      data: {
        confirmationStatus: 'rejected',
        status: 'cancelled',
        confirmationToken: null,
        confirmationExpiresAt: null
      }
    });

    // TODO: Process refund if payment was made

    return true;
  }

  /**
   * Generate secure confirmation token
   */
  private generateConfirmationToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Clean up expired confirmation tokens
   */
  static async cleanupExpiredTokens(): Promise<void> {
    const now = new Date();

    await prisma.appointment.updateMany({
      where: {
        confirmationExpiresAt: {
          lt: now
        },
        confirmationStatus: 'pending'
      },
      data: {
        confirmationStatus: 'rejected',
        status: 'cancelled',
        confirmationToken: null,
        confirmationExpiresAt: null
      }
    });
  }
}
