import { prisma } from '../lib/prisma';
import { EducationVerification, CareerVerification } from '@prisma/client';

export interface CreateEducationVerificationData {
  educationId: string;
  fileId: string;
  materialType: string;
  description?: string;
}

export interface CreateCareerVerificationData {
  careerId: string;
  fileId: string;
  materialType: string;
  description?: string;
}

export interface UpdateVerificationStatusData {
  status: 'pending' | 'approved' | 'rejected';
  reviewNotes?: string;
  reviewedById: string;
}

export class VerificationMaterialService {
  /**
   * Add education verification material
   */
  static async addEducationVerification(data: CreateEducationVerificationData): Promise<EducationVerification> {
    // Validate education exists
    const education = await prisma.tutorEducation.findUnique({
      where: { id: data.educationId }
    });

    if (!education) {
      throw new Error('Education record not found');
    }

    // Validate file exists
    const file = await prisma.file.findUnique({
      where: { id: data.fileId }
    });

    if (!file) {
      throw new Error('File not found');
    }

    return await prisma.educationVerification.create({
      data: {
        educationId: data.educationId,
        fileId: data.fileId,
        materialType: data.materialType,
        description: data.description,
      },
      include: {
        file: true,
        education: true,
      }
    });
  }

  /**
   * Add career verification material
   */
  static async addCareerVerification(data: CreateCareerVerificationData): Promise<CareerVerification> {
    // Validate career exists
    const career = await prisma.tutorCareer.findUnique({
      where: { id: data.careerId }
    });

    if (!career) {
      throw new Error('Career record not found');
    }

    // Validate file exists
    const file = await prisma.file.findUnique({
      where: { id: data.fileId }
    });

    if (!file) {
      throw new Error('File not found');
    }

    return await prisma.careerVerification.create({
      data: {
        careerId: data.careerId,
        fileId: data.fileId,
        materialType: data.materialType,
        description: data.description,
      },
      include: {
        file: true,
        career: true,
      }
    });
  }

  /**
   * Get education verification materials
   */
  static async getEducationVerifications(educationId: string): Promise<EducationVerification[]> {
    return await prisma.educationVerification.findMany({
      where: { educationId },
      include: {
        file: true,
        reviewedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
  }

  /**
   * Get career verification materials
   */
  static async getCareerVerifications(careerId: string): Promise<CareerVerification[]> {
    return await prisma.careerVerification.findMany({
      where: { careerId },
      include: {
        file: true,
        reviewedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
  }

  /**
   * Get all verification materials for a tutor
   */
  static async getTutorVerifications(tutorId: string) {
    const educationVerifications = await prisma.educationVerification.findMany({
      where: {
        education: {
          tutorId
        }
      },
      include: {
        file: true,
        education: true,
        reviewedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    const careerVerifications = await prisma.careerVerification.findMany({
      where: {
        career: {
          tutorId
        }
      },
      include: {
        file: true,
        career: true,
        reviewedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return {
      educationVerifications,
      careerVerifications
    };
  }

  /**
   * Update education verification status (admin only)
   */
  static async updateEducationVerificationStatus(
    verificationId: string,
    data: UpdateVerificationStatusData
  ): Promise<EducationVerification> {
    const verification = await prisma.educationVerification.findUnique({
      where: { id: verificationId }
    });

    if (!verification) {
      throw new Error('Education verification not found');
    }

    return await prisma.educationVerification.update({
      where: { id: verificationId },
      data: {
        status: data.status,
        reviewNotes: data.reviewNotes,
        reviewedById: data.reviewedById,
        reviewedAt: new Date(),
      },
      include: {
        file: true,
        education: true,
        reviewedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    });
  }

  /**
   * Update career verification status (admin only)
   */
  static async updateCareerVerificationStatus(
    verificationId: string,
    data: UpdateVerificationStatusData
  ): Promise<CareerVerification> {
    const verification = await prisma.careerVerification.findUnique({
      where: { id: verificationId }
    });

    if (!verification) {
      throw new Error('Career verification not found');
    }

    return await prisma.careerVerification.update({
      where: { id: verificationId },
      data: {
        status: data.status,
        reviewNotes: data.reviewNotes,
        reviewedById: data.reviewedById,
        reviewedAt: new Date(),
      },
      include: {
        file: true,
        career: true,
        reviewedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    });
  }

  /**
   * Delete education verification material
   */
  static async deleteEducationVerification(verificationId: string, userId: string): Promise<void> {
    const verification = await prisma.educationVerification.findUnique({
      where: { id: verificationId },
      include: {
        education: {
          include: {
            tutor: true
          }
        }
      }
    });

    if (!verification) {
      throw new Error('Education verification not found');
    }

    // Check if user owns this verification
    if (verification.education.tutor.userId !== userId) {
      throw new Error('Unauthorized to delete this verification');
    }

    await prisma.educationVerification.delete({
      where: { id: verificationId }
    });
  }

  /**
   * Delete career verification material
   */
  static async deleteCareerVerification(verificationId: string, userId: string): Promise<void> {
    const verification = await prisma.careerVerification.findUnique({
      where: { id: verificationId },
      include: {
        career: {
          include: {
            tutor: true
          }
        }
      }
    });

    if (!verification) {
      throw new Error('Career verification not found');
    }

    // Check if user owns this verification
    if (verification.career.tutor.userId !== userId) {
      throw new Error('Unauthorized to delete this verification');
    }

    await prisma.careerVerification.delete({
      where: { id: verificationId }
    });
  }

  /**
   * Get pending verifications for admin review
   */
  static async getPendingVerifications() {
    const educationVerifications = await prisma.educationVerification.findMany({
      where: { status: 'pending' },
      include: {
        file: true,
        education: {
          include: {
            tutor: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  }
                }
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'asc' }
    });

    const careerVerifications = await prisma.careerVerification.findMany({
      where: { status: 'pending' },
      include: {
        file: true,
        career: {
          include: {
            tutor: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  }
                }
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'asc' }
    });

    return {
      educationVerifications,
      careerVerifications
    };
  }
}
