import nodemailer from 'nodemailer';
import { FastifyInstance } from 'fastify';

export interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

export interface VerificationCodeOptions {
  email: string;
  code: string;
  type: 'registration' | 'password-reset' | 'email-verification';
}

export interface AppointmentNotificationOptions {
  tutorEmail: string;
  tutorName: string;
  studentName: string;
  appointmentDate: string;
  appointmentTime: string;
  duration: number;
  meetingType: string;
  price: number;
  isFree: boolean;
  confirmationUrl?: string;
}

export interface AppointmentConfirmationOptions {
  studentEmail: string;
  studentName: string;
  tutorName: string;
  appointmentDate: string;
  appointmentTime: string;
  status: 'confirmed' | 'rejected';
}

export class EmailService {
  private transporter: nodemailer.Transporter;
  private app: FastifyInstance;

  constructor(app: FastifyInstance) {
    this.app = app;
    this.transporter = this.createTransporter();
  }

  /**
   * Create email transporter based on environment
   */
  private createTransporter(): nodemailer.Transporter {
    // Use environment variables from .env file
    const emailHost = this.app.config.EMAIL_HOST;
    const emailPort = this.app.config.EMAIL_PORT;
    const emailUser = this.app.config.EMAIL_USER;
    const emailPass = this.app.config.EMAIL_PASS;

    // In development, log email configuration
    if (this.app.config.NODE_ENV === 'development') {
      this.app.log.info(`📧 Email configuration: ${emailHost}:${emailPort} (${emailUser})`);
    }

    // Use real SMTP settings from environment
    return nodemailer.createTransport({
      host: emailHost,
      port: parseInt(emailPort?.toString() || '587'),
      secure: emailPort === 465, // Use SSL for port 465
      auth: {
        user: emailUser,
        pass: emailPass,
      },
    });
  }

  /**
   * Send email
   */
  async sendEmail(options: EmailOptions): Promise<void> {
    try {
      // In development, just log the email instead of sending
      if (this.app.config.NODE_ENV === 'development') {
        this.app.log.info(`📧 Email would be sent to: ${options.to}`);
        this.app.log.info(`📧 Subject: ${options.subject}`);
        this.app.log.info(`📧 Content: ${options.text || 'HTML content'}`);
        return;
      }

      const info = await this.transporter.sendMail({
        from: this.app.config.EMAIL_FROM || `"${this.app.config.APP_NAME}" <${this.app.config.EMAIL_USER}>`,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
      });

      this.app.log.info(`Email sent: ${info.messageId}`);
    } catch (error) {
      this.app.log.error('Failed to send email:', error);
      throw new Error('Failed to send email');
    }
  }

  /**
   * Send verification code email
   */
  async sendVerificationCode(options: VerificationCodeOptions): Promise<void> {
    const { email, code, type } = options;

    let subject: string;
    let html: string;
    let text: string;

    switch (type) {
      case 'registration':
        subject = '欢迎注册 - 验证您的邮箱';
        html = this.getRegistrationEmailTemplate(code);
        text = `欢迎注册！您的验证码是：${code}。此验证码将在10分钟后过期。`;
        break;

      case 'password-reset':
        subject = '重置密码 - 验证码';
        html = this.getPasswordResetEmailTemplate(code);
        text = `您请求重置密码。您的验证码是：${code}。此验证码将在10分钟后过期。如果您没有请求重置密码，请忽略此邮件。`;
        break;

      case 'email-verification':
        subject = '邮箱验证 - 验证码';
        html = this.getEmailVerificationTemplate(code);
        text = `请验证您的邮箱地址。您的验证码是：${code}。此验证码将在10分钟后过期。`;
        break;

      default:
        throw new Error('Invalid verification code type');
    }

    await this.sendEmail({
      to: email,
      subject,
      html,
      text,
    });
  }

  /**
   * Generate registration email template
   */
  private getRegistrationEmailTemplate(code: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>欢迎注册</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .code { font-size: 24px; font-weight: bold; color: #007bff; text-align: center; padding: 20px; background: white; border: 2px dashed #007bff; margin: 20px 0; }
          .footer { text-align: center; color: #666; font-size: 12px; padding: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>欢迎注册人生导航</h1>
          </div>
          <div class="content">
            <p>感谢您注册我们的服务！</p>
            <p>请使用以下验证码完成注册：</p>
            <div class="code">${code}</div>
            <p>此验证码将在 <strong>10分钟</strong> 后过期。</p>
            <p>如果您没有注册账户，请忽略此邮件。</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate password reset email template
   */
  private getPasswordResetEmailTemplate(code: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>重置密码</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .code { font-size: 24px; font-weight: bold; color: #dc3545; text-align: center; padding: 20px; background: white; border: 2px dashed #dc3545; margin: 20px 0; }
          .footer { text-align: center; color: #666; font-size: 12px; padding: 20px; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 15px 0; border-radius: 5px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>重置密码</h1>
          </div>
          <div class="content">
            <p>您请求重置密码。</p>
            <p>请使用以下验证码重置您的密码：</p>
            <div class="code">${code}</div>
            <p>此验证码将在 <strong>10分钟</strong> 后过期。</p>
            <div class="warning">
              <strong>安全提醒：</strong>如果您没有请求重置密码，请立即忽略此邮件并检查您的账户安全。
            </div>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate email verification template
   */
  private getEmailVerificationTemplate(code: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>邮箱验证</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #28a745; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .code { font-size: 24px; font-weight: bold; color: #28a745; text-align: center; padding: 20px; background: white; border: 2px dashed #28a745; margin: 20px 0; }
          .footer { text-align: center; color: #666; font-size: 12px; padding: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>验证邮箱地址</h1>
          </div>
          <div class="content">
            <p>请验证您的邮箱地址以完成设置。</p>
            <p>请使用以下验证码：</p>
            <div class="code">${code}</div>
            <p>此验证码将在 <strong>10分钟</strong> 后过期。</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate random verification code
   */
  static generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Send appointment notification to tutor
   */
  async sendAppointmentNotification(options: AppointmentNotificationOptions): Promise<void> {
    const { tutorEmail, tutorName, studentName, appointmentDate, appointmentTime,
            duration, meetingType, price, isFree, confirmationUrl } = options;

    const subject = isFree ? '新的免费咨询预约' : '新的付费咨询预约 - 需要确认';

    let html: string;
    let text: string;

    if (isFree) {
      // Free appointment - direct notification
      html = this.getFreeAppointmentNotificationTemplate({
        tutorName, studentName, appointmentDate, appointmentTime, duration, meetingType
      });
      text = `您好 ${tutorName}，您有一个新的免费咨询预约。学生：${studentName}，时间：${appointmentDate} ${appointmentTime}，时长：${duration}分钟，方式：${meetingType}。`;
    } else {
      // Paid appointment - requires confirmation
      html = this.getPaidAppointmentNotificationTemplate({
        tutorName, studentName, appointmentDate, appointmentTime, duration, meetingType, price, confirmationUrl
      });
      text = `您好 ${tutorName}，您有一个新的付费咨询预约需要确认。学生：${studentName}，时间：${appointmentDate} ${appointmentTime}，费用：¥${price}。请点击确认链接：${confirmationUrl}`;
    }

    await this.sendEmail({
      to: tutorEmail,
      subject,
      html,
      text
    });
  }

  /**
   * Send appointment confirmation to student
   */
  async sendAppointmentConfirmation(options: AppointmentConfirmationOptions): Promise<void> {
    const { studentEmail, studentName, tutorName, appointmentDate, appointmentTime, status } = options;

    const subject = status === 'confirmed' ? '预约已确认' : '预约被拒绝';
    const html = this.getAppointmentConfirmationTemplate({
      studentName, tutorName, appointmentDate, appointmentTime, status
    });
    const text = status === 'confirmed'
      ? `您好 ${studentName}，您与 ${tutorName} 的预约已确认。时间：${appointmentDate} ${appointmentTime}。`
      : `您好 ${studentName}，很抱歉，您与 ${tutorName} 的预约被拒绝。时间：${appointmentDate} ${appointmentTime}。`;

    await this.sendEmail({
      to: studentEmail,
      subject,
      html,
      text
    });
  }

  /**
   * Generate free appointment notification template
   */
  private getFreeAppointmentNotificationTemplate(data: {
    tutorName: string;
    studentName: string;
    appointmentDate: string;
    appointmentTime: string;
    duration: number;
    meetingType: string;
  }): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>新的免费咨询预约</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #28a745; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .appointment-details { background: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .footer { text-align: center; color: #666; font-size: 12px; padding: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>新的免费咨询预约</h1>
          </div>
          <div class="content">
            <p>您好 ${data.tutorName}，</p>
            <p>您有一个新的免费咨询预约：</p>
            <div class="appointment-details">
              <h3>预约详情</h3>
              <p><strong>学生：</strong> ${data.studentName}</p>
              <p><strong>日期：</strong> ${data.appointmentDate}</p>
              <p><strong>时间：</strong> ${data.appointmentTime}</p>
              <p><strong>时长：</strong> ${data.duration} 分钟</p>
              <p><strong>方式：</strong> ${data.meetingType === 'online' ? '在线咨询' : '线下咨询'}</p>
            </div>
            <p>由于这是免费咨询，预约已自动确认。请准时参加咨询。</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate paid appointment notification template
   */
  private getPaidAppointmentNotificationTemplate(data: {
    tutorName: string;
    studentName: string;
    appointmentDate: string;
    appointmentTime: string;
    duration: number;
    meetingType: string;
    price: number;
    confirmationUrl?: string;
  }): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>新的付费咨询预约</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .appointment-details { background: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .confirm-button { background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
          .footer { text-align: center; color: #666; font-size: 12px; padding: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>新的付费咨询预约</h1>
          </div>
          <div class="content">
            <p>您好 ${data.tutorName}，</p>
            <p>您有一个新的付费咨询预约需要确认：</p>
            <div class="appointment-details">
              <h3>预约详情</h3>
              <p><strong>学生：</strong> ${data.studentName}</p>
              <p><strong>日期：</strong> ${data.appointmentDate}</p>
              <p><strong>时间：</strong> ${data.appointmentTime}</p>
              <p><strong>时长：</strong> ${data.duration} 分钟</p>
              <p><strong>方式：</strong> ${data.meetingType === 'online' ? '在线咨询' : '线下咨询'}</p>
              <p><strong>费用：</strong> ¥${data.price}</p>
            </div>
            <p>学生已完成支付，请点击下方链接确认预约：</p>
            ${data.confirmationUrl ? `<a href="${data.confirmationUrl}" class="confirm-button">确认预约</a>` : ''}
            <p><small>如果您无法点击按钮，请复制以下链接到浏览器：<br>${data.confirmationUrl}</small></p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate appointment confirmation template
   */
  private getAppointmentConfirmationTemplate(data: {
    studentName: string;
    tutorName: string;
    appointmentDate: string;
    appointmentTime: string;
    status: 'confirmed' | 'rejected';
  }): string {
    const isConfirmed = data.status === 'confirmed';
    const headerColor = isConfirmed ? '#28a745' : '#dc3545';
    const title = isConfirmed ? '预约已确认' : '预约被拒绝';

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${title}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: ${headerColor}; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .appointment-details { background: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .footer { text-align: center; color: #666; font-size: 12px; padding: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${title}</h1>
          </div>
          <div class="content">
            <p>您好 ${data.studentName}，</p>
            ${isConfirmed
              ? `<p>您与 ${data.tutorName} 的预约已确认！</p>`
              : `<p>很抱歉，您与 ${data.tutorName} 的预约被拒绝。</p>`
            }
            <div class="appointment-details">
              <h3>预约详情</h3>
              <p><strong>导师：</strong> ${data.tutorName}</p>
              <p><strong>日期：</strong> ${data.appointmentDate}</p>
              <p><strong>时间：</strong> ${data.appointmentTime}</p>
            </div>
            ${isConfirmed
              ? '<p>请准时参加咨询。如有任何问题，请联系客服。</p>'
              : '<p>如有疑问，请联系客服。我们会为您安排其他合适的导师。</p>'
            }
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}