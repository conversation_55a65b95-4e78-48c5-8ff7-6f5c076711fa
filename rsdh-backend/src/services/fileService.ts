import { prisma } from '../lib/prisma';
import { S3Service } from './s3Service';
import { FastifyInstance } from 'fastify';
import * as path from 'path';
import { lookup } from 'mime-types';

export interface FileUploadData {
  originalName: string;
  buffer: Buffer;
  mimeType: string;
  size: number;
  category?: string;
  isPublic?: boolean;
  metadata?: any;
}

export interface FileRecord {
  id: string;
  originalName: string;
  fileName: string;
  mimeType: string;
  size: number;
  s3Key: string;
  s3Bucket: string;
  s3Region: string;
  cdnUrl: string | null;
  uploadedById: string;
  category: string;
  isPublic: boolean;
  metadata: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface FileListOptions {
  userId?: string;
  category?: string;
  isPublic?: boolean;
  page?: number;
  limit?: number;
  search?: string;
}

export class FileService {
  private s3Service: S3Service;
  private fastify: FastifyInstance;

  constructor(fastify: FastifyInstance, s3Service: S3Service) {
    this.fastify = fastify;
    this.s3Service = s3Service;
  }

  /**
   * Validate file type and size
   */
  validateFile(file: FileUploadData): { valid: boolean; error?: string } {
    const allowedTypes = this.fastify.config.ALLOWED_FILE_TYPES.split(',');
    const maxSize = this.fastify.config.MAX_FILE_SIZE;

    if (!allowedTypes.includes(file.mimeType)) {
      return {
        valid: false,
        error: `File type ${file.mimeType} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
      };
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size ${file.size} bytes exceeds maximum allowed size of ${maxSize} bytes`
      };
    }

    return { valid: true };
  }

  /**
   * Upload file and save record to database
   */
  async uploadFile(userId: string, fileData: FileUploadData): Promise<FileRecord> {
    // Validate file
    const validation = this.validateFile(fileData);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    // Generate S3 key
    const category = fileData.category || 'general';
    const s3Key = this.s3Service.generateFileKey(fileData.originalName, category);

    // Upload to S3 (or mock for development)
    let uploadResult;
    try {
      uploadResult = await this.s3Service.uploadFile(
        fileData.buffer,
        fileData.originalName,
        {
          key: s3Key,
          contentType: fileData.mimeType,
          isPublic: fileData.isPublic || false,
          metadata: fileData.metadata ? { custom: JSON.stringify(fileData.metadata) } : undefined,
        }
      );
    } catch (error) {
      // For development, create a mock upload result if S3 is not configured
      console.warn('S3 upload failed, using mock result for development:', error);
      uploadResult = {
        key: s3Key,
        url: `http://localhost:3001/mock-files/${s3Key}`,
        size: fileData.size,
      };
    }

    // Save to database
    const fileRecord = await prisma.file.create({
      data: {
        originalName: fileData.originalName,
        fileName: path.basename(s3Key),
        mimeType: fileData.mimeType,
        size: fileData.size,
        s3Key: uploadResult.key,
        s3Bucket: this.s3Service['config']?.bucketName || 'mock-bucket',
        s3Region: this.s3Service['config']?.region || 'mock-region',
        cdnUrl: this.s3Service['config']?.cdnBaseUrl ? uploadResult.url : null,
        uploadedById: userId,
        category,
        isPublic: fileData.isPublic || false,
        metadata: fileData.metadata || null,
      },
    });

    return fileRecord as FileRecord;
  }

  /**
   * Get file by ID
   */
  async getFileById(fileId: string, userId?: string): Promise<FileRecord | null> {
    const file = await prisma.file.findUnique({
      where: { id: fileId },
    });

    if (!file) {
      return null;
    }

    // Check access permissions
    if (!file.isPublic && userId && file.uploadedById !== userId) {
      throw new Error('Access denied');
    }

    return file as FileRecord;
  }

  /**
   * Get file access URL
   */
  async getFileUrl(fileId: string, userId?: string, expiresIn: number = 3600): Promise<string> {
    const file = await this.getFileById(fileId, userId);
    if (!file) {
      throw new Error('File not found');
    }

    // If file is public and CDN is available, return CDN URL
    if (file.isPublic && file.cdnUrl) {
      return file.cdnUrl;
    }

    // If file is public, return direct S3 URL
    if (file.isPublic) {
      return this.s3Service.getFileUrl(file.s3Key);
    }

    // For private files, generate presigned URL
    return await this.s3Service.getPresignedDownloadUrl(file.s3Key, expiresIn);
  }

  /**
   * Delete file
   */
  async deleteFile(fileId: string, userId: string): Promise<void> {
    const file = await prisma.file.findUnique({
      where: { id: fileId },
    });

    if (!file) {
      throw new Error('File not found');
    }

    // Check ownership
    if (file.uploadedById !== userId) {
      throw new Error('Access denied');
    }

    // Delete from S3 (or skip for mock)
    try {
      await this.s3Service.deleteFile(file.s3Key);
    } catch (error) {
      console.warn('S3 delete failed, continuing with database deletion:', error);
    }

    // Delete from database
    await prisma.file.delete({
      where: { id: fileId },
    });
  }

  /**
   * List files with pagination and filters
   */
  async listFiles(options: FileListOptions = {}): Promise<{
    files: FileRecord[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      userId,
      category,
      isPublic,
      page = 1,
      limit = 20,
      search,
    } = options;

    const where: any = {};

    if (userId) {
      where.uploadedById = userId;
    }

    if (category) {
      where.category = category;
    }

    if (typeof isPublic === 'boolean') {
      where.isPublic = isPublic;
    }

    if (search) {
      where.OR = [
        { originalName: { contains: search, mode: 'insensitive' } },
        { fileName: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [files, total] = await Promise.all([
      prisma.file.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.file.count({ where }),
    ]);

    return {
      files: files as FileRecord[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Get user's storage usage
   */
  async getUserStorageUsage(userId: string): Promise<{
    totalFiles: number;
    totalSize: number;
    sizeByCategory: Record<string, { count: number; size: number }>;
  }> {
    const files = await prisma.file.findMany({
      where: { uploadedById: userId },
      select: { size: true, category: true },
    });

    const totalFiles = files.length;
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);

    const sizeByCategory: Record<string, { count: number; size: number }> = {};
    files.forEach(file => {
      if (!sizeByCategory[file.category]) {
        sizeByCategory[file.category] = { count: 0, size: 0 };
      }
      sizeByCategory[file.category].count++;
      sizeByCategory[file.category].size += file.size;
    });

    return {
      totalFiles,
      totalSize,
      sizeByCategory,
    };
  }

  /**
   * Generate presigned upload URL for direct client upload
   */
  async generateUploadUrl(
    userId: string,
    fileName: string,
    contentType: string,
    category: string = 'general',
    expiresIn: number = 3600
  ): Promise<{
    uploadUrl: string;
    key: string;
    fileId: string;
  }> {
    // Validate content type
    const allowedTypes = this.fastify.config.ALLOWED_FILE_TYPES.split(',');
    if (!allowedTypes.includes(contentType)) {
      throw new Error(`Content type ${contentType} is not allowed`);
    }

    // Generate S3 key
    const s3Key = this.s3Service.generateFileKey(fileName, category);

    // Create file record in pending state
    const fileRecord = await prisma.file.create({
      data: {
        originalName: fileName,
        fileName: path.basename(s3Key),
        mimeType: contentType,
        size: 0, // Will be updated after upload
        s3Key,
        s3Bucket: this.s3Service['config'].bucketName,
        s3Region: this.s3Service['config'].region,
        cdnUrl: null,
        uploadedById: userId,
        category,
        isPublic: false,
        metadata: { status: 'pending' },
      },
    });

    // Generate presigned URL
    const uploadUrl = await this.s3Service.getPresignedUploadUrl(
      s3Key,
      contentType,
      expiresIn
    );

    return {
      uploadUrl,
      key: s3Key,
      fileId: fileRecord.id,
    };
  }

  /**
   * Confirm upload completion and update file record
   */
  async confirmUpload(fileId: string, userId: string): Promise<FileRecord> {
    const file = await prisma.file.findUnique({
      where: { id: fileId },
    });

    if (!file || file.uploadedById !== userId) {
      throw new Error('File not found or access denied');
    }

    // Get file info from S3
    const s3Info = await this.s3Service.getFileInfo(file.s3Key);
    if (!s3Info) {
      throw new Error('File not found in storage');
    }

    // Update file record
    const updatedFile = await prisma.file.update({
      where: { id: fileId },
      data: {
        size: s3Info.size,
        cdnUrl: this.s3Service['config'].cdnBaseUrl
          ? this.s3Service.getFileUrl(file.s3Key)
          : null,
        metadata: { status: 'completed' },
      },
    });

    return updatedFile as FileRecord;
  }
}
