import { FastifyInstance } from 'fastify';
import { PaymentService } from './paymentService';
import { NotificationService } from './notificationService';

export class SchedulerService {
  private app: FastifyInstance;
  private intervals: NodeJS.Timeout[] = [];

  constructor(app: FastifyInstance) {
    this.app = app;
  }

  /**
   * Start all scheduled tasks
   */
  start(): void {
    this.app.log.info('Starting scheduler service...');

    // Process payment splits every hour
    const paymentSplitInterval = setInterval(async () => {
      try {
        await PaymentService.processPendingSplits();
        this.app.log.info('Payment splits processed successfully');
      } catch (error) {
        this.app.log.error('Error processing payment splits:', error);
      }
    }, 60 * 60 * 1000); // 1 hour

    // Clean up expired confirmation tokens every 30 minutes
    const tokenCleanupInterval = setInterval(async () => {
      try {
        await NotificationService.cleanupExpiredTokens();
        this.app.log.info('Expired confirmation tokens cleaned up');
      } catch (error) {
        this.app.log.error('Error cleaning up expired tokens:', error);
      }
    }, 30 * 60 * 1000); // 30 minutes

    this.intervals.push(paymentSplitInterval, tokenCleanupInterval);
    this.app.log.info('Scheduler service started');
  }

  /**
   * Stop all scheduled tasks
   */
  stop(): void {
    this.app.log.info('Stopping scheduler service...');
    
    this.intervals.forEach(interval => {
      clearInterval(interval);
    });
    
    this.intervals = [];
    this.app.log.info('Scheduler service stopped');
  }

  /**
   * Manually trigger payment split processing
   */
  async triggerPaymentSplitProcessing(): Promise<void> {
    this.app.log.info('Manually triggering payment split processing...');
    try {
      await PaymentService.processPendingSplits();
      this.app.log.info('Manual payment split processing completed');
    } catch (error) {
      this.app.log.error('Error in manual payment split processing:', error);
      throw error;
    }
  }

  /**
   * Manually trigger token cleanup
   */
  async triggerTokenCleanup(): Promise<void> {
    this.app.log.info('Manually triggering token cleanup...');
    try {
      await NotificationService.cleanupExpiredTokens();
      this.app.log.info('Manual token cleanup completed');
    } catch (error) {
      this.app.log.error('Error in manual token cleanup:', error);
      throw error;
    }
  }
}
