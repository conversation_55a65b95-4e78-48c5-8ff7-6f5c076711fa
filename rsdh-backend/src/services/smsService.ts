import { FastifyInstance } from 'fastify';

export interface SMSConfig {
  provider: 'aliyun' | 'tencent' | 'mock';
  accessKeyId?: string;
  accessKeySecret?: string;
  signName?: string;
  templateCode?: string;
}

export interface SendSMSParams {
  phoneNumber: string;
  code: string;
  templateParams?: Record<string, string>;
}

export class SMSService {
  private config: SMSConfig;
  private app: FastifyInstance;

  constructor(app: FastifyInstance) {
    this.app = app;
    this.config = {
      provider: (app.config.SMS_PROVIDER as 'aliyun' | 'tencent' | 'mock') || 'mock',
      accessKeyId: app.config.SMS_ACCESS_KEY_ID,
      accessKeySecret: app.config.SMS_ACCESS_KEY_SECRET,
      signName: app.config.SMS_SIGN_NAME || '人生导航',
      templateCode: app.config.SMS_TEMPLATE_CODE || 'SMS_123456789',
    };
  }

  /**
   * Send SMS verification code
   */
  async sendVerificationCode(params: SendSMSParams): Promise<boolean> {
    const { phoneNumber, code } = params;

    try {
      switch (this.config.provider) {
        case 'aliyun':
          return await this.sendAliyunSMS(phoneNumber, code);
        case 'tencent':
          return await this.sendTencentSMS(phoneNumber, code);
        case 'mock':
        default:
          return await this.sendMockSMS(phoneNumber, code);
      }
    } catch (error) {
      this.app.log.error('Failed to send SMS:', error);
      return false;
    }
  }

  /**
   * Send SMS using Aliyun SMS service
   */
  private async sendAliyunSMS(phoneNumber: string, code: string): Promise<boolean> {
    // TODO: Implement Aliyun SMS integration
    // This would require installing @alicloud/sms20170525 package
    this.app.log.info(`[Aliyun SMS] Would send code ${code} to ${phoneNumber}`);

    // For now, return true in development mode
    if (this.app.config.NODE_ENV === 'development') {
      this.app.log.info(`[Development] SMS code for ${phoneNumber}: ${code}`);
      return true;
    }

    throw new Error('Aliyun SMS not implemented yet');
  }

  /**
   * Send SMS using Tencent Cloud SMS service
   */
  private async sendTencentSMS(phoneNumber: string, code: string): Promise<boolean> {
    // TODO: Implement Tencent Cloud SMS integration
    // This would require installing tencentcloud-sdk-nodejs package
    this.app.log.info(`[Tencent SMS] Would send code ${code} to ${phoneNumber}`);

    // For now, return true in development mode
    if (this.app.config.NODE_ENV === 'development') {
      this.app.log.info(`[Development] SMS code for ${phoneNumber}: ${code}`);
      return true;
    }

    throw new Error('Tencent SMS not implemented yet');
  }

  /**
   * Mock SMS service for development and testing
   */
  private async sendMockSMS(phoneNumber: string, code: string): Promise<boolean> {
    this.app.log.info(`[Mock SMS] Sending code ${code} to ${phoneNumber}`);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100));

    // In development, log the code to console
    if (this.app.config.NODE_ENV === 'development') {
      console.log(`📱 SMS Verification Code for ${phoneNumber}: ${code}`);
    }

    return true;
  }

  /**
   * Validate phone number format
   */
  static validatePhoneNumber(phoneNumber: string): boolean {
    // Check for invalid characters first (only allow digits, +, -, space, parentheses)
    const allowedCharsRegex = /^[\d+\-\s()]+$/;
    if (!allowedCharsRegex.test(phoneNumber)) {
      return false;
    }

    // Then normalize the phone number to remove formatting
    const normalized = this.normalizePhoneNumber(phoneNumber);

    // Basic validation for Chinese mobile numbers
    // Supports formats: +86xxxxxxxxxx, 86xxxxxxxxxx, 1xxxxxxxxxx
    const phoneRegex = /^(\+?86)?1[3-9]\d{9}$/;
    return phoneRegex.test(normalized);
  }

  /**
   * Normalize phone number to standard format
   */
  static normalizePhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters except +
    let normalized = phoneNumber.replace(/[^\d+]/g, '');

    // Handle different formats
    if (normalized.startsWith('+86')) {
      return normalized;
    } else if (normalized.startsWith('86') && normalized.length === 13) {
      return '+' + normalized;
    } else if (normalized.startsWith('1') && normalized.length === 11) {
      return '+86' + normalized;
    }

    return normalized;
  }
}
