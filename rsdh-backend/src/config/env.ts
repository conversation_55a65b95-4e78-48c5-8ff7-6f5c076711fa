import { FastifyPluginAsync } from 'fastify';
import fp from 'fastify-plugin';
import fastifyEnv from '@fastify/env';
import { isEmail } from 'validator';

const validateEmail = (email: string, fieldName: string) => {
  if (email && !isEmail(email)) {
    throw new Error(`${fieldName} must be a valid email address`);
  }
  return true;
};

declare module 'fastify' {
  interface FastifyInstance {
    config: {
      DATABASE_URL: string;
      NODE_ENV: 'development' | 'production' | 'test';
      PORT: number;
      APP_NAME: string;
      APP_URL: string;
      CORS_ORIGIN: string;
      TRUSTED_ORIGINS: string;
      AUTH_URL: string;
      JWT_SECRET: string;
      JWT_EXPIRES_IN: string;
      REFRESH_TOKEN_EXPIRES_IN: string;
      ADMIN_EMAIL: string;
      ADMIN_PASSWORD: string;
      TEST_EMAIL: string;
      TEST_PASSWORD: string;
      SUPPORT_EMAIL: string;
      EMAIL_HOST: string;
      EMAIL_PORT: number;
      EMAIL_USER: string;
      EMAIL_PASS: string;
      EMAIL_FROM: string;
      // SMS Configuration
      SMS_PROVIDER: string;
      SMS_ACCESS_KEY_ID?: string;
      SMS_ACCESS_KEY_SECRET?: string;
      SMS_SIGN_NAME: string;
      SMS_TEMPLATE_CODE: string;
      // WeChat OAuth Configuration
      WECHAT_APP_ID?: string;
      WECHAT_APP_SECRET?: string;
      // WeChat Payment Configuration
      WECHAT_PAY_APP_ID?: string;
      WECHAT_PAY_MCH_ID?: string;
      WECHAT_PAY_API_KEY?: string;
      WECHAT_PAY_CERT_PATH?: string;
      WECHAT_PAY_KEY_PATH?: string;
      WECHAT_PAY_NOTIFY_URL?: string;
      // Payment Split Configuration
      TUTOR_COMMISSION_RATE: number; // Percentage for tutor (e.g., 0.7 for 70%)
      // S3 Storage Configuration
      S3_PROVIDER: string;
      S3_ENDPOINT?: string;
      S3_REGION: string;
      S3_ACCESS_KEY_ID: string;
      S3_SECRET_ACCESS_KEY: string;
      S3_BUCKET_NAME: string;
      S3_FORCE_PATH_STYLE?: boolean;
      CDN_BASE_URL?: string;
      // File Upload Configuration
      MAX_FILE_SIZE: number;
      ALLOWED_FILE_TYPES: string;
    };
  }
}

const schema = {
  type: 'object',
  required: [ 'DATABASE_URL', 'JWT_SECRET', 'ADMIN_EMAIL', 'ADMIN_PASSWORD', 'EMAIL_HOST', 'EMAIL_USER', 'EMAIL_PASS', 'S3_REGION', 'S3_ACCESS_KEY_ID', 'S3_SECRET_ACCESS_KEY', 'S3_BUCKET_NAME' ],
  properties: {
    DATABASE_URL: { type: 'string', default: 'postgresql://postgres:postgres@localhost:5432/rsdh_dev' },
    NODE_ENV: { type: 'string', default: 'development' },
    PORT: { type: 'number', default: 3001 },
    APP_NAME: { type: 'string', default: 'Life Navigation' },
    APP_URL: { type: 'string', default: 'http://localhost:3000' },
    CORS_ORIGIN: { type: 'string', default: 'http://localhost:3000' },
    TRUSTED_ORIGINS: { type: 'string', default: 'http://localhost:3000' },
    AUTH_URL: { type: 'string', default: 'http://localhost:3000' },
    JWT_SECRET: { type: 'string', default: 'your-secret-key-here' },

    // SMS Configuration
    SMS_PROVIDER: { type: 'string', default: 'mock' }, // aliyun, tencent, mock
    SMS_ACCESS_KEY_ID: { type: 'string' },
    SMS_ACCESS_KEY_SECRET: { type: 'string' },
    SMS_SIGN_NAME: { type: 'string', default: '人生导航' },
    SMS_TEMPLATE_CODE: { type: 'string', default: 'SMS_123456789' },

    // WeChat OAuth Configuration
    WECHAT_APP_ID: { type: 'string' },
    WECHAT_APP_SECRET: { type: 'string' },

    // WeChat Payment Configuration
    WECHAT_PAY_APP_ID: { type: 'string' },
    WECHAT_PAY_MCH_ID: { type: 'string' },
    WECHAT_PAY_API_KEY: { type: 'string' },
    WECHAT_PAY_CERT_PATH: { type: 'string' },
    WECHAT_PAY_KEY_PATH: { type: 'string' },
    WECHAT_PAY_NOTIFY_URL: { type: 'string' },

    // Payment Split Configuration
    TUTOR_COMMISSION_RATE: { type: 'number', default: 0.7 }, // 70% to tutor by default
    JWT_EXPIRES_IN: { type: 'string', default: '1d' },
    REFRESH_TOKEN_EXPIRES_IN: { type: 'string', default: '7d' },
    ADMIN_EMAIL: { type: 'string', default: '<EMAIL>' },
    ADMIN_PASSWORD: { type: 'string' },
    TEST_EMAIL: { type: 'string', default: '<EMAIL>' },
    TEST_PASSWORD: { type: 'string' },
    SUPPORT_EMAIL: { type: 'string', default: '<EMAIL>' },
    EMAIL_HOST: { type: 'string' },
    EMAIL_PORT: { type: 'number', default: 587 },
    EMAIL_USER: { type: 'string' },
    EMAIL_PASS: { type: 'string' },
    EMAIL_FROM: { type: 'string' },

    // S3 Storage Configuration
    S3_PROVIDER: { type: 'string', default: 'aws' }, // aws, backblaze, aliyun, tencent, minio
    S3_ENDPOINT: { type: 'string' }, // Custom endpoint for non-AWS providers
    S3_REGION: { type: 'string', default: 'us-east-1' },
    S3_ACCESS_KEY_ID: { type: 'string' },
    S3_SECRET_ACCESS_KEY: { type: 'string' },
    S3_BUCKET_NAME: { type: 'string' },
    S3_FORCE_PATH_STYLE: { type: 'boolean', default: false }, // Required for MinIO and some providers
    CDN_BASE_URL: { type: 'string' }, // CDN URL for faster access

    // File Upload Configuration
    MAX_FILE_SIZE: { type: 'number', default: 10485760 }, // 10MB in bytes
    ALLOWED_FILE_TYPES: { type: 'string', default: 'image/jpeg,image/png,image/gif,image/webp,video/mp4,video/webm,audio/mp3,audio/wav,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document' },
  }
};

const envPlugin: FastifyPluginAsync = fp(async (fastify) => {
  await fastify.register(fastifyEnv, {
    confKey: 'config',
    schema: schema,
    dotenv: true
  });

  // Post-validation logic
  validateEmail(fastify.config.ADMIN_EMAIL, 'ADMIN_EMAIL');
  if (fastify.config.TEST_EMAIL) {
    validateEmail(fastify.config.TEST_EMAIL, 'TEST_EMAIL');
  }

  if (fastify.config.ADMIN_PASSWORD && fastify.config.ADMIN_PASSWORD.length < 8) {
    throw new Error('ADMIN_PASSWORD must be at least 8 characters long');
  }
});

export default envPlugin;

