import { betterAuth } from 'better-auth';
import { prismaAdapter } from 'better-auth/adapters/prisma';
import { phoneNumber, genericOAuth } from 'better-auth/plugins';
import { prisma } from './prisma';
import { SMSService } from '../services/smsService';

const trustedOrigins = process.env.TRUSTED_ORIGINS?.split(',') || [];

export const auth = betterAuth({
  trustedOrigins,
  database: prismaAdapter(prisma, { provider: 'postgresql' }),
  plugins: [
    // Phone number authentication plugin
    phoneNumber({
      sendOTP: async ({ phoneNumber, code }, request): Promise<void> => {
        try {
          // Create a mock fastify instance for SMS service
          const mockApp = {
            config: {
              NODE_ENV: process.env.NODE_ENV || 'development',
              SMS_PROVIDER: process.env.SMS_PROVIDER || 'mock',
              SMS_ACCESS_KEY_ID: process.env.SMS_ACCESS_KEY_ID,
              SMS_ACCESS_KEY_SECRET: process.env.SMS_ACCESS_KEY_SECRET,
              SMS_SIGN_NAME: process.env.SMS_SIGN_NAME || '人生导航',
              SMS_TEMPLATE_CODE: process.env.SMS_TEMPLATE_CODE || 'SMS_123456789',
            },
            log: {
              info: console.log,
              error: console.error,
            }
          } as any;

          const smsService = new SMSService(mockApp);
          const success = await smsService.sendVerificationCode({
            phoneNumber,
            code,
          });

          if (!success) {
            throw new Error('Failed to send SMS');
          }

          console.log(`SMS OTP sent to ${phoneNumber}: ${code}`);
        } catch (error) {
          console.error('Failed to send SMS OTP:', error);
          throw error; // Better-Auth expects void or Promise<void>
        }
      },
      signUpOnVerification: {
        getTempEmail: (phoneNumber) => {
          // Generate a temporary email for phone number users
          const normalizedPhone = phoneNumber.replace(/[^\d]/g, '');
          return `${normalizedPhone}@phone.rsdh.local`;
        },
        getTempName: (phoneNumber) => {
          // Use phone number as temporary name
          return phoneNumber;
        },
      },
      phoneNumberValidator: (phoneNumber) => {
        return SMSService.validatePhoneNumber(phoneNumber);
      },
      requireVerification: true,
      otpLength: 6,
      expiresIn: 300, // 5 minutes
    }),

    // WeChat OAuth plugin using generic OAuth
    genericOAuth({
      config: [
        {
          providerId: 'wechat',
          clientId: process.env.WECHAT_APP_ID || '',
          clientSecret: process.env.WECHAT_APP_SECRET || '',
          authorizationUrl: 'https://open.weixin.qq.com/connect/oauth2/authorize',
          tokenUrl: 'https://api.weixin.qq.com/sns/oauth2/access_token',
          userInfoUrl: 'https://api.weixin.qq.com/sns/userinfo',
          scopes: ['snsapi_userinfo'],


        },
      ],
    }),
  ],
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // Set to true if you want email verification
    sendResetPassword: async ({ user, url, token }) => {
      // Import EmailService dynamically to avoid circular dependencies
      const { EmailService } = await import('../services/emailService');

      try {
        // Create a mock fastify instance for EmailService
        // In a real implementation, you'd pass the actual fastify instance
        const mockApp = {
          config: {
            NODE_ENV: process.env.NODE_ENV || 'development',
            EMAIL_HOST: process.env.EMAIL_HOST || '',
            EMAIL_PORT: parseInt(process.env.EMAIL_PORT || '587'),
            EMAIL_USER: process.env.EMAIL_USER || '',
            EMAIL_PASS: process.env.EMAIL_PASS || '',
            EMAIL_FROM: process.env.EMAIL_FROM || '',
            APP_NAME: process.env.APP_NAME || 'Life Navigation',
          },
          log: {
            info: console.log,
            error: console.error,
          }
        } as any;

        const emailService = new EmailService(mockApp);

        // Send password reset email using our email service
        await emailService.sendEmail({
          to: user.email,
          subject: '重置密码 - 验证链接',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2>重置密码</h2>
              <p>您好，</p>
              <p>您请求重置密码。请点击下面的链接来重置您的密码：</p>
              <p><a href="${url}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">重置密码</a></p>
              <p>如果您没有请求重置密码，请忽略此邮件。</p>
              <p>此链接将在1小时后过期。</p>
              <p>谢谢！</p>
            </div>
          `,
          text: `您请求重置密码。请访问以下链接来重置您的密码：${url}。如果您没有请求重置密码，请忽略此邮件。此链接将在1小时后过期。`
        });

        console.log(`Password reset email sent to ${user.email}`);
      } catch (error) {
        console.error('Failed to send password reset email:', error);
        // In development, still log the URL for testing
        if (process.env.NODE_ENV === 'development') {
          console.log(`Password reset URL: ${url}`);
          console.log(`Reset Token: ${token}`);
        }
      }
    },
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // Update session every 24 hours
  },
  user: {
    additionalFields: {
      avatar: {
        type: 'string',
        required: false,
      },
      disabled: {
        type: 'boolean',
        required: false,
        defaultValue: false,
      },
      role: {
        type: 'string',
        required: false,
        defaultValue: 'user',
      },
    },
  },
});
