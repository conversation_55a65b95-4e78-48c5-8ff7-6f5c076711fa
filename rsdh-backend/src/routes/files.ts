import { FastifyInstance } from 'fastify';
import { authenticateUser, AuthenticatedRequest } from '../middleware/auth';
import { FileService } from '../services/fileService';
import { createS3Service } from '../services/s3Service';

export async function fileRoutes(fastify: FastifyInstance) {
  // Initialize services
  const s3Service = createS3Service(fastify);
  const fileService = new FileService(fastify, s3Service);

  // Register multipart support
  await fastify.register(require('@fastify/multipart'), {
    limits: {
      fileSize: fastify.config.MAX_FILE_SIZE,
    },
  });

  // Upload file (multipart form data)
  fastify.post('/upload', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Files'],
      summary: 'Upload file',
      description: 'Upload a file to S3 storage',
      security: [{ bearerAuth: [] }],
      consumes: ['multipart/form-data'],
      response: {
        200: {
          description: 'File uploaded successfully',
          type: 'object',
          properties: {
            id: { type: 'string' },
            originalName: { type: 'string' },
            fileName: { type: 'string' },
            mimeType: { type: 'string' },
            size: { type: 'number' },
            category: { type: 'string' },
            isPublic: { type: 'boolean' },
            url: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' }
          }
        },
        400: {
          description: 'Bad Request',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const parts = (request as any).parts();
      let fileData: any = null;
      let category = 'general';
      let isPublic = false;

      for await (const part of parts) {
        if (part.type === 'file') {
          fileData = part;
        } else if (part.type === 'field') {
          if (part.fieldname === 'category') {
            category = part.value as string;
          } else if (part.fieldname === 'isPublic') {
            isPublic = part.value === 'true';
          }
        }
      }

      if (!fileData) {
        return reply.status(400).send({
          error: 'Bad Request',
          message: 'No file provided'
        });
      }

      const buffer = await fileData.toBuffer();

      const fileRecord = await fileService.uploadFile(request.user!.id, {
        originalName: fileData.filename,
        buffer,
        mimeType: fileData.mimetype,
        size: buffer.length,
        category,
        isPublic,
      });

      const url = await fileService.getFileUrl(fileRecord.id, request.user!.id);

      return reply.send({
        id: fileRecord.id,
        originalName: fileRecord.originalName,
        fileName: fileRecord.fileName,
        mimeType: fileRecord.mimeType,
        size: fileRecord.size,
        category: fileRecord.category,
        isPublic: fileRecord.isPublic,
        url,
        createdAt: fileRecord.createdAt,
      });
    } catch (error) {
      request.log.error('Error uploading file:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Failed to upload file'
      });
    }
  });

  // Generate presigned upload URL
  fastify.post('/upload-url', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Files'],
      summary: 'Generate presigned upload URL',
      description: 'Generate a presigned URL for direct client upload to S3',
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['fileName', 'contentType'],
        properties: {
          fileName: { type: 'string', description: 'Original file name' },
          contentType: { type: 'string', description: 'MIME type of the file' },
          category: { type: 'string', description: 'File category', default: 'general' },
          expiresIn: { type: 'number', description: 'URL expiration time in seconds', default: 3600 }
        }
      },
      response: {
        200: {
          description: 'Presigned URL generated successfully',
          type: 'object',
          properties: {
            uploadUrl: { type: 'string' },
            fileId: { type: 'string' },
            key: { type: 'string' },
            expiresIn: { type: 'number' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { fileName, contentType, category = 'general', expiresIn = 3600 } = request.body as {
        fileName: string;
        contentType: string;
        category?: string;
        expiresIn?: number;
      };

      const result = await fileService.generateUploadUrl(
        request.user!.id,
        fileName,
        contentType,
        category,
        expiresIn
      );

      return reply.send({
        ...result,
        expiresIn,
      });
    } catch (error) {
      request.log.error('Error generating upload URL:', error);
      return reply.status(400).send({
        error: 'Bad Request',
        message: error instanceof Error ? error.message : 'Failed to generate upload URL'
      });
    }
  });

  // Confirm upload completion
  fastify.post('/confirm-upload/:fileId', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Files'],
      summary: 'Confirm upload completion',
      description: 'Confirm that a file has been successfully uploaded via presigned URL',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['fileId'],
        properties: {
          fileId: { type: 'string' }
        }
      },
      response: {
        200: {
          description: 'Upload confirmed successfully',
          type: 'object',
          properties: {
            id: { type: 'string' },
            originalName: { type: 'string' },
            size: { type: 'number' },
            url: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { fileId } = request.params as { fileId: string };

      const fileRecord = await fileService.confirmUpload(fileId, request.user!.id);
      const url = await fileService.getFileUrl(fileRecord.id, request.user!.id);

      return reply.send({
        id: fileRecord.id,
        originalName: fileRecord.originalName,
        size: fileRecord.size,
        url,
      });
    } catch (error) {
      request.log.error('Error confirming upload:', error);
      return reply.status(400).send({
        error: 'Bad Request',
        message: error instanceof Error ? error.message : 'Failed to confirm upload'
      });
    }
  });

  // Get file info
  fastify.get('/:fileId', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Files'],
      summary: 'Get file information',
      description: 'Get file metadata and access URL',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['fileId'],
        properties: {
          fileId: { type: 'string' }
        }
      },
      response: {
        200: {
          description: 'File information retrieved successfully',
          type: 'object',
          properties: {
            id: { type: 'string' },
            originalName: { type: 'string' },
            fileName: { type: 'string' },
            mimeType: { type: 'string' },
            size: { type: 'number' },
            category: { type: 'string' },
            isPublic: { type: 'boolean' },
            url: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' }
          }
        },
        404: {
          description: 'File not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { fileId } = request.params as { fileId: string };

      const file = await fileService.getFileById(fileId, request.user!.id);
      if (!file) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'File not found'
        });
      }

      const url = await fileService.getFileUrl(fileId, request.user!.id);

      return reply.send({
        id: file.id,
        originalName: file.originalName,
        fileName: file.fileName,
        mimeType: file.mimeType,
        size: file.size,
        category: file.category,
        isPublic: file.isPublic,
        url,
        createdAt: file.createdAt,
      });
    } catch (error) {
      request.log.error('Error getting file:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get file information'
      });
    }
  });

  // List user's files
  fastify.get('/', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Files'],
      summary: 'List user files',
      description: 'Get a paginated list of user\'s uploaded files',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          category: { type: 'string', description: 'Filter by category' },
          page: { type: 'number', minimum: 1, default: 1 },
          limit: { type: 'number', minimum: 1, maximum: 100, default: 20 },
          search: { type: 'string', description: 'Search in file names' }
        }
      },
      response: {
        200: {
          description: 'Files retrieved successfully',
          type: 'object',
          properties: {
            files: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  originalName: { type: 'string' },
                  mimeType: { type: 'string' },
                  size: { type: 'number' },
                  category: { type: 'string' },
                  isPublic: { type: 'boolean' },
                  createdAt: { type: 'string', format: 'date-time' }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                page: { type: 'number' },
                limit: { type: 'number' },
                totalPages: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { category, page = 1, limit = 20, search } = request.query as {
        category?: string;
        page?: number;
        limit?: number;
        search?: string;
      };

      const result = await fileService.listFiles({
        userId: request.user!.id,
        category,
        page,
        limit,
        search,
      });

      return reply.send({
        files: result.files.map(file => ({
          id: file.id,
          originalName: file.originalName,
          mimeType: file.mimeType,
          size: file.size,
          category: file.category,
          isPublic: file.isPublic,
          createdAt: file.createdAt,
        })),
        pagination: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          totalPages: result.totalPages,
        },
      });
    } catch (error) {
      request.log.error('Error listing files:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to list files'
      });
    }
  });

  // Delete file
  fastify.delete('/:fileId', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Files'],
      summary: 'Delete file',
      description: 'Delete a file from storage and database',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['fileId'],
        properties: {
          fileId: { type: 'string' }
        }
      },
      response: {
        200: {
          description: 'File deleted successfully',
          type: 'object',
          properties: {
            message: { type: 'string' }
          }
        },
        404: {
          description: 'File not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { fileId } = request.params as { fileId: string };

      await fileService.deleteFile(fileId, request.user!.id);

      return reply.send({
        message: 'File deleted successfully'
      });
    } catch (error) {
      request.log.error('Error deleting file:', error);
      if (error instanceof Error && error.message === 'File not found') {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'File not found'
        });
      }
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Failed to delete file'
      });
    }
  });

  // Get user storage usage
  fastify.get('/usage/stats', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Files'],
      summary: 'Get storage usage statistics',
      description: 'Get user\'s storage usage statistics',
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          description: 'Storage usage retrieved successfully',
          type: 'object',
          properties: {
            totalFiles: { type: 'number' },
            totalSize: { type: 'number' },
            totalSizeFormatted: { type: 'string' },
            sizeByCategory: {
              type: 'object',
              additionalProperties: {
                type: 'object',
                properties: {
                  count: { type: 'number' },
                  size: { type: 'number' },
                  sizeFormatted: { type: 'string' }
                }
              }
            }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const usage = await fileService.getUserStorageUsage(request.user!.id);

      // Format file sizes
      const formatBytes = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      };

      const formattedSizeByCategory: Record<string, any> = {};
      Object.entries(usage.sizeByCategory).forEach(([category, data]) => {
        formattedSizeByCategory[category] = {
          ...data,
          sizeFormatted: formatBytes(data.size),
        };
      });

      return reply.send({
        totalFiles: usage.totalFiles,
        totalSize: usage.totalSize,
        totalSizeFormatted: formatBytes(usage.totalSize),
        sizeByCategory: formattedSizeByCategory,
      });
    } catch (error) {
      request.log.error('Error getting storage usage:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get storage usage'
      });
    }
  });
}
