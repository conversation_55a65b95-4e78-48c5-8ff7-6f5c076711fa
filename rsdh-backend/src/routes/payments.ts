import { FastifyInstance } from 'fastify';
import { PaymentService } from '../services/paymentService';
import { authenticateUser, AuthenticatedRequest } from '../middleware/auth';

export async function paymentRoutes(fastify: FastifyInstance) {
  // Create WeChat payment
  fastify.post('/:paymentId/wechat', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Payments'],
      summary: 'Create WeChat payment',
      description: 'Create WeChat payment for appointment',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['paymentId'],
        properties: {
          paymentId: { type: 'string', description: 'Payment ID' }
        }
      },
      body: {
        type: 'object',
        properties: {
          openid: { type: 'string', description: 'WeChat OpenID (optional)' }
        }
      },
      response: {
        200: {
          description: 'WeChat payment created successfully',
          type: 'object',
          properties: {
            prepayId: { type: 'string' },
            paySign: { type: 'string' },
            timeStamp: { type: 'string' },
            nonceStr: { type: 'string' },
            package: { type: 'string' },
            signType: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { paymentId } = request.params as { paymentId: string };
      const { openid } = request.body as { openid?: string };

      const paymentService = new PaymentService(fastify);

      // First get the payment to get the amount
      const payment = await PaymentService.getPaymentById(paymentId);
      if (!payment) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'Payment not found'
        });
      }

      const paymentResponse = await paymentService.processWeChatPayment(paymentId, {
        amount: payment.amount,
        description: '导师咨询服务',
        outTradeNo: `${paymentId}_${Date.now()}`,
        openid
      });

      if (!paymentResponse) {
        return reply.status(400).send({
          error: 'Payment Failed',
          message: 'Failed to create WeChat payment'
        });
      }

      return reply.send(paymentResponse);
    } catch (error) {
      request.log.error('Error creating WeChat payment:', error);

      if (error instanceof Error) {
        if (error.message === 'Payment not found') {
          return reply.status(404).send({
            error: 'Not Found',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to create WeChat payment'
      });
    }
  });

  // WeChat payment notification callback
  fastify.post('/wechat/notify', {
    schema: {
      tags: ['Payments'],
      summary: 'WeChat payment notification',
      description: 'Handle WeChat payment notification callback',
      body: {
        type: 'object',
        properties: {
          transaction_id: { type: 'string' },
          out_trade_no: { type: 'string' },
          result_code: { type: 'string' },
          return_code: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const notification = request.body as any;

      // TODO: Verify WeChat signature

      if (notification.return_code === 'SUCCESS' && notification.result_code === 'SUCCESS') {
        // Extract payment ID from out_trade_no
        const outTradeNo = notification.out_trade_no;
        const paymentId = outTradeNo.split('_')[0];

        // Complete payment
        await PaymentService.completePayment(paymentId, notification.transaction_id);

        request.log.info(`Payment ${paymentId} completed successfully`);
      }

      // Return success response to WeChat
      return reply.type('application/xml').send(`
        <xml>
          <return_code><![CDATA[SUCCESS]]></return_code>
          <return_msg><![CDATA[OK]]></return_msg>
        </xml>
      `);
    } catch (error) {
      request.log.error('Error processing WeChat payment notification:', error);

      // Return failure response to WeChat
      return reply.type('application/xml').send(`
        <xml>
          <return_code><![CDATA[FAIL]]></return_code>
          <return_msg><![CDATA[ERROR]]></return_msg>
        </xml>
      `);
    }
  });

  // Get payment status
  fastify.get('/:paymentId/status', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Payments'],
      summary: 'Get payment status',
      description: 'Get payment status by ID',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['paymentId'],
        properties: {
          paymentId: { type: 'string', description: 'Payment ID' }
        }
      },
      response: {
        200: {
          description: 'Payment status retrieved successfully',
          type: 'object',
          properties: {
            id: { type: 'string' },
            status: { type: 'string' },
            amount: { type: 'number' },
            currency: { type: 'string' },
            paidAt: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { paymentId } = request.params as { paymentId: string };

      const payment = await PaymentService.getPaymentById(paymentId);

      if (!payment) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'Payment not found'
        });
      }

      // Check if user has access to this payment
      if (payment.studentId !== request.user!.id && payment.tutorId !== request.user!.id) {
        return reply.status(403).send({
          error: 'Forbidden',
          message: 'Access denied'
        });
      }

      return reply.send({
        id: payment.id,
        status: payment.status,
        amount: payment.amount,
        currency: payment.currency,
        paidAt: payment.paidAt
      });
    } catch (error) {
      request.log.error('Error getting payment status:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get payment status'
      });
    }
  });

  // Process pending payment splits (admin endpoint)
  fastify.post('/splits/process', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Payments'],
      summary: 'Process pending payment splits',
      description: 'Process pending payment splits for completed appointments (Admin only)',
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          description: 'Payment splits processed successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      // Check if user is admin
      if (request.user!.role !== 'admin') {
        return reply.status(403).send({
          error: 'Forbidden',
          message: 'Admin access required'
        });
      }

      await PaymentService.processPendingSplits();

      return reply.send({
        success: true,
        message: 'Payment splits processed successfully'
      });
    } catch (error) {
      request.log.error('Error processing payment splits:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to process payment splits'
      });
    }
  });
}
