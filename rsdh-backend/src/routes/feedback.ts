import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { FeedbackService, CreateFeedbackData, UpdateFeedbackData, FeedbackListOptions, AttachFileToFeedbackData } from '../services/feedbackService';
import { authenticateUser, requireAdmin, AuthenticatedRequest } from '../middleware/auth';

interface CreateFeedbackRequest extends AuthenticatedRequest {
  body: CreateFeedbackData & {
    fileIds?: string[];
  };
}

interface UpdateFeedbackRequest extends AuthenticatedRequest {
  params: { id: string };
  body: UpdateFeedbackData;
}

interface GetFeedbackRequest extends AuthenticatedRequest {
  params: { id: string };
}

interface DeleteFeedbackRequest extends AuthenticatedRequest {
  params: { id: string };
}

interface AttachFileRequest extends AuthenticatedRequest {
  body: AttachFileToFeedbackData;
}

interface RemoveFileRequest extends AuthenticatedRequest {
  params: { feedbackId: string; fileId: string };
}

interface GetFeedbackListRequest extends AuthenticatedRequest {
  query: FeedbackListOptions;
}

export async function feedbackRoutes(fastify: FastifyInstance) {
  // Create feedback
  fastify.post('/', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Feedback'],
      summary: 'Create feedback',
      description: 'Create a new feedback with optional file attachments',
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['content'],
        properties: {
          type: {
            type: 'string',
            enum: ['general', 'bug', 'suggestion', 'complaint', 'tutor_feedback', 'appointment_feedback'],
            default: 'general',
            description: 'Type of feedback'
          },
          title: { type: 'string', description: 'Optional title for the feedback' },
          content: { type: 'string', description: 'Main feedback content' },
          contactInfo: { type: 'string', description: 'Optional contact information' },
          category: {
            type: 'string',
            enum: ['general', 'technical', 'service', 'billing', 'ui_ux', 'performance'],
            default: 'general',
            description: 'Category of feedback'
          },
          relatedTutorId: { type: 'string', description: 'ID of related tutor (if applicable)' },
          relatedAppointmentId: { type: 'string', description: 'ID of related appointment (if applicable)' },
          fileIds: {
            type: 'array',
            items: { type: 'string' },
            description: 'Array of file IDs to attach to the feedback'
          },
          metadata: { type: 'object', description: 'Additional metadata' }
        }
      },
      response: {
        201: {
          description: 'Feedback created successfully',
          type: 'object',
          properties: {
            id: { type: 'string' },
            type: { type: 'string' },
            title: { type: 'string' },
            content: { type: 'string' },
            status: { type: 'string' },
            priority: { type: 'string' },
            category: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                email: { type: 'string' },
                avatar: { type: 'string' }
              }
            },
            files: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  file: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      originalName: { type: 'string' },
                      mimeType: { type: 'string' },
                      size: { type: 'number' }
                    }
                  }
                }
              }
            }
          }
        },
        400: {
          description: 'Bad Request',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const body = request.body as CreateFeedbackData & { fileIds?: string[] };
      const { fileIds, ...feedbackData } = body;
      const userAgent = request.headers['user-agent'];
      const ipAddress = request.ip;

      const feedback = await FeedbackService.createFeedback(
        {
          ...feedbackData,
          userAgent,
          ipAddress
        },
        request.user!.id,
        fileIds
      );

      return reply.status(201).send(feedback);
    } catch (error) {
      request.log.error('Error creating feedback:', error);

      if (error instanceof Error) {
        const errorMessages = [
          'Related tutor not found',
          'Related appointment not found',
          'You do not have access to this appointment',
          'Some files not found or not owned by user'
        ];

        if (errorMessages.includes(error.message)) {
          return reply.status(400).send({
            error: 'Bad Request',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while creating feedback'
      });
    }
  });

  // Get feedback by ID
  fastify.get('/:id', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Feedback'],
      summary: 'Get feedback by ID',
      description: 'Get detailed feedback information',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      response: {
        200: {
          description: 'Feedback details',
          type: 'object',
          properties: {
            id: { type: 'string' },
            type: { type: 'string' },
            title: { type: 'string' },
            content: { type: 'string' },
            status: { type: 'string' },
            priority: { type: 'string' },
            category: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
            resolvedAt: { type: 'string', format: 'date-time' },
            user: { type: 'object' },
            relatedTutor: { type: 'object' },
            relatedAppointment: { type: 'object' },
            assignedTo: { type: 'object' },
            files: { type: 'array' }
          }
        },
        404: {
          description: 'Feedback not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const feedback = await FeedbackService.getFeedbackById(id, request.user!.id);

      return reply.send(feedback);
    } catch (error) {
      request.log.error('Error getting feedback:', error);

      if (error instanceof Error) {
        if (error.message === 'Feedback not found') {
          return reply.status(404).send({
            error: 'Not Found',
            message: error.message
          });
        }

        if (error.message === 'Access denied') {
          return reply.status(403).send({
            error: 'Forbidden',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while retrieving feedback'
      });
    }
  });

  // Get feedback list
  fastify.get('/', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Feedback'],
      summary: 'Get feedback list',
      description: 'Get paginated list of feedback (users see only their own, admins see all)',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          type: { type: 'string' },
          status: { type: 'string' },
          priority: { type: 'string' },
          category: { type: 'string' },
          assignedToId: { type: 'string' },
          relatedTutorId: { type: 'string' },
          relatedAppointmentId: { type: 'string' },
          search: { type: 'string' }
        }
      },
      response: {
        200: {
          description: 'Feedback list',
          type: 'object',
          properties: {
            items: { type: 'array' },
            total: { type: 'integer' },
            page: { type: 'integer' },
            limit: { type: 'integer' },
            totalPages: { type: 'integer' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const options = { ...request.query as FeedbackListOptions };

      // Non-admin users can only see their own feedback
      const user = request.user!;
      if (user.role !== 'admin') {
        options.userId = user.id;
      }

      const result = await FeedbackService.getFeedbackList(options);

      return reply.send(result);
    } catch (error) {
      request.log.error('Error getting feedback list:', error);

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while retrieving feedback list'
      });
    }
  });

  // Update feedback (admin only)
  fastify.patch('/:id', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Feedback'],
      summary: 'Update feedback',
      description: 'Update feedback status, priority, assignment, etc. (admin only)',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          status: {
            type: 'string',
            enum: ['pending', 'in_progress', 'resolved', 'closed'],
            description: 'Feedback status'
          },
          priority: {
            type: 'string',
            enum: ['low', 'normal', 'high', 'urgent'],
            description: 'Feedback priority'
          },
          assignedToId: { type: 'string', description: 'ID of admin to assign to' },
          adminNotes: { type: 'string', description: 'Internal admin notes' }
        }
      },
      response: {
        200: {
          description: 'Feedback updated successfully',
          type: 'object'
        },
        404: {
          description: 'Feedback not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const updateData = request.body as UpdateFeedbackData;

      const feedback = await FeedbackService.updateFeedback(id, updateData, request.user!.id);

      return reply.send(feedback);
    } catch (error) {
      request.log.error('Error updating feedback:', error);

      if (error instanceof Error) {
        if (error.message === 'Feedback not found') {
          return reply.status(404).send({
            error: 'Not Found',
            message: error.message
          });
        }

        if (error.message === 'Admin access required' || error.message === 'Assignee must be an admin user') {
          return reply.status(400).send({
            error: 'Bad Request',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while updating feedback'
      });
    }
  });

  // Delete feedback
  fastify.delete('/:id', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Feedback'],
      summary: 'Delete feedback',
      description: 'Delete feedback (admin can delete any, users can delete their own unprocessed feedback)',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      response: {
        200: {
          description: 'Feedback deleted successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' }
          }
        },
        404: {
          description: 'Feedback not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };

      const result = await FeedbackService.deleteFeedback(id, request.user!.id);

      return reply.send(result);
    } catch (error) {
      request.log.error('Error deleting feedback:', error);

      if (error instanceof Error) {
        if (error.message === 'Feedback not found') {
          return reply.status(404).send({
            error: 'Not Found',
            message: error.message
          });
        }

        if (error.message === 'Cannot delete this feedback') {
          return reply.status(403).send({
            error: 'Forbidden',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while deleting feedback'
      });
    }
  });

  // Attach file to feedback
  fastify.post('/files/attach', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Feedback'],
      summary: 'Attach file to feedback',
      description: 'Attach an uploaded file to existing feedback',
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['feedbackId', 'fileId'],
        properties: {
          feedbackId: { type: 'string', description: 'Feedback ID' },
          fileId: { type: 'string', description: 'File ID to attach' },
          description: { type: 'string', description: 'Optional description of the file' }
        }
      },
      response: {
        201: {
          description: 'File attached successfully',
          type: 'object',
          properties: {
            id: { type: 'string' },
            feedbackId: { type: 'string' },
            fileId: { type: 'string' },
            description: { type: 'string' },
            file: { type: 'object' }
          }
        },
        400: {
          description: 'Bad Request',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const attachmentData = request.body as AttachFileToFeedbackData;

      const result = await FeedbackService.attachFileToFeedback(attachmentData, request.user!.id);

      return reply.status(201).send(result);
    } catch (error) {
      request.log.error('Error attaching file to feedback:', error);

      if (error instanceof Error) {
        const errorMessages = [
          'Feedback not found',
          'File not found',
          'Access denied',
          'File access denied',
          'File already attached to this feedback'
        ];

        if (errorMessages.includes(error.message)) {
          return reply.status(400).send({
            error: 'Bad Request',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while attaching file'
      });
    }
  });

  // Remove file from feedback
  fastify.delete('/:feedbackId/files/:fileId', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Feedback'],
      summary: 'Remove file from feedback',
      description: 'Remove an attached file from feedback',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          feedbackId: { type: 'string' },
          fileId: { type: 'string' }
        },
        required: ['feedbackId', 'fileId']
      },
      response: {
        200: {
          description: 'File removed successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' }
          }
        },
        400: {
          description: 'Bad Request',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { feedbackId, fileId } = request.params as { feedbackId: string; fileId: string };

      const result = await FeedbackService.removeFileFromFeedback(feedbackId, fileId, request.user!.id);

      return reply.send(result);
    } catch (error) {
      request.log.error('Error removing file from feedback:', error);

      if (error instanceof Error) {
        const errorMessages = [
          'Feedback not found',
          'Access denied',
          'File attachment not found'
        ];

        if (errorMessages.includes(error.message)) {
          return reply.status(400).send({
            error: 'Bad Request',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while removing file'
      });
    }
  });

  // Get feedback statistics (admin only)
  fastify.get('/admin/stats', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Feedback'],
      summary: 'Get feedback statistics',
      description: 'Get comprehensive feedback statistics (admin only)',
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          description: 'Feedback statistics',
          type: 'object',
          properties: {
            total: { type: 'integer' },
            pending: { type: 'integer' },
            inProgress: { type: 'integer' },
            resolved: { type: 'integer' },
            closed: { type: 'integer' },
            highPriority: { type: 'integer' },
            recent: { type: 'integer' },
            byType: { type: 'array' },
            byCategory: { type: 'array' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const stats = await FeedbackService.getFeedbackStats(request.user!.id);

      return reply.send(stats);
    } catch (error) {
      request.log.error('Error getting feedback statistics:', error);

      if (error instanceof Error && error.message === 'Admin access required') {
        return reply.status(403).send({
          error: 'Forbidden',
          message: error.message
        });
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while retrieving statistics'
      });
    }
  });
}
