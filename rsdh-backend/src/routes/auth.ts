import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { UserService } from '../services/userService';
import { VerificationService } from '../services/verificationService';
import { generateSimpleToken } from '../middleware/auth';
import bcrypt from 'bcrypt';

interface SendCodeBody {
  email: string;
  type: 'registration' | 'password-reset' | 'email-verification';
}

interface VerifyCodeBody {
  email: string;
  code: string;
  type: 'registration' | 'password-reset' | 'email-verification';
}

interface RegisterWithCodeBody {
  email: string;
  code: string;
  password: string;
  name: string;
  avatar?: string;
}

interface ResetPasswordWithCodeBody {
  email: string;
  code: string;
  newPassword: string;
}

interface LoginBody {
  email: string;
  password: string;
}

export async function authRoutes(fastify: FastifyInstance) {
  const verificationService = new VerificationService(fastify);

  // Send verification code
  fastify.post('/send-code', {
    schema: {
      tags: ['Authentication'],
      summary: 'Send verification code',
      description: 'Send verification code to email for registration, password reset, or email verification',
      body: {
        type: 'object',
        required: ['email', 'type'],
        properties: {
          email: { type: 'string', format: 'email' },
          type: { type: 'string', enum: ['registration', 'password-reset', 'email-verification'] },
        },
      },
      response: {
        200: {
          description: 'Verification code sent successfully',
          type: 'object',
          properties: {
            message: { type: 'string' },
            email: { type: 'string' },
          },
        },
        400: {
          description: 'Bad request',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
          },
        },
        429: {
          description: 'Too many requests',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request: FastifyRequest<{ Body: SendCodeBody }>, reply: FastifyReply) => {
    try {
      const { email, type } = request.body;

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return reply.status(400).send({
          error: 'Invalid Email',
          message: 'Please provide a valid email address',
        });
      }

      // Check rate limiting
      const canRequest = await verificationService.canRequestNewCode(email, type);
      if (!canRequest) {
        return reply.status(429).send({
          error: 'Too Many Requests',
          message: 'Please wait before requesting another verification code',
        });
      }

      // Check daily limit
      const hasExceeded = await verificationService.hasExceededDailyLimit(email, type);
      if (hasExceeded) {
        return reply.status(429).send({
          error: 'Daily Limit Exceeded',
          message: 'You have exceeded the daily limit for verification codes',
        });
      }

      // For registration, check if user already exists
      if (type === 'registration') {
        const userExists = await UserService.userExists(email);
        if (userExists) {
          return reply.status(400).send({
            error: 'User Exists',
            message: 'User with this email already exists',
          });
        }
      }

      // For password reset, check if user exists
      if (type === 'password-reset') {
        const userExists = await UserService.userExists(email);
        if (!userExists) {
          return reply.status(400).send({
            error: 'User Not Found',
            message: 'No user found with this email address',
          });
        }
      }

      // Send verification code
      await verificationService.sendVerificationCode(email, type);

      return {
        message: 'Verification code sent successfully',
        email,
      };
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to send verification code',
      });
    }
  });

  // Verify code
  fastify.post('/verify-code', {
    schema: {
      tags: ['Authentication'],
      summary: 'Verify code',
      description: 'Verify a verification code',
      body: {
        type: 'object',
        required: ['email', 'code', 'type'],
        properties: {
          email: { type: 'string', format: 'email' },
          code: { type: 'string', minLength: 6, maxLength: 6 },
          type: { type: 'string', enum: ['registration', 'password-reset', 'email-verification'] },
        },
      },
      response: {
        200: {
          description: 'Code verified successfully',
          type: 'object',
          properties: {
            message: { type: 'string' },
            valid: { type: 'boolean' },
          },
        },
        400: {
          description: 'Invalid code',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request: FastifyRequest<{ Body: VerifyCodeBody }>, reply: FastifyReply) => {
    try {
      const { email, code, type } = request.body;

      const isValid = await verificationService.verifyCode(email, code, type);

      if (!isValid) {
        return reply.status(400).send({
          error: 'Invalid Code',
          message: 'The verification code is invalid or has expired',
        });
      }

      return {
        message: 'Code verified successfully',
        valid: true,
      };
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to verify code',
      });
    }
  });

  // Register with verification code
  fastify.post('/register-with-code', {
    schema: {
      tags: ['Authentication'],
      summary: 'Register with verification code',
      description: 'Register a new user using email verification code',
      body: {
        type: 'object',
        required: ['email', 'code', 'password', 'name'],
        properties: {
          email: { type: 'string', format: 'email' },
          code: { type: 'string', minLength: 6, maxLength: 6 },
          password: { type: 'string', minLength: 8 },
          name: { type: 'string', minLength: 1 },
          avatar: { type: 'string', format: 'uri' },
        },
      },
      response: {
        201: {
          description: 'User registered successfully',
          type: 'object',
          properties: {
            message: { type: 'string' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string' },
                avatar: { type: 'string' },
                role: { type: 'string' },
              },
            },
          },
        },
        400: {
          description: 'Bad request',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request: FastifyRequest<{ Body: RegisterWithCodeBody }>, reply: FastifyReply) => {
    try {
      const { email, code, password, name, avatar } = request.body;

      // Verify the code first
      const isValid = await verificationService.verifyCode(email, code, 'registration');
      if (!isValid) {
        return reply.status(400).send({
          error: 'Invalid Code',
          message: 'The verification code is invalid or has expired',
        });
      }

      // Check if user already exists
      const userExists = await UserService.userExists(email);
      if (userExists) {
        return reply.status(400).send({
          error: 'User Exists',
          message: 'User with this email already exists',
        });
      }

      // Create user
      const user = await UserService.createUser({
        email,
        password,
        name,
        avatar,
      });

      return reply.status(201).send({
        message: 'User registered successfully',
        user,
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to register user',
      });
    }
  });

  // Reset password with verification code
  fastify.post('/reset-password-with-code', {
    schema: {
      tags: ['Authentication'],
      summary: 'Reset password with verification code',
      description: 'Reset user password using email verification code',
      body: {
        type: 'object',
        required: ['email', 'code', 'newPassword'],
        properties: {
          email: { type: 'string', format: 'email' },
          code: { type: 'string', minLength: 6, maxLength: 6 },
          newPassword: { type: 'string', minLength: 8 },
        },
      },
      response: {
        200: {
          description: 'Password reset successfully',
          type: 'object',
          properties: {
            message: { type: 'string' },
          },
        },
        400: {
          description: 'Bad request',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request: FastifyRequest<{ Body: ResetPasswordWithCodeBody }>, reply: FastifyReply) => {
    try {
      const { email, code, newPassword } = request.body;

      // Verify the code first
      const isValid = await verificationService.verifyCode(email, code, 'password-reset');
      if (!isValid) {
        return reply.status(400).send({
          error: 'Invalid Code',
          message: 'The verification code is invalid or has expired',
        });
      }

      // Find user
      const user = await UserService.findByEmail(email);
      if (!user) {
        return reply.status(400).send({
          error: 'User Not Found',
          message: 'No user found with this email address',
        });
      }

      // Use UserService to update password to ensure consistent hashing
      await UserService.updatePassword(user.id, newPassword);

      return {
        message: 'Password reset successfully',
      };
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to reset password',
      });
    }
  });

  // Simple login endpoint for development/testing
  fastify.post('/login', {
    schema: {
      tags: ['Authentication'],
      summary: 'Login with email and password',
      description: 'Login with email and password to get an authentication token',
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 8 },
        },
      },
      response: {
        200: {
          description: 'Login successful',
          type: 'object',
          properties: {
            message: { type: 'string' },
            token: { type: 'string' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string' },
                role: { type: 'string' },
              },
            },
          },
        },
        401: {
          description: 'Invalid credentials',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request: FastifyRequest<{ Body: LoginBody }>, reply: FastifyReply) => {
    try {
      const { email, password } = request.body;

      // Get user by email (including password for authentication)
      const user = await UserService.findByEmailForAuth(email);
      if (!user) {
        return reply.status(401).send({
          error: 'Invalid Credentials',
          message: 'Invalid email or password',
        });
      }

      // Check if user is disabled
      if (user.disabled) {
        return reply.status(401).send({
          error: 'Account Disabled',
          message: 'Your account has been disabled',
        });
      }

      // Verify password
      if (!user.password) {
        return reply.status(401).send({
          error: 'Invalid Credentials',
          message: 'Invalid email or password',
        });
      }

      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        return reply.status(401).send({
          error: 'Invalid Credentials',
          message: 'Invalid email or password',
        });
      }

      // Generate token
      const token = generateSimpleToken(email);

      reply.send({
        message: 'Login successful',
        token,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        },
      });
    } catch (error) {
      request.log.error('Login error:', error);
      reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Login failed',
      });
    }
  });
}
