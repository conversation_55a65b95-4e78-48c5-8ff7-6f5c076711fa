import { FastifyInstance } from 'fastify';
import { PaymentService } from '../services/paymentService';
import { authenticateUser, AuthenticatedRequest } from '../middleware/auth';
import { yuanToFen, formatFenAsYuan } from '../utils/currency';

export async function adminPaymentRoutes(fastify: FastifyInstance) {
  // Get payment statistics
  fastify.get('/stats', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Admin Payments'],
      summary: 'Get payment statistics',
      description: 'Get payment statistics for admin dashboard',
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            totalPayments: { type: 'number' },
            completedPayments: { type: 'number' },
            refundedPayments: { type: 'number' },
            pendingPayments: { type: 'number' },
            totalAmount: { type: 'string' },
            completedAmount: { type: 'string' },
            refundedAmount: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      // Check admin permission
      if (request.user!.role !== 'admin') {
        return reply.status(403).send({
          error: 'Forbidden',
          message: 'Admin access required'
        });
      }

      const stats = await PaymentService.getPaymentStats();

      return reply.send({
        totalPayments: stats.totalPayments,
        completedPayments: stats.completedPayments,
        refundedPayments: stats.refundedPayments,
        pendingPayments: stats.pendingPayments,
        totalAmount: formatFenAsYuan(stats.totalAmount),
        completedAmount: formatFenAsYuan(stats.completedAmount),
        refundedAmount: formatFenAsYuan(stats.refundedAmount)
      });
    } catch (error) {
      request.log.error('Error getting payment stats:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get payment statistics'
      });
    }
  });

  // Get all payments with pagination
  fastify.get('/', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Admin Payments'],
      summary: 'Get all payments',
      description: 'Get all payments with pagination for admin management',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'number', minimum: 1, default: 1 },
          limit: { type: 'number', minimum: 1, maximum: 100, default: 10 },
          status: { type: 'string', enum: ['pending', 'processing', 'completed', 'failed', 'refunded'] },
          search: { type: 'string' }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      // Check admin permission
      if (request.user!.role !== 'admin') {
        return reply.status(403).send({
          error: 'Forbidden',
          message: 'Admin access required'
        });
      }

      const { page, limit, status, search } = request.query as {
        page?: number;
        limit?: number;
        status?: string;
        search?: string;
      };

      const result = await PaymentService.getAllPayments({
        page,
        limit,
        status,
        search
      });

      // Convert BigInt amounts to strings for JSON serialization
      const paymentsWithFormattedAmounts = result.payments.map(payment => ({
        ...payment,
        amount: formatFenAsYuan(payment.amount),
        refundAmount: payment.refundAmount ? formatFenAsYuan(payment.refundAmount) : null,
        splits: payment.splits.map(split => ({
          ...split,
          tutorAmount: formatFenAsYuan(split.tutorAmount),
          platformAmount: formatFenAsYuan(split.platformAmount)
        }))
      }));

      return reply.send({
        ...result,
        payments: paymentsWithFormattedAmounts
      });
    } catch (error) {
      request.log.error('Error getting payments:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get payments'
      });
    }
  });

  // Process refund
  fastify.post('/:paymentId/refund', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Admin Payments'],
      summary: 'Process refund',
      description: 'Process refund for a payment',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['paymentId'],
        properties: {
          paymentId: { type: 'string', description: 'Payment ID' }
        }
      },
      body: {
        type: 'object',
        required: ['amount', 'reason'],
        properties: {
          amount: { type: 'string', description: 'Refund amount in yuan' },
          reason: { type: 'string', description: 'Reason for refund' }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      // Check admin permission
      if (request.user!.role !== 'admin') {
        return reply.status(403).send({
          error: 'Forbidden',
          message: 'Admin access required'
        });
      }

      const { paymentId } = request.params as { paymentId: string };
      const { amount, reason } = request.body as { amount: string; reason: string };

      const refundAmountFen = yuanToFen(amount);

      const refundedPayment = await PaymentService.processRefund(
        paymentId,
        refundAmountFen,
        reason,
        request.user!.id
      );

      // Convert BigInt amounts to strings for JSON serialization
      const formattedPayment = {
        ...refundedPayment,
        amount: formatFenAsYuan(refundedPayment.amount),
        refundAmount: refundedPayment.refundAmount ? formatFenAsYuan(refundedPayment.refundAmount) : null
      };

      return reply.send(formattedPayment);
    } catch (error) {
      request.log.error('Error processing refund:', error);

      if (error instanceof Error) {
        return reply.status(400).send({
          error: 'Bad Request',
          message: error.message
        });
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to process refund'
      });
    }
  });

  // Get split statistics
  fastify.get('/splits/stats', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Admin Payments'],
      summary: 'Get split statistics',
      description: 'Get payment split statistics for admin dashboard',
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            totalSplits: { type: 'number' },
            pendingSplits: { type: 'number' },
            completedSplits: { type: 'number' },
            cancelledSplits: { type: 'number' },
            totalTutorAmount: { type: 'string' },
            completedTutorAmount: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      // Check admin permission
      if (request.user!.role !== 'admin') {
        return reply.status(403).send({
          error: 'Forbidden',
          message: 'Admin access required'
        });
      }

      const stats = await PaymentService.getSplitStats();

      return reply.send({
        totalSplits: stats.totalSplits,
        pendingSplits: stats.pendingSplits,
        completedSplits: stats.completedSplits,
        cancelledSplits: stats.cancelledSplits,
        totalTutorAmount: formatFenAsYuan(stats.totalTutorAmount),
        completedTutorAmount: formatFenAsYuan(stats.completedTutorAmount)
      });
    } catch (error) {
      request.log.error('Error getting split stats:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get split statistics'
      });
    }
  });

  // Get all splits with pagination
  fastify.get('/splits', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Admin Payments'],
      summary: 'Get all payment splits',
      description: 'Get all payment splits with pagination for admin management',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'number', minimum: 1, default: 1 },
          limit: { type: 'number', minimum: 1, maximum: 100, default: 10 },
          status: { type: 'string', enum: ['pending', 'processing', 'completed', 'failed', 'cancelled'] },
          search: { type: 'string' }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      // Check admin permission
      if (request.user!.role !== 'admin') {
        return reply.status(403).send({
          error: 'Forbidden',
          message: 'Admin access required'
        });
      }

      const { page, limit, status, search } = request.query as {
        page?: number;
        limit?: number;
        status?: string;
        search?: string;
      };

      const result = await PaymentService.getAllSplits({
        page,
        limit,
        status,
        search
      });

      // Convert BigInt amounts to strings for JSON serialization
      const splitsWithFormattedAmounts = result.splits.map(split => ({
        ...split,
        tutorAmount: formatFenAsYuan(split.tutorAmount),
        platformAmount: formatFenAsYuan(split.platformAmount),
        payment: {
          ...split.payment,
          amount: formatFenAsYuan(split.payment.amount)
        }
      }));

      return reply.send({
        ...result,
        splits: splitsWithFormattedAmounts
      });
    } catch (error) {
      request.log.error('Error getting splits:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get splits'
      });
    }
  });

  // Process split manually
  fastify.post('/splits/:splitId/process', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Admin Payments'],
      summary: 'Process split manually',
      description: 'Process payment split manually (admin action)',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['splitId'],
        properties: {
          splitId: { type: 'string', description: 'Split ID' }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      // Check admin permission
      if (request.user!.role !== 'admin') {
        return reply.status(403).send({
          error: 'Forbidden',
          message: 'Admin access required'
        });
      }

      const { splitId } = request.params as { splitId: string };

      const processedSplit = await PaymentService.processSplitManually(
        splitId,
        request.user!.id
      );

      // Convert BigInt amounts to strings for JSON serialization
      const formattedSplit = {
        ...processedSplit,
        tutorAmount: formatFenAsYuan(processedSplit.tutorAmount),
        platformAmount: formatFenAsYuan(processedSplit.platformAmount)
      };

      return reply.send(formattedSplit);
    } catch (error) {
      request.log.error('Error processing split:', error);

      if (error instanceof Error) {
        return reply.status(400).send({
          error: 'Bad Request',
          message: error.message
        });
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to process split'
      });
    }
  });

  // Cancel split
  fastify.post('/splits/:splitId/cancel', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Admin Payments'],
      summary: 'Cancel split',
      description: 'Cancel payment split (admin action)',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['splitId'],
        properties: {
          splitId: { type: 'string', description: 'Split ID' }
        }
      },
      body: {
        type: 'object',
        required: ['reason'],
        properties: {
          reason: { type: 'string', description: 'Reason for cancellation' }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      // Check admin permission
      if (request.user!.role !== 'admin') {
        return reply.status(403).send({
          error: 'Forbidden',
          message: 'Admin access required'
        });
      }

      const { splitId } = request.params as { splitId: string };
      const { reason } = request.body as { reason: string };

      const cancelledSplit = await PaymentService.cancelSplit(
        splitId,
        request.user!.id,
        reason
      );

      // Convert BigInt amounts to strings for JSON serialization
      const formattedSplit = {
        ...cancelledSplit,
        tutorAmount: formatFenAsYuan(cancelledSplit.tutorAmount),
        platformAmount: formatFenAsYuan(cancelledSplit.platformAmount)
      };

      return reply.send(formattedSplit);
    } catch (error) {
      request.log.error('Error cancelling split:', error);

      if (error instanceof Error) {
        return reply.status(400).send({
          error: 'Bad Request',
          message: error.message
        });
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to cancel split'
      });
    }
  });
}
