const fetch = require('node-fetch');

const BACKEND_URL = 'http://127.0.0.1:3003';
const FRONTEND_URL = 'http://localhost:3004';

async function testBackendAPI() {
  console.log('🔧 Testing Backend API...\n');

  const tests = [
    {
      name: 'Basic tutors endpoint',
      url: `${BACKEND_URL}/api/tutors`,
      description: 'Should return list of tutors'
    },
    {
      name: '985 university filter',
      url: `${BACKEND_URL}/api/tutors?is985=true`,
      description: 'Should return tutors from 985 universities'
    },
    {
      name: '211 university filter',
      url: `${BACKEND_URL}/api/tutors?is211=true`,
      description: 'Should return tutors from 211 universities'
    },
    {
      name: 'Institution search',
      url: `${BACKEND_URL}/api/tutors?institution=Tsinghua`,
      description: 'Should return tutors from Tsinghua University'
    },
    {
      name: 'Career filter',
      url: `${BACKEND_URL}/api/tutors?career=Software`,
      description: 'Should return tutors with Software career'
    },
    {
      name: 'Price range filter',
      url: `${BACKEND_URL}/api/tutors?priceFrom=200&priceTo=400`,
      description: 'Should return tutors in price range 200-400'
    },
    {
      name: 'Combined filters',
      url: `${BACKEND_URL}/api/tutors?is985=true&career=Software&priceFrom=200`,
      description: 'Should return tutors matching multiple criteria'
    }
  ];

  let passedTests = 0;

  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}`);
      console.log(`  URL: ${test.url}`);
      
      const response = await fetch(test.url);
      
      if (!response.ok) {
        console.log(`  ❌ HTTP Error: ${response.status}`);
        continue;
      }

      const data = await response.json();
      
      if (data && typeof data === 'object' && Array.isArray(data.tutors)) {
        console.log(`  ✅ Success: Found ${data.tutors.length} tutors`);
        console.log(`  Total: ${data.total}, Page: ${data.page}/${data.totalPages}`);
        passedTests++;
      } else {
        console.log(`  ❌ Invalid response format`);
      }
      
      console.log('');
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
      console.log('');
    }
  }

  console.log(`📊 Backend API Tests: ${passedTests}/${tests.length} passed\n`);
  return passedTests === tests.length;
}

async function testFrontendAccess() {
  console.log('🌐 Testing Frontend Access...\n');

  try {
    const response = await fetch(FRONTEND_URL);
    
    if (response.ok) {
      console.log('✅ Frontend is accessible');
      console.log(`   URL: ${FRONTEND_URL}`);
      console.log(`   Status: ${response.status}`);
      return true;
    } else {
      console.log(`❌ Frontend returned status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Frontend access failed: ${error.message}`);
    return false;
  }
}

async function testCORS() {
  console.log('\n🔗 Testing CORS Configuration...\n');

  try {
    // Test preflight request
    const preflightResponse = await fetch(`${BACKEND_URL}/api/tutors`, {
      method: 'OPTIONS',
      headers: {
        'Origin': FRONTEND_URL,
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    });

    console.log(`Preflight request status: ${preflightResponse.status}`);
    
    const corsHeaders = {
      'Access-Control-Allow-Origin': preflightResponse.headers.get('Access-Control-Allow-Origin'),
      'Access-Control-Allow-Methods': preflightResponse.headers.get('Access-Control-Allow-Methods'),
      'Access-Control-Allow-Headers': preflightResponse.headers.get('Access-Control-Allow-Headers')
    };

    console.log('CORS Headers:', corsHeaders);

    if (corsHeaders['Access-Control-Allow-Origin']) {
      console.log('✅ CORS is configured');
      return true;
    } else {
      console.log('❌ CORS may not be properly configured');
      return false;
    }
  } catch (error) {
    console.log(`❌ CORS test failed: ${error.message}`);
    return false;
  }
}

async function generateTestReport() {
  console.log('📋 Generating Integration Test Report...\n');

  const backendHealthy = await testBackendAPI();
  const frontendHealthy = await testFrontendAccess();
  const corsWorking = await testCORS();

  console.log('\n' + '='.repeat(50));
  console.log('📊 INTEGRATION TEST SUMMARY');
  console.log('='.repeat(50));
  
  console.log(`Backend API: ${backendHealthy ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Frontend Access: ${frontendHealthy ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`CORS Configuration: ${corsWorking ? '✅ PASS' : '❌ FAIL'}`);
  
  console.log('\n📝 Next Steps:');
  
  if (backendHealthy && frontendHealthy && corsWorking) {
    console.log('🎉 All systems are working! You can now:');
    console.log(`   1. Open ${FRONTEND_URL}/tutors to test the filter UI`);
    console.log('   2. Try different filter combinations');
    console.log('   3. Verify that the advanced filters work correctly');
  } else {
    console.log('⚠️  Some issues detected:');
    if (!backendHealthy) {
      console.log('   - Check backend server is running on port 3003');
      console.log('   - Verify database connection');
    }
    if (!frontendHealthy) {
      console.log('   - Check frontend server is running on port 3004');
      console.log('   - Verify Vite configuration');
    }
    if (!corsWorking) {
      console.log('   - Check CORS configuration in backend');
      console.log('   - Verify frontend URL is in allowed origins');
    }
  }

  console.log('\n🔗 Useful URLs:');
  console.log(`   Frontend: ${FRONTEND_URL}`);
  console.log(`   Backend API: ${BACKEND_URL}/api/tutors`);
  console.log(`   API Documentation: ${BACKEND_URL}/documentation`);
}

// Run the integration tests
generateTestReport().catch(console.error);
