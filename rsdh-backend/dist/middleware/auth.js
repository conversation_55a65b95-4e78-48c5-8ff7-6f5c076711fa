"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticateUser = authenticateUser;
exports.requireAdmin = requireAdmin;
exports.generateSimpleToken = generateSimpleToken;
const userService_1 = require("../services/userService");
/**
 * Simple authentication middleware
 * For now, we'll use a basic approach until better-auth is fully integrated
 */
async function authenticateUser(request, reply) {
    try {
        // For development/testing, we'll use a simple header-based auth
        // In production, this should be replaced with proper JWT validation
        const authHeader = request.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return reply.status(401).send({
                error: 'Unauthorized',
                message: 'Missing or invalid authorization header'
            });
        }
        // Extract email from the token (simplified for now)
        const token = authHeader.substring(7); // Remove 'Bearer '
        // For development, we'll decode a simple base64 encoded email
        // In production, this should be proper JWT validation
        let email;
        try {
            email = Buffer.from(token, 'base64').toString('utf-8');
        }
        catch (error) {
            return reply.status(401).send({
                error: 'Unauthorized',
                message: 'Invalid token format'
            });
        }
        // Get user from database
        const user = await userService_1.UserService.findByEmail(email);
        if (!user) {
            return reply.status(401).send({
                error: 'Unauthorized',
                message: 'User not found'
            });
        }
        if (user.disabled) {
            return reply.status(403).send({
                error: 'Forbidden',
                message: 'User account is disabled'
            });
        }
        // Attach user to request
        request.user = {
            id: user.id,
            email: user.email,
            role: user.role,
            disabled: user.disabled
        };
    }
    catch (error) {
        request.log.error('Authentication error:', error);
        return reply.status(500).send({
            error: 'Internal Server Error',
            message: 'Authentication failed'
        });
    }
}
/**
 * Admin role check middleware
 */
async function requireAdmin(request, reply) {
    if (!request.user) {
        return reply.status(401).send({
            error: 'Unauthorized',
            message: 'Authentication required'
        });
    }
    if (request.user.role !== 'admin') {
        return reply.status(403).send({
            error: 'Forbidden',
            message: 'Admin access required'
        });
    }
}
/**
 * Generate a simple token for development/testing
 */
function generateSimpleToken(email) {
    return Buffer.from(email).toString('base64');
}
