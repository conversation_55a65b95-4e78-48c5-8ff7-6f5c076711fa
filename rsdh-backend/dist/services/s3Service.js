"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.S3_PROVIDER_ENDPOINTS = exports.S3Service = void 0;
exports.createS3Service = createS3Service;
const client_s3_1 = require("@aws-sdk/client-s3");
const s3_request_presigner_1 = require("@aws-sdk/s3-request-presigner");
const crypto = __importStar(require("crypto"));
const path = __importStar(require("path"));
const mime_types_1 = require("mime-types");
class S3Service {
    constructor(config) {
        this.config = config;
        // Configure S3 client based on provider
        const clientConfig = {
            region: config.region,
            credentials: {
                accessKeyId: config.accessKeyId,
                secretAccessKey: config.secretAccessKey,
            },
        };
        // Set custom endpoint for non-AWS providers
        if (config.endpoint) {
            clientConfig.endpoint = config.endpoint;
        }
        // Force path style for providers like MinIO
        if (config.forcePathStyle) {
            clientConfig.forcePathStyle = true;
        }
        this.s3Client = new client_s3_1.S3Client(clientConfig);
    }
    /**
     * Generate a unique file key
     */
    generateFileKey(originalName, category = 'general') {
        const ext = path.extname(originalName);
        const timestamp = Date.now();
        const random = crypto.randomBytes(8).toString('hex');
        return `${category}/${timestamp}-${random}${ext}`;
    }
    /**
     * Upload file buffer to S3
     */
    async uploadFile(buffer, originalName, options = {}) {
        const key = options.key || this.generateFileKey(originalName);
        const contentType = options.contentType || (0, mime_types_1.lookup)(originalName) || 'application/octet-stream';
        const command = new client_s3_1.PutObjectCommand({
            Bucket: this.config.bucketName,
            Key: key,
            Body: buffer,
            ContentType: contentType,
            Metadata: options.metadata,
            ACL: options.isPublic ? 'public-read' : 'private',
        });
        await this.s3Client.send(command);
        const url = this.getFileUrl(key);
        return {
            key,
            url,
            size: buffer.length,
        };
    }
    /**
     * Get file URL (CDN or S3 direct)
     */
    getFileUrl(key) {
        if (this.config.cdnBaseUrl) {
            return `${this.config.cdnBaseUrl}/${key}`;
        }
        if (this.config.endpoint) {
            // Custom endpoint (like MinIO, Backblaze, etc.)
            const baseUrl = this.config.endpoint.replace(/\/$/, '');
            return `${baseUrl}/${this.config.bucketName}/${key}`;
        }
        // AWS S3 URL
        return `https://${this.config.bucketName}.s3.${this.config.region}.amazonaws.com/${key}`;
    }
    /**
     * Generate presigned URL for upload
     */
    async getPresignedUploadUrl(key, contentType, expiresIn = 3600) {
        const command = new client_s3_1.PutObjectCommand({
            Bucket: this.config.bucketName,
            Key: key,
            ContentType: contentType,
        });
        return await (0, s3_request_presigner_1.getSignedUrl)(this.s3Client, command, { expiresIn });
    }
    /**
     * Generate presigned URL for download
     */
    async getPresignedDownloadUrl(key, expiresIn = 3600) {
        const command = new client_s3_1.GetObjectCommand({
            Bucket: this.config.bucketName,
            Key: key,
        });
        return await (0, s3_request_presigner_1.getSignedUrl)(this.s3Client, command, { expiresIn });
    }
    /**
     * Delete file from S3
     */
    async deleteFile(key) {
        const command = new client_s3_1.DeleteObjectCommand({
            Bucket: this.config.bucketName,
            Key: key,
        });
        await this.s3Client.send(command);
    }
    /**
     * Get file information
     */
    async getFileInfo(key) {
        try {
            const command = new client_s3_1.HeadObjectCommand({
                Bucket: this.config.bucketName,
                Key: key,
            });
            const response = await this.s3Client.send(command);
            return {
                key,
                bucket: this.config.bucketName,
                region: this.config.region,
                size: response.ContentLength || 0,
                contentType: response.ContentType || 'application/octet-stream',
                lastModified: response.LastModified || new Date(),
                etag: response.ETag || '',
            };
        }
        catch (error) {
            return null;
        }
    }
    /**
     * List files with prefix
     */
    async listFiles(prefix = '', maxKeys = 100) {
        const command = new client_s3_1.ListObjectsV2Command({
            Bucket: this.config.bucketName,
            Prefix: prefix,
            MaxKeys: maxKeys,
        });
        const response = await this.s3Client.send(command);
        return response.Contents?.map((obj) => obj.Key || '') || [];
    }
    /**
     * Check if file exists
     */
    async fileExists(key) {
        const info = await this.getFileInfo(key);
        return info !== null;
    }
}
exports.S3Service = S3Service;
/**
 * Create S3Service instance from Fastify config
 */
function createS3Service(fastify) {
    const config = {
        provider: fastify.config.S3_PROVIDER,
        endpoint: fastify.config.S3_ENDPOINT,
        region: fastify.config.S3_REGION,
        accessKeyId: fastify.config.S3_ACCESS_KEY_ID,
        secretAccessKey: fastify.config.S3_SECRET_ACCESS_KEY,
        bucketName: fastify.config.S3_BUCKET_NAME,
        forcePathStyle: fastify.config.S3_FORCE_PATH_STYLE,
        cdnBaseUrl: fastify.config.CDN_BASE_URL,
    };
    return new S3Service(config);
}
/**
 * Provider-specific endpoint configurations
 */
exports.S3_PROVIDER_ENDPOINTS = {
    aws: undefined, // Use default AWS endpoints
    backblaze: 'https://s3.us-west-002.backblazeb2.com',
    aliyun: 'https://oss-cn-hangzhou.aliyuncs.com', // Example region
    tencent: 'https://cos.ap-beijing.myqcloud.com', // Example region
    minio: 'http://localhost:9000', // Default MinIO endpoint
};
