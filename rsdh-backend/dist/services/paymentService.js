"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentService = void 0;
const prisma_1 = require("../lib/prisma");
const crypto_1 = __importDefault(require("crypto"));
const currency_1 = require("../utils/currency");
class PaymentService {
    constructor(app) {
        this.app = app;
    }
    /**
     * Create a new payment record
     */
    static async createPayment(data, tx) {
        (0, currency_1.validateNonNegativeFen)(data.amount);
        const client = tx || prisma_1.prisma;
        return await client.payment.create({
            data: {
                appointmentId: data.appointmentId,
                tutorId: data.tutorId,
                studentId: data.studentId,
                amount: data.amount,
                currency: data.currency || 'CNY',
                paymentMethod: data.paymentMethod || 'wechat',
                status: 'pending'
            },
            include: {
                appointment: true,
                tutor: {
                    include: {
                        user: true
                    }
                },
                student: true
            }
        });
    }
    /**
     * Process WeChat payment (mock implementation for testing)
     */
    async processWeChatPayment(paymentId, request) {
        const payment = await prisma_1.prisma.payment.findUnique({
            where: { id: paymentId },
            include: { appointment: true }
        });
        if (!payment) {
            throw new Error('Payment not found');
        }
        // Check if WeChat Pay is configured
        const isWeChatConfigured = this.app.config.WECHAT_PAY_APP_ID &&
            this.app.config.WECHAT_PAY_MCH_ID &&
            this.app.config.WECHAT_PAY_API_KEY;
        if (!isWeChatConfigured) {
            // Mock payment for testing
            this.app.log.info(`[Mock WeChat Pay] Processing payment ${paymentId} for amount ${(0, currency_1.formatFenAsYuan)(request.amount)} CNY`);
            // Simulate payment processing delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            // Mock successful payment
            await prisma_1.prisma.payment.update({
                where: { id: paymentId },
                data: {
                    status: 'completed',
                    transactionId: `mock_${Date.now()}`,
                    wechatOrderId: request.outTradeNo,
                    paidAt: new Date()
                }
            });
            // Return mock response
            return {
                prepayId: `mock_prepay_${Date.now()}`,
                paySign: this.generateMockSign(),
                timeStamp: Math.floor(Date.now() / 1000).toString(),
                nonceStr: this.generateNonceStr(),
                package: `prepay_id=mock_prepay_${Date.now()}`,
                signType: 'MD5'
            };
        }
        // TODO: Implement real WeChat Pay integration
        // This would involve calling WeChat Pay API with proper authentication
        throw new Error('WeChat Pay integration not implemented yet');
    }
    /**
     * Handle payment completion and trigger split processing
     */
    static async completePayment(paymentId, transactionId) {
        const payment = await prisma_1.prisma.payment.update({
            where: { id: paymentId },
            data: {
                status: 'completed',
                transactionId,
                paidAt: new Date()
            },
            include: {
                appointment: true,
                tutor: true
            }
        });
        // Create payment split record
        const tutorPercentage = parseFloat(process.env.TUTOR_COMMISSION_RATE || '0.7');
        const { primaryAmount: tutorAmount, remainderAmount: platformAmount } = (0, currency_1.calculatePercentageSplit)(payment.amount, tutorPercentage);
        await PaymentService.createPaymentSplit({
            paymentId: payment.id,
            tutorId: payment.tutorId,
            tutorAmount,
            platformAmount,
            tutorPercentage
        });
        // Update appointment status to confirmed
        await prisma_1.prisma.appointment.update({
            where: { id: payment.appointmentId },
            data: {
                paymentStatus: 'paid',
                confirmationStatus: 'confirmed'
            }
        });
        return payment;
    }
    /**
     * Create payment split record
     */
    static async createPaymentSplit(data) {
        (0, currency_1.validateNonNegativeFen)(data.tutorAmount);
        (0, currency_1.validateNonNegativeFen)(data.platformAmount);
        return await prisma_1.prisma.paymentSplit.create({
            data: {
                paymentId: data.paymentId,
                tutorId: data.tutorId,
                tutorAmount: data.tutorAmount,
                platformAmount: data.platformAmount,
                tutorPercentage: data.tutorPercentage,
                status: 'pending'
            }
        });
    }
    /**
     * Process payment splits 48 hours after appointment completion
     */
    static async processPendingSplits() {
        const cutoffTime = new Date();
        cutoffTime.setHours(cutoffTime.getHours() - 48);
        const pendingSplits = await prisma_1.prisma.paymentSplit.findMany({
            where: {
                status: 'pending',
                payment: {
                    appointment: {
                        status: 'completed',
                        endTime: {
                            lte: cutoffTime
                        }
                    }
                }
            },
            include: {
                payment: {
                    include: {
                        appointment: true
                    }
                },
                tutor: {
                    include: {
                        user: true
                    }
                }
            }
        });
        for (const split of pendingSplits) {
            try {
                await PaymentService.transferToTutor(split.id);
            }
            catch (error) {
                console.error(`Failed to process split ${split.id}:`, error);
            }
        }
    }
    /**
     * Transfer money to tutor (mock implementation)
     */
    static async transferToTutor(splitId) {
        const split = await prisma_1.prisma.paymentSplit.findUnique({
            where: { id: splitId },
            include: {
                tutor: {
                    include: {
                        user: true
                    }
                }
            }
        });
        if (!split) {
            throw new Error('Payment split not found');
        }
        // Mock transfer for testing
        console.log(`[Mock Transfer] Transferring ${(0, currency_1.formatFenAsYuan)(split.tutorAmount)} CNY to tutor ${split.tutor.user.name}`);
        await prisma_1.prisma.paymentSplit.update({
            where: { id: splitId },
            data: {
                status: 'completed',
                transferredAt: new Date(),
                wechatTransferId: `mock_transfer_${Date.now()}`
            }
        });
        return split;
    }
    /**
     * Generate mock payment signature for testing
     */
    generateMockSign() {
        return crypto_1.default.randomBytes(16).toString('hex');
    }
    /**
     * Generate random nonce string
     */
    generateNonceStr() {
        return crypto_1.default.randomBytes(16).toString('hex');
    }
    /**
     * Get payment by ID
     */
    static async getPaymentById(paymentId) {
        return await prisma_1.prisma.payment.findUnique({
            where: { id: paymentId },
            include: {
                appointment: true,
                tutor: {
                    include: {
                        user: true
                    }
                },
                student: true
            }
        });
    }
    /**
     * Calculate appointment price based on tutor rates and duration
     * Returns price in fen (BigInt)
     */
    static calculateAppointmentPrice(tutor, durationMinutes) {
        if (tutor.isFree) {
            return 0n;
        }
        // Use half-hour rate if duration is 30 minutes or less
        if (durationMinutes <= 30 && tutor.halfHourRate > 0n) {
            return tutor.halfHourRate;
        }
        // Use hourly rate for longer durations
        const hours = durationMinutes / 60;
        const hourlyRateNumber = Number(tutor.hourlyRate);
        const totalPrice = hourlyRateNumber * hours;
        return BigInt(Math.round(totalPrice));
    }
    /**
     * Process refund for a payment
     */
    static async processRefund(paymentId, refundAmount, reason, adminId) {
        (0, currency_1.validateNonNegativeFen)(refundAmount);
        const payment = await prisma_1.prisma.payment.findUnique({
            where: { id: paymentId },
            include: {
                appointment: true,
                splits: true
            }
        });
        if (!payment) {
            throw new Error('Payment not found');
        }
        if (payment.status !== 'completed') {
            throw new Error('Can only refund completed payments');
        }
        if (refundAmount > payment.amount) {
            throw new Error('Refund amount cannot exceed payment amount');
        }
        // Update payment with refund information
        const updatedPayment = await prisma_1.prisma.payment.update({
            where: { id: paymentId },
            data: {
                status: 'refunded',
                refundedAt: new Date(),
                refundAmount,
                refundReason: reason,
                refundedById: adminId
            },
            include: {
                appointment: true,
                student: true,
                tutor: {
                    include: {
                        user: true
                    }
                }
            }
        });
        // Update appointment status
        await prisma_1.prisma.appointment.update({
            where: { id: payment.appointmentId },
            data: {
                paymentStatus: 'refunded'
            }
        });
        // Cancel any pending splits
        await prisma_1.prisma.paymentSplit.updateMany({
            where: {
                paymentId: paymentId,
                status: 'pending'
            },
            data: {
                status: 'cancelled'
            }
        });
        console.log(`[Mock Refund] Refunding ${(0, currency_1.formatFenAsYuan)(refundAmount)} CNY to student ${updatedPayment.student.name}`);
        return updatedPayment;
    }
    /**
     * Get payment statistics for admin dashboard
     */
    static async getPaymentStats() {
        const [totalPayments, completedPayments, refundedPayments, pendingPayments] = await Promise.all([
            prisma_1.prisma.payment.count(),
            prisma_1.prisma.payment.count({ where: { status: 'completed' } }),
            prisma_1.prisma.payment.count({ where: { status: 'refunded' } }),
            prisma_1.prisma.payment.count({ where: { status: 'pending' } })
        ]);
        const [totalAmount, completedAmount, refundedAmount] = await Promise.all([
            prisma_1.prisma.payment.aggregate({
                _sum: { amount: true }
            }),
            prisma_1.prisma.payment.aggregate({
                where: { status: 'completed' },
                _sum: { amount: true }
            }),
            prisma_1.prisma.payment.aggregate({
                where: { status: 'refunded' },
                _sum: { refundAmount: true }
            })
        ]);
        return {
            totalPayments,
            completedPayments,
            refundedPayments,
            pendingPayments,
            totalAmount: totalAmount._sum.amount || 0n,
            completedAmount: completedAmount._sum.amount || 0n,
            refundedAmount: refundedAmount._sum.refundAmount || 0n
        };
    }
    /**
     * Get all payments with pagination for admin management
     */
    static async getAllPayments(options = {}) {
        const { page = 1, limit = 10, status, search } = options;
        const skip = (page - 1) * limit;
        const where = {};
        if (status) {
            where.status = status;
        }
        if (search) {
            where.OR = [
                { student: { name: { contains: search, mode: 'insensitive' } } },
                { student: { email: { contains: search, mode: 'insensitive' } } },
                { tutor: { user: { name: { contains: search, mode: 'insensitive' } } } },
                { transactionId: { contains: search, mode: 'insensitive' } }
            ];
        }
        const [payments, total] = await Promise.all([
            prisma_1.prisma.payment.findMany({
                where,
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
                include: {
                    appointment: true,
                    student: true,
                    tutor: {
                        include: {
                            user: true
                        }
                    },
                    refundedBy: true,
                    splits: true
                }
            }),
            prisma_1.prisma.payment.count({ where })
        ]);
        return {
            payments,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
        };
    }
    /**
     * Process payment split manually (admin action)
     */
    static async processSplitManually(splitId, adminId) {
        const split = await prisma_1.prisma.paymentSplit.findUnique({
            where: { id: splitId },
            include: {
                payment: true,
                tutor: {
                    include: {
                        user: true
                    }
                }
            }
        });
        if (!split) {
            throw new Error('Payment split not found');
        }
        if (split.status !== 'pending') {
            throw new Error('Can only process pending splits');
        }
        if (split.payment.status !== 'completed') {
            throw new Error('Cannot process split for non-completed payment');
        }
        const updatedSplit = await prisma_1.prisma.paymentSplit.update({
            where: { id: splitId },
            data: {
                status: 'completed',
                transferredAt: new Date(),
                wechatTransferId: `manual_transfer_${Date.now()}`,
                processedById: adminId
            },
            include: {
                tutor: {
                    include: {
                        user: true
                    }
                }
            }
        });
        console.log(`[Manual Transfer] Admin processed transfer of ${(0, currency_1.formatFenAsYuan)(split.tutorAmount)} CNY to tutor ${split.tutor.user.name}`);
        return updatedSplit;
    }
    /**
     * Cancel payment split (admin action)
     */
    static async cancelSplit(splitId, adminId, reason) {
        const split = await prisma_1.prisma.paymentSplit.findUnique({
            where: { id: splitId },
            include: {
                tutor: {
                    include: {
                        user: true
                    }
                }
            }
        });
        if (!split) {
            throw new Error('Payment split not found');
        }
        if (split.status !== 'pending') {
            throw new Error('Can only cancel pending splits');
        }
        const updatedSplit = await prisma_1.prisma.paymentSplit.update({
            where: { id: splitId },
            data: {
                status: 'cancelled',
                failureReason: reason,
                processedById: adminId
            }
        });
        console.log(`[Split Cancelled] Admin cancelled split for tutor ${split.tutor.user.name}: ${reason}`);
        return updatedSplit;
    }
    /**
     * Get payment split statistics
     */
    static async getSplitStats() {
        const [totalSplits, pendingSplits, completedSplits, cancelledSplits] = await Promise.all([
            prisma_1.prisma.paymentSplit.count(),
            prisma_1.prisma.paymentSplit.count({ where: { status: 'pending' } }),
            prisma_1.prisma.paymentSplit.count({ where: { status: 'completed' } }),
            prisma_1.prisma.paymentSplit.count({ where: { status: 'cancelled' } })
        ]);
        const [totalTutorAmount, completedTutorAmount] = await Promise.all([
            prisma_1.prisma.paymentSplit.aggregate({
                _sum: { tutorAmount: true }
            }),
            prisma_1.prisma.paymentSplit.aggregate({
                where: { status: 'completed' },
                _sum: { tutorAmount: true }
            })
        ]);
        return {
            totalSplits,
            pendingSplits,
            completedSplits,
            cancelledSplits,
            totalTutorAmount: totalTutorAmount._sum.tutorAmount || 0n,
            completedTutorAmount: completedTutorAmount._sum.tutorAmount || 0n
        };
    }
    /**
     * Get all payment splits with pagination for admin management
     */
    static async getAllSplits(options = {}) {
        const { page = 1, limit = 10, status, search } = options;
        const skip = (page - 1) * limit;
        const where = {};
        if (status) {
            where.status = status;
        }
        if (search) {
            where.OR = [
                { tutor: { user: { name: { contains: search, mode: 'insensitive' } } } },
                { tutor: { user: { email: { contains: search, mode: 'insensitive' } } } },
                { wechatTransferId: { contains: search, mode: 'insensitive' } }
            ];
        }
        const [splits, total] = await Promise.all([
            prisma_1.prisma.paymentSplit.findMany({
                where,
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
                include: {
                    payment: {
                        include: {
                            appointment: true,
                            student: true
                        }
                    },
                    tutor: {
                        include: {
                            user: true
                        }
                    },
                    processedBy: true
                }
            }),
            prisma_1.prisma.paymentSplit.count({ where })
        ]);
        return {
            splits,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
        };
    }
}
exports.PaymentService = PaymentService;
