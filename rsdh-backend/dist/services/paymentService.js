"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentService = void 0;
const prisma_1 = require("../lib/prisma");
const crypto_1 = __importDefault(require("crypto"));
class PaymentService {
    constructor(app) {
        this.app = app;
    }
    /**
     * Create a new payment record
     */
    static async createPayment(data) {
        return await prisma_1.prisma.payment.create({
            data: {
                appointmentId: data.appointmentId,
                tutorId: data.tutorId,
                studentId: data.studentId,
                amount: data.amount,
                currency: data.currency || 'CNY',
                paymentMethod: data.paymentMethod || 'wechat',
                status: 'pending'
            },
            include: {
                appointment: true,
                tutor: {
                    include: {
                        user: true
                    }
                },
                student: true
            }
        });
    }
    /**
     * Process WeChat payment (mock implementation for testing)
     */
    async processWeChatPayment(paymentId, request) {
        const payment = await prisma_1.prisma.payment.findUnique({
            where: { id: paymentId },
            include: { appointment: true }
        });
        if (!payment) {
            throw new Error('Payment not found');
        }
        // Check if WeChat Pay is configured
        const isWeChatConfigured = this.app.config.WECHAT_PAY_APP_ID &&
            this.app.config.WECHAT_PAY_MCH_ID &&
            this.app.config.WECHAT_PAY_API_KEY;
        if (!isWeChatConfigured) {
            // Mock payment for testing
            this.app.log.info(`[Mock WeChat Pay] Processing payment ${paymentId} for amount ${request.amount}`);
            // Simulate payment processing delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            // Mock successful payment
            await prisma_1.prisma.payment.update({
                where: { id: paymentId },
                data: {
                    status: 'completed',
                    transactionId: `mock_${Date.now()}`,
                    wechatOrderId: request.outTradeNo,
                    paidAt: new Date()
                }
            });
            // Return mock response
            return {
                prepayId: `mock_prepay_${Date.now()}`,
                paySign: this.generateMockSign(),
                timeStamp: Math.floor(Date.now() / 1000).toString(),
                nonceStr: this.generateNonceStr(),
                package: `prepay_id=mock_prepay_${Date.now()}`,
                signType: 'MD5'
            };
        }
        // TODO: Implement real WeChat Pay integration
        // This would involve calling WeChat Pay API with proper authentication
        throw new Error('WeChat Pay integration not implemented yet');
    }
    /**
     * Handle payment completion and trigger split processing
     */
    static async completePayment(paymentId, transactionId) {
        const payment = await prisma_1.prisma.payment.update({
            where: { id: paymentId },
            data: {
                status: 'completed',
                transactionId,
                paidAt: new Date()
            },
            include: {
                appointment: true,
                tutor: true
            }
        });
        // Create payment split record
        const tutorPercentage = parseFloat(process.env.TUTOR_COMMISSION_RATE || '0.7');
        const tutorAmount = payment.amount * tutorPercentage;
        const platformAmount = payment.amount - tutorAmount;
        await PaymentService.createPaymentSplit({
            paymentId: payment.id,
            tutorId: payment.tutorId,
            tutorAmount,
            platformAmount,
            tutorPercentage
        });
        // Update appointment status to confirmed
        await prisma_1.prisma.appointment.update({
            where: { id: payment.appointmentId },
            data: {
                paymentStatus: 'paid',
                confirmationStatus: 'confirmed'
            }
        });
        return payment;
    }
    /**
     * Create payment split record
     */
    static async createPaymentSplit(data) {
        return await prisma_1.prisma.paymentSplit.create({
            data: {
                paymentId: data.paymentId,
                tutorId: data.tutorId,
                tutorAmount: data.tutorAmount,
                platformAmount: data.platformAmount,
                tutorPercentage: data.tutorPercentage,
                status: 'pending'
            }
        });
    }
    /**
     * Process payment splits 48 hours after appointment completion
     */
    static async processPendingSplits() {
        const cutoffTime = new Date();
        cutoffTime.setHours(cutoffTime.getHours() - 48);
        const pendingSplits = await prisma_1.prisma.paymentSplit.findMany({
            where: {
                status: 'pending',
                payment: {
                    appointment: {
                        status: 'completed',
                        endTime: {
                            lte: cutoffTime
                        }
                    }
                }
            },
            include: {
                payment: {
                    include: {
                        appointment: true
                    }
                },
                tutor: {
                    include: {
                        user: true
                    }
                }
            }
        });
        for (const split of pendingSplits) {
            try {
                await PaymentService.transferToTutor(split.id);
            }
            catch (error) {
                console.error(`Failed to process split ${split.id}:`, error);
            }
        }
    }
    /**
     * Transfer money to tutor (mock implementation)
     */
    static async transferToTutor(splitId) {
        const split = await prisma_1.prisma.paymentSplit.findUnique({
            where: { id: splitId },
            include: {
                tutor: {
                    include: {
                        user: true
                    }
                }
            }
        });
        if (!split) {
            throw new Error('Payment split not found');
        }
        // Mock transfer for testing
        console.log(`[Mock Transfer] Transferring ${split.tutorAmount} CNY to tutor ${split.tutor.user.name}`);
        await prisma_1.prisma.paymentSplit.update({
            where: { id: splitId },
            data: {
                status: 'completed',
                transferredAt: new Date(),
                wechatTransferId: `mock_transfer_${Date.now()}`
            }
        });
        return split;
    }
    /**
     * Generate mock payment signature for testing
     */
    generateMockSign() {
        return crypto_1.default.randomBytes(16).toString('hex');
    }
    /**
     * Generate random nonce string
     */
    generateNonceStr() {
        return crypto_1.default.randomBytes(16).toString('hex');
    }
    /**
     * Get payment by ID
     */
    static async getPaymentById(paymentId) {
        return await prisma_1.prisma.payment.findUnique({
            where: { id: paymentId },
            include: {
                appointment: true,
                tutor: {
                    include: {
                        user: true
                    }
                },
                student: true
            }
        });
    }
    /**
     * Calculate appointment price based on tutor rates and duration
     */
    static calculateAppointmentPrice(tutor, durationMinutes) {
        if (tutor.isFree) {
            return 0;
        }
        // Use half-hour rate if duration is 30 minutes or less
        if (durationMinutes <= 30 && tutor.halfHourRate > 0) {
            return tutor.halfHourRate;
        }
        // Use hourly rate for longer durations
        const hours = durationMinutes / 60;
        return tutor.hourlyRate * hours;
    }
}
exports.PaymentService = PaymentService;
