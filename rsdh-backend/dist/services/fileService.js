"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileService = void 0;
const prisma_1 = require("../lib/prisma");
const path = __importStar(require("path"));
class FileService {
    constructor(fastify, s3Service) {
        this.fastify = fastify;
        this.s3Service = s3Service;
    }
    /**
     * Validate file type and size
     */
    validateFile(file) {
        const allowedTypes = this.fastify.config.ALLOWED_FILE_TYPES.split(',');
        const maxSize = this.fastify.config.MAX_FILE_SIZE;
        if (!allowedTypes.includes(file.mimeType)) {
            return {
                valid: false,
                error: `File type ${file.mimeType} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
            };
        }
        if (file.size > maxSize) {
            return {
                valid: false,
                error: `File size ${file.size} bytes exceeds maximum allowed size of ${maxSize} bytes`
            };
        }
        return { valid: true };
    }
    /**
     * Upload file and save record to database
     */
    async uploadFile(userId, fileData) {
        // Validate file
        const validation = this.validateFile(fileData);
        if (!validation.valid) {
            throw new Error(validation.error);
        }
        // Generate S3 key
        const category = fileData.category || 'general';
        const s3Key = this.s3Service.generateFileKey(fileData.originalName, category);
        // Upload to S3 (or mock for development)
        let uploadResult;
        try {
            uploadResult = await this.s3Service.uploadFile(fileData.buffer, fileData.originalName, {
                key: s3Key,
                contentType: fileData.mimeType,
                isPublic: fileData.isPublic || false,
                metadata: fileData.metadata ? { custom: JSON.stringify(fileData.metadata) } : undefined,
            });
        }
        catch (error) {
            // For development, create a mock upload result if S3 is not configured
            console.warn('S3 upload failed, using mock result for development:', error);
            uploadResult = {
                key: s3Key,
                url: `http://localhost:3001/mock-files/${s3Key}`,
                size: fileData.size,
            };
        }
        // Save to database
        const fileRecord = await prisma_1.prisma.file.create({
            data: {
                originalName: fileData.originalName,
                fileName: path.basename(s3Key),
                mimeType: fileData.mimeType,
                size: fileData.size,
                s3Key: uploadResult.key,
                s3Bucket: this.s3Service['config']?.bucketName || 'mock-bucket',
                s3Region: this.s3Service['config']?.region || 'mock-region',
                cdnUrl: this.s3Service['config']?.cdnBaseUrl ? uploadResult.url : null,
                uploadedById: userId,
                category,
                isPublic: fileData.isPublic || false,
                metadata: fileData.metadata || null,
            },
        });
        return fileRecord;
    }
    /**
     * Get file by ID
     */
    async getFileById(fileId, userId) {
        const file = await prisma_1.prisma.file.findUnique({
            where: { id: fileId },
        });
        if (!file) {
            return null;
        }
        // Check access permissions
        if (!file.isPublic && userId && file.uploadedById !== userId) {
            throw new Error('Access denied');
        }
        return file;
    }
    /**
     * Get file access URL
     */
    async getFileUrl(fileId, userId, expiresIn = 3600) {
        const file = await this.getFileById(fileId, userId);
        if (!file) {
            throw new Error('File not found');
        }
        // If file is public and CDN is available, return CDN URL
        if (file.isPublic && file.cdnUrl) {
            return file.cdnUrl;
        }
        // If file is public, return direct S3 URL
        if (file.isPublic) {
            return this.s3Service.getFileUrl(file.s3Key);
        }
        // For private files, generate presigned URL
        return await this.s3Service.getPresignedDownloadUrl(file.s3Key, expiresIn);
    }
    /**
     * Delete file
     */
    async deleteFile(fileId, userId) {
        const file = await prisma_1.prisma.file.findUnique({
            where: { id: fileId },
        });
        if (!file) {
            throw new Error('File not found');
        }
        // Check ownership
        if (file.uploadedById !== userId) {
            throw new Error('Access denied');
        }
        // Delete from S3 (or skip for mock)
        try {
            await this.s3Service.deleteFile(file.s3Key);
        }
        catch (error) {
            console.warn('S3 delete failed, continuing with database deletion:', error);
        }
        // Delete from database
        await prisma_1.prisma.file.delete({
            where: { id: fileId },
        });
    }
    /**
     * List files with pagination and filters
     */
    async listFiles(options = {}) {
        const { userId, category, isPublic, page = 1, limit = 20, search, } = options;
        const where = {};
        if (userId) {
            where.uploadedById = userId;
        }
        if (category) {
            where.category = category;
        }
        if (typeof isPublic === 'boolean') {
            where.isPublic = isPublic;
        }
        if (search) {
            where.OR = [
                { originalName: { contains: search, mode: 'insensitive' } },
                { fileName: { contains: search, mode: 'insensitive' } },
            ];
        }
        const [files, total] = await Promise.all([
            prisma_1.prisma.file.findMany({
                where,
                orderBy: { createdAt: 'desc' },
                skip: (page - 1) * limit,
                take: limit,
            }),
            prisma_1.prisma.file.count({ where }),
        ]);
        return {
            files: files,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    /**
     * Get user's storage usage
     */
    async getUserStorageUsage(userId) {
        const files = await prisma_1.prisma.file.findMany({
            where: { uploadedById: userId },
            select: { size: true, category: true },
        });
        const totalFiles = files.length;
        const totalSize = files.reduce((sum, file) => sum + file.size, 0);
        const sizeByCategory = {};
        files.forEach(file => {
            if (!sizeByCategory[file.category]) {
                sizeByCategory[file.category] = { count: 0, size: 0 };
            }
            sizeByCategory[file.category].count++;
            sizeByCategory[file.category].size += file.size;
        });
        return {
            totalFiles,
            totalSize,
            sizeByCategory,
        };
    }
    /**
     * Generate presigned upload URL for direct client upload
     */
    async generateUploadUrl(userId, fileName, contentType, category = 'general', expiresIn = 3600) {
        // Validate content type
        const allowedTypes = this.fastify.config.ALLOWED_FILE_TYPES.split(',');
        if (!allowedTypes.includes(contentType)) {
            throw new Error(`Content type ${contentType} is not allowed`);
        }
        // Generate S3 key
        const s3Key = this.s3Service.generateFileKey(fileName, category);
        // Create file record in pending state
        const fileRecord = await prisma_1.prisma.file.create({
            data: {
                originalName: fileName,
                fileName: path.basename(s3Key),
                mimeType: contentType,
                size: 0, // Will be updated after upload
                s3Key,
                s3Bucket: this.s3Service['config'].bucketName,
                s3Region: this.s3Service['config'].region,
                cdnUrl: null,
                uploadedById: userId,
                category,
                isPublic: false,
                metadata: { status: 'pending' },
            },
        });
        // Generate presigned URL
        const uploadUrl = await this.s3Service.getPresignedUploadUrl(s3Key, contentType, expiresIn);
        return {
            uploadUrl,
            key: s3Key,
            fileId: fileRecord.id,
        };
    }
    /**
     * Confirm upload completion and update file record
     */
    async confirmUpload(fileId, userId) {
        const file = await prisma_1.prisma.file.findUnique({
            where: { id: fileId },
        });
        if (!file || file.uploadedById !== userId) {
            throw new Error('File not found or access denied');
        }
        // Get file info from S3
        const s3Info = await this.s3Service.getFileInfo(file.s3Key);
        if (!s3Info) {
            throw new Error('File not found in storage');
        }
        // Update file record
        const updatedFile = await prisma_1.prisma.file.update({
            where: { id: fileId },
            data: {
                size: s3Info.size,
                cdnUrl: this.s3Service['config'].cdnBaseUrl
                    ? this.s3Service.getFileUrl(file.s3Key)
                    : null,
                metadata: { status: 'completed' },
            },
        });
        return updatedFile;
    }
}
exports.FileService = FileService;
