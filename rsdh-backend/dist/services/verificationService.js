"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationService = void 0;
const prisma_1 = require("../lib/prisma");
const emailService_1 = require("./emailService");
class VerificationService {
    constructor(app) {
        this.app = app;
        this.emailService = new emailService_1.EmailService(app);
    }
    /**
     * Send verification code
     */
    async sendVerificationCode(email, type) {
        // Generate verification code
        const code = emailService_1.EmailService.generateVerificationCode();
        const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
        try {
            // Store verification code in database
            await prisma_1.prisma.verificationCode.create({
                data: {
                    email,
                    code,
                    type,
                    expiresAt,
                    used: false,
                },
            });
            // Send email
            await this.emailService.sendVerificationCode({
                email,
                code,
                type,
            });
            this.app.log.info(`Verification code sent to ${email} for ${type}`);
        }
        catch (error) {
            this.app.log.error('Failed to send verification code:', error);
            throw new Error('Failed to send verification code');
        }
    }
    /**
     * Verify code
     */
    async verifyCode(email, code, type) {
        try {
            // Find valid verification code
            const verificationCode = await prisma_1.prisma.verificationCode.findFirst({
                where: {
                    email,
                    code,
                    type,
                    used: false,
                    expiresAt: {
                        gt: new Date(),
                    },
                },
            });
            if (!verificationCode) {
                return false;
            }
            // Mark as used
            await prisma_1.prisma.verificationCode.update({
                where: { id: verificationCode.id },
                data: { used: true },
            });
            this.app.log.info(`Verification code verified for ${email} (${type})`);
            return true;
        }
        catch (error) {
            this.app.log.error('Failed to verify code:', error);
            return false;
        }
    }
    /**
     * Clean up expired verification codes
     */
    async cleanupExpiredCodes() {
        try {
            const result = await prisma_1.prisma.verificationCode.deleteMany({
                where: {
                    OR: [
                        { expiresAt: { lt: new Date() } },
                        { used: true },
                    ],
                },
            });
            this.app.log.info(`Cleaned up ${result.count} expired verification codes`);
        }
        catch (error) {
            this.app.log.error('Failed to cleanup expired codes:', error);
        }
    }
    /**
     * Check if user can request new code (rate limiting)
     */
    async canRequestNewCode(email, type) {
        try {
            // Check if there's a recent code (within last minute)
            const recentCode = await prisma_1.prisma.verificationCode.findFirst({
                where: {
                    email,
                    type,
                    createdAt: {
                        gt: new Date(Date.now() - 60 * 1000), // 1 minute ago
                    },
                },
            });
            return !recentCode;
        }
        catch (error) {
            this.app.log.error('Failed to check rate limit:', error);
            return false;
        }
    }
    /**
     * Get verification code attempts count
     */
    async getAttemptCount(email, type) {
        try {
            const count = await prisma_1.prisma.verificationCode.count({
                where: {
                    email,
                    type,
                    createdAt: {
                        gt: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
                    },
                },
            });
            return count;
        }
        catch (error) {
            this.app.log.error('Failed to get attempt count:', error);
            return 0;
        }
    }
    /**
     * Check if user has exceeded daily limit
     */
    async hasExceededDailyLimit(email, type) {
        const attemptCount = await this.getAttemptCount(email, type);
        const dailyLimit = 5; // Maximum 5 attempts per day
        return attemptCount >= dailyLimit;
    }
}
exports.VerificationService = VerificationService;
