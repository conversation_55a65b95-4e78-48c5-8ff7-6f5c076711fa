"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationMaterialService = void 0;
const prisma_1 = require("../lib/prisma");
class VerificationMaterialService {
    /**
     * Add education verification material
     */
    static async addEducationVerification(data) {
        // Validate education exists
        const education = await prisma_1.prisma.tutorEducation.findUnique({
            where: { id: data.educationId }
        });
        if (!education) {
            throw new Error('Education record not found');
        }
        // Validate file exists
        const file = await prisma_1.prisma.file.findUnique({
            where: { id: data.fileId }
        });
        if (!file) {
            throw new Error('File not found');
        }
        return await prisma_1.prisma.educationVerification.create({
            data: {
                educationId: data.educationId,
                fileId: data.fileId,
                materialType: data.materialType,
                description: data.description,
            },
            include: {
                file: true,
                education: true,
            }
        });
    }
    /**
     * Add career verification material
     */
    static async addCareerVerification(data) {
        // Validate career exists
        const career = await prisma_1.prisma.tutorCareer.findUnique({
            where: { id: data.careerId }
        });
        if (!career) {
            throw new Error('Career record not found');
        }
        // Validate file exists
        const file = await prisma_1.prisma.file.findUnique({
            where: { id: data.fileId }
        });
        if (!file) {
            throw new Error('File not found');
        }
        return await prisma_1.prisma.careerVerification.create({
            data: {
                careerId: data.careerId,
                fileId: data.fileId,
                materialType: data.materialType,
                description: data.description,
            },
            include: {
                file: true,
                career: true,
            }
        });
    }
    /**
     * Get education verification materials
     */
    static async getEducationVerifications(educationId) {
        return await prisma_1.prisma.educationVerification.findMany({
            where: { educationId },
            include: {
                file: true,
                reviewedBy: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    }
                }
            },
            orderBy: { createdAt: 'desc' }
        });
    }
    /**
     * Get career verification materials
     */
    static async getCareerVerifications(careerId) {
        return await prisma_1.prisma.careerVerification.findMany({
            where: { careerId },
            include: {
                file: true,
                reviewedBy: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    }
                }
            },
            orderBy: { createdAt: 'desc' }
        });
    }
    /**
     * Get all verification materials for a tutor
     */
    static async getTutorVerifications(tutorId) {
        const educationVerifications = await prisma_1.prisma.educationVerification.findMany({
            where: {
                education: {
                    tutorId
                }
            },
            include: {
                file: true,
                education: true,
                reviewedBy: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    }
                }
            },
            orderBy: { createdAt: 'desc' }
        });
        const careerVerifications = await prisma_1.prisma.careerVerification.findMany({
            where: {
                career: {
                    tutorId
                }
            },
            include: {
                file: true,
                career: true,
                reviewedBy: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    }
                }
            },
            orderBy: { createdAt: 'desc' }
        });
        return {
            educationVerifications,
            careerVerifications
        };
    }
    /**
     * Update education verification status (admin only)
     */
    static async updateEducationVerificationStatus(verificationId, data) {
        const verification = await prisma_1.prisma.educationVerification.findUnique({
            where: { id: verificationId }
        });
        if (!verification) {
            throw new Error('Education verification not found');
        }
        return await prisma_1.prisma.educationVerification.update({
            where: { id: verificationId },
            data: {
                status: data.status,
                reviewNotes: data.reviewNotes,
                reviewedById: data.reviewedById,
                reviewedAt: new Date(),
            },
            include: {
                file: true,
                education: true,
                reviewedBy: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    }
                }
            }
        });
    }
    /**
     * Update career verification status (admin only)
     */
    static async updateCareerVerificationStatus(verificationId, data) {
        const verification = await prisma_1.prisma.careerVerification.findUnique({
            where: { id: verificationId }
        });
        if (!verification) {
            throw new Error('Career verification not found');
        }
        return await prisma_1.prisma.careerVerification.update({
            where: { id: verificationId },
            data: {
                status: data.status,
                reviewNotes: data.reviewNotes,
                reviewedById: data.reviewedById,
                reviewedAt: new Date(),
            },
            include: {
                file: true,
                career: true,
                reviewedBy: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    }
                }
            }
        });
    }
    /**
     * Delete education verification material
     */
    static async deleteEducationVerification(verificationId, userId) {
        const verification = await prisma_1.prisma.educationVerification.findUnique({
            where: { id: verificationId },
            include: {
                education: {
                    include: {
                        tutor: true
                    }
                }
            }
        });
        if (!verification) {
            throw new Error('Education verification not found');
        }
        // Check if user owns this verification
        if (verification.education.tutor.userId !== userId) {
            throw new Error('Unauthorized to delete this verification');
        }
        await prisma_1.prisma.educationVerification.delete({
            where: { id: verificationId }
        });
    }
    /**
     * Delete career verification material
     */
    static async deleteCareerVerification(verificationId, userId) {
        const verification = await prisma_1.prisma.careerVerification.findUnique({
            where: { id: verificationId },
            include: {
                career: {
                    include: {
                        tutor: true
                    }
                }
            }
        });
        if (!verification) {
            throw new Error('Career verification not found');
        }
        // Check if user owns this verification
        if (verification.career.tutor.userId !== userId) {
            throw new Error('Unauthorized to delete this verification');
        }
        await prisma_1.prisma.careerVerification.delete({
            where: { id: verificationId }
        });
    }
    /**
     * Get pending verifications for admin review
     */
    static async getPendingVerifications() {
        const educationVerifications = await prisma_1.prisma.educationVerification.findMany({
            where: { status: 'pending' },
            include: {
                file: true,
                education: {
                    include: {
                        tutor: {
                            include: {
                                user: {
                                    select: {
                                        id: true,
                                        name: true,
                                        email: true,
                                    }
                                }
                            }
                        }
                    }
                }
            },
            orderBy: { createdAt: 'asc' }
        });
        const careerVerifications = await prisma_1.prisma.careerVerification.findMany({
            where: { status: 'pending' },
            include: {
                file: true,
                career: {
                    include: {
                        tutor: {
                            include: {
                                user: {
                                    select: {
                                        id: true,
                                        name: true,
                                        email: true,
                                    }
                                }
                            }
                        }
                    }
                }
            },
            orderBy: { createdAt: 'asc' }
        });
        return {
            educationVerifications,
            careerVerifications
        };
    }
}
exports.VerificationMaterialService = VerificationMaterialService;
