"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TutorService = void 0;
const prisma_1 = require("../lib/prisma");
const currency_1 = require("../utils/currency");
class TutorService {
    /**
     * Apply to become a tutor
     */
    static async applyToBecomeTutor(data) {
        // Check if user already has a tutor profile
        const existingProfile = await prisma_1.prisma.tutorProfile.findUnique({
            where: { userId: data.userId }
        });
        if (existingProfile) {
            throw new Error('User already has a tutor profile');
        }
        // Validate user exists
        const user = await prisma_1.prisma.user.findUnique({
            where: { id: data.userId }
        });
        if (!user) {
            throw new Error('User not found');
        }
        // Create tutor profile with education and career in a transaction
        const tutorProfile = await prisma_1.prisma.$transaction(async (tx) => {
            // Convert rates to fen (BigInt)
            const rateFen = data.rate ? (0, currency_1.yuanToFen)(data.rate) : 0n;
            const hourlyRateFen = data.hourlyRate ? (0, currency_1.yuanToFen)(data.hourlyRate) : (data.rate ? (0, currency_1.yuanToFen)(data.rate) : 0n);
            const halfHourRateFen = data.halfHourRate ? (0, currency_1.yuanToFen)(data.halfHourRate) : (hourlyRateFen / 2n);
            // Validate rates
            if (!data.isFree) {
                (0, currency_1.validateNonNegativeFen)(rateFen);
                (0, currency_1.validateNonNegativeFen)(hourlyRateFen);
                (0, currency_1.validateNonNegativeFen)(halfHourRateFen);
            }
            // Create tutor profile
            const profile = await tx.tutorProfile.create({
                data: {
                    userId: data.userId,
                    title: data.title,
                    bio: data.bio,
                    rate: rateFen, // Keep for backward compatibility
                    hourlyRate: hourlyRateFen,
                    halfHourRate: halfHourRateFen,
                    isFree: data.isFree || false,
                    currency: data.currency || 'CNY',
                    status: 'pending'
                }
            });
            // Create education records
            if (data.education && data.education.length > 0) {
                await tx.tutorEducation.createMany({
                    data: data.education.map(edu => ({
                        tutorId: profile.id,
                        ...edu
                    }))
                });
            }
            // Create career records
            if (data.career && data.career.length > 0) {
                await tx.tutorCareer.createMany({
                    data: data.career.map(career => ({
                        tutorId: profile.id,
                        ...career
                    }))
                });
            }
            return profile;
        });
        return tutorProfile;
    }
    /**
     * Get tutor profile by user ID
     */
    static async getTutorByUserId(userId) {
        const tutor = await prisma_1.prisma.tutorProfile.findUnique({
            where: { userId },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        name: true,
                        avatar: true
                    }
                },
                education: {
                    orderBy: { startYear: 'desc' }
                },
                career: {
                    orderBy: { startYear: 'desc' }
                },
                availability: {
                    orderBy: { dayOfWeek: 'asc' }
                },
                _count: {
                    select: {
                        reviews: true,
                        appointments: true
                    }
                },
                reviews: {
                    select: {
                        rating: true
                    }
                }
            }
        });
        if (!tutor) {
            return null;
        }
        // Calculate average rating
        const averageRating = tutor.reviews.length > 0
            ? tutor.reviews.reduce((sum, review) => sum + review.rating, 0) / tutor.reviews.length
            : 0;
        // Remove reviews from the result and add averageRating
        const { reviews, ...tutorWithoutReviews } = tutor;
        return {
            ...tutorWithoutReviews,
            averageRating: Math.round(averageRating * 10) / 10 // Round to 1 decimal place
        };
    }
    /**
     * Get tutor profile by tutor ID
     */
    static async getTutorById(tutorId) {
        const tutor = await prisma_1.prisma.tutorProfile.findUnique({
            where: { id: tutorId },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        name: true,
                        avatar: true
                    }
                },
                education: {
                    orderBy: { startYear: 'desc' }
                },
                career: {
                    orderBy: { startYear: 'desc' }
                },
                availability: {
                    orderBy: { dayOfWeek: 'asc' }
                },
                _count: {
                    select: {
                        reviews: true,
                        appointments: true
                    }
                },
                reviews: {
                    select: {
                        rating: true
                    }
                }
            }
        });
        if (!tutor) {
            return null;
        }
        // Calculate average rating
        const averageRating = tutor.reviews.length > 0
            ? tutor.reviews.reduce((sum, review) => sum + review.rating, 0) / tutor.reviews.length
            : 0;
        // Remove reviews from the result and add averageRating
        const { reviews, ...tutorWithoutReviews } = tutor;
        return {
            ...tutorWithoutReviews,
            averageRating: Math.round(averageRating * 10) / 10 // Round to 1 decimal place
        };
    }
    /**
     * Update tutor profile
     */
    static async updateTutorProfile(tutorId, data) {
        const tutor = await prisma_1.prisma.tutorProfile.findUnique({
            where: { id: tutorId }
        });
        if (!tutor) {
            throw new Error('Tutor profile not found');
        }
        // Convert rates to fen if provided
        const updateData = {
            title: data.title,
            bio: data.bio,
            isFree: data.isFree,
            currency: data.currency,
            updatedAt: new Date()
        };
        if (data.rate !== undefined) {
            updateData.rate = data.rate ? (0, currency_1.yuanToFen)(data.rate) : 0n;
            (0, currency_1.validateNonNegativeFen)(updateData.rate);
        }
        if (data.hourlyRate !== undefined) {
            updateData.hourlyRate = data.hourlyRate ? (0, currency_1.yuanToFen)(data.hourlyRate) : 0n;
            (0, currency_1.validateNonNegativeFen)(updateData.hourlyRate);
        }
        if (data.halfHourRate !== undefined) {
            updateData.halfHourRate = data.halfHourRate ? (0, currency_1.yuanToFen)(data.halfHourRate) : 0n;
            (0, currency_1.validateNonNegativeFen)(updateData.halfHourRate);
        }
        return await prisma_1.prisma.tutorProfile.update({
            where: { id: tutorId },
            data: updateData
        });
    }
    /**
     * Get all tutors with pagination and filtering
     */
    static async getAllTutors(options = {}) {
        const { page = 1, limit = 10, status, search, institution, graduationYearFrom, graduationYearTo, career, degree, is985, is211, ratingFrom, ratingTo, priceFrom, priceTo } = options;
        const skip = (page - 1) * limit;
        const where = {};
        const educationFilters = [];
        const careerFilters = [];
        if (status) {
            where.status = status;
        }
        // Basic search
        if (search) {
            where.OR = [
                { title: { contains: search, mode: 'insensitive' } },
                { bio: { contains: search, mode: 'insensitive' } },
                { user: { name: { contains: search, mode: 'insensitive' } } }
            ];
        }
        // Institution filter (fuzzy search)
        if (institution) {
            educationFilters.push({
                institution: { contains: institution, mode: 'insensitive' }
            });
        }
        // Graduation year range filter
        if (graduationYearFrom || graduationYearTo) {
            const yearFilter = {};
            if (graduationYearFrom) {
                yearFilter.gte = graduationYearFrom;
            }
            if (graduationYearTo) {
                yearFilter.lte = graduationYearTo;
            }
            educationFilters.push({
                OR: [
                    { endYear: yearFilter },
                    { endYear: null, startYear: yearFilter } // For ongoing education
                ]
            });
        }
        // Degree filter (fuzzy search)
        if (degree) {
            educationFilters.push({
                degree: { contains: degree, mode: 'insensitive' }
            });
        }
        // 985/211 filters
        if (is985 !== undefined) {
            educationFilters.push({ is985 });
        }
        if (is211 !== undefined) {
            educationFilters.push({ is211 });
        }
        // Career filter (fuzzy search in title and company)
        if (career) {
            careerFilters.push({
                OR: [
                    { title: { contains: career, mode: 'insensitive' } },
                    { company: { contains: career, mode: 'insensitive' } }
                ]
            });
        }
        // Price range filter
        if (priceFrom !== undefined || priceTo !== undefined) {
            const priceFilter = {};
            if (priceFrom !== undefined) {
                priceFilter.gte = priceFrom;
            }
            if (priceTo !== undefined) {
                priceFilter.lte = priceTo;
            }
            where.hourlyRate = priceFilter;
        }
        // Apply education filters
        if (educationFilters.length > 0) {
            where.education = {
                some: {
                    AND: educationFilters
                }
            };
        }
        // Apply career filters
        if (careerFilters.length > 0) {
            where.career = {
                some: {
                    AND: careerFilters
                }
            };
        }
        const [tutors, total] = await Promise.all([
            prisma_1.prisma.tutorProfile.findMany({
                where,
                skip,
                take: limit,
                include: {
                    user: {
                        select: {
                            id: true,
                            email: true,
                            name: true,
                            avatar: true
                        }
                    },
                    education: {
                        orderBy: { startYear: 'desc' }
                    },
                    career: {
                        orderBy: { startYear: 'desc' }
                    },
                    availability: {
                        orderBy: { dayOfWeek: 'asc' }
                    },
                    _count: {
                        select: {
                            reviews: true,
                            appointments: true
                        }
                    },
                    reviews: {
                        select: {
                            rating: true
                        }
                    }
                },
                orderBy: { createdAt: 'desc' }
            }),
            prisma_1.prisma.tutorProfile.count({ where })
        ]);
        // Calculate average ratings for all tutors
        let tutorsWithRatings = tutors.map(tutor => {
            const averageRating = tutor.reviews.length > 0
                ? tutor.reviews.reduce((sum, review) => sum + review.rating, 0) / tutor.reviews.length
                : 0;
            const { reviews, ...tutorWithoutReviews } = tutor;
            return {
                ...tutorWithoutReviews,
                averageRating: Math.round(averageRating * 10) / 10
            };
        });
        // Apply rating filter after calculation (since rating is computed)
        if (ratingFrom !== undefined || ratingTo !== undefined) {
            tutorsWithRatings = tutorsWithRatings.filter(tutor => {
                const rating = tutor.averageRating;
                if (ratingFrom !== undefined && rating < ratingFrom)
                    return false;
                if (ratingTo !== undefined && rating > ratingTo)
                    return false;
                return true;
            });
        }
        // Recalculate total and pagination after rating filter
        const filteredTotal = tutorsWithRatings.length;
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedTutors = tutorsWithRatings.slice(startIndex, endIndex);
        return {
            tutors: paginatedTutors,
            total: filteredTotal,
            page,
            limit,
            totalPages: Math.ceil(filteredTotal / limit)
        };
    }
    /**
     * Approve or reject tutor application (admin only)
     */
    static async updateTutorStatus(tutorId, status) {
        const tutor = await prisma_1.prisma.tutorProfile.findUnique({
            where: { id: tutorId }
        });
        if (!tutor) {
            throw new Error('Tutor profile not found');
        }
        return await prisma_1.prisma.tutorProfile.update({
            where: { id: tutorId },
            data: {
                status,
                updatedAt: new Date()
            }
        });
    }
}
exports.TutorService = TutorService;
