"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationService = void 0;
const prisma_1 = require("../lib/prisma");
const emailService_1 = require("./emailService");
const crypto_1 = __importDefault(require("crypto"));
const currency_1 = require("../utils/currency");
class NotificationService {
    constructor(app) {
        this.app = app;
        this.emailService = new emailService_1.EmailService(app);
    }
    /**
     * Send appointment notification to tutor
     */
    async sendAppointmentNotification(data) {
        // Get appointment details with tutor and student info
        const appointment = await prisma_1.prisma.appointment.findUnique({
            where: { id: data.appointmentId },
            include: {
                tutor: {
                    include: {
                        user: true
                    }
                },
                student: true
            }
        });
        if (!appointment) {
            throw new Error('Appointment not found');
        }
        const tutorEmail = appointment.tutor.user.email;
        const tutorName = appointment.tutor.user.name || '导师';
        const studentName = appointment.student.name || '学生';
        let confirmationUrl;
        // Generate confirmation token and URL for paid appointments
        if (!data.isFree) {
            const confirmationToken = this.generateConfirmationToken();
            const expiresAt = new Date();
            expiresAt.setHours(expiresAt.getHours() + 24); // 24 hours to confirm
            // Update appointment with confirmation token
            await prisma_1.prisma.appointment.update({
                where: { id: data.appointmentId },
                data: {
                    confirmationToken,
                    confirmationExpiresAt: expiresAt
                }
            });
            confirmationUrl = `${this.app.config.APP_URL}/api/appointments/${data.appointmentId}/confirm?token=${confirmationToken}`;
        }
        // Send email notification
        await this.emailService.sendAppointmentNotification({
            tutorEmail,
            tutorName,
            studentName,
            appointmentDate: data.appointmentDate,
            appointmentTime: data.appointmentTime,
            duration: data.duration,
            meetingType: data.meetingType,
            price: (0, currency_1.fenToYuan)(data.price),
            isFree: data.isFree,
            confirmationUrl
        });
        this.app.log.info(`Appointment notification sent to tutor ${tutorEmail} for appointment ${data.appointmentId}`);
    }
    /**
     * Send appointment confirmation to student
     */
    async sendAppointmentConfirmation(appointmentId, status) {
        const appointment = await prisma_1.prisma.appointment.findUnique({
            where: { id: appointmentId },
            include: {
                tutor: {
                    include: {
                        user: true
                    }
                },
                student: true
            }
        });
        if (!appointment) {
            throw new Error('Appointment not found');
        }
        const studentEmail = appointment.student.email;
        const studentName = appointment.student.name || '学生';
        const tutorName = appointment.tutor.user.name || '导师';
        const appointmentDate = appointment.startTime.toLocaleDateString('zh-CN');
        const appointmentTime = appointment.startTime.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
        await this.emailService.sendAppointmentConfirmation({
            studentEmail,
            studentName,
            tutorName,
            appointmentDate,
            appointmentTime,
            status
        });
        this.app.log.info(`Appointment confirmation sent to student ${studentEmail} for appointment ${appointmentId} with status ${status}`);
    }
    /**
     * Confirm appointment via token
     */
    static async confirmAppointment(appointmentId, token) {
        const appointment = await prisma_1.prisma.appointment.findUnique({
            where: { id: appointmentId },
            include: {
                tutor: {
                    include: {
                        user: true
                    }
                },
                student: true
            }
        });
        if (!appointment) {
            throw new Error('Appointment not found');
        }
        // Check if token is valid and not expired
        if (appointment.confirmationToken !== token) {
            throw new Error('Invalid confirmation token');
        }
        if (!appointment.confirmationExpiresAt || appointment.confirmationExpiresAt < new Date()) {
            throw new Error('Confirmation token has expired');
        }
        // Update appointment status
        await prisma_1.prisma.appointment.update({
            where: { id: appointmentId },
            data: {
                confirmationStatus: 'confirmed',
                confirmationToken: null,
                confirmationExpiresAt: null
            }
        });
        return true;
    }
    /**
     * Reject appointment via token
     */
    static async rejectAppointment(appointmentId, token) {
        const appointment = await prisma_1.prisma.appointment.findUnique({
            where: { id: appointmentId }
        });
        if (!appointment) {
            throw new Error('Appointment not found');
        }
        // Check if token is valid and not expired
        if (appointment.confirmationToken !== token) {
            throw new Error('Invalid confirmation token');
        }
        if (!appointment.confirmationExpiresAt || appointment.confirmationExpiresAt < new Date()) {
            throw new Error('Confirmation token has expired');
        }
        // Update appointment status
        await prisma_1.prisma.appointment.update({
            where: { id: appointmentId },
            data: {
                confirmationStatus: 'rejected',
                status: 'cancelled',
                confirmationToken: null,
                confirmationExpiresAt: null
            }
        });
        // TODO: Process refund if payment was made
        return true;
    }
    /**
     * Generate secure confirmation token
     */
    generateConfirmationToken() {
        return crypto_1.default.randomBytes(32).toString('hex');
    }
    /**
     * Clean up expired confirmation tokens
     */
    static async cleanupExpiredTokens() {
        const now = new Date();
        await prisma_1.prisma.appointment.updateMany({
            where: {
                confirmationExpiresAt: {
                    lt: now
                },
                confirmationStatus: 'pending'
            },
            data: {
                confirmationStatus: 'rejected',
                status: 'cancelled',
                confirmationToken: null,
                confirmationExpiresAt: null
            }
        });
    }
}
exports.NotificationService = NotificationService;
