"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const prisma_1 = require("../lib/prisma");
const bcrypt_1 = __importDefault(require("bcrypt"));
class UserService {
    /**
     * Hash a password
     */
    static async hashPassword(password) {
        return bcrypt_1.default.hash(password, this.SALT_ROUNDS);
    }
    /**
     * Verify a password
     */
    static async verifyPassword(password, hashedPassword) {
        if (!hashedPassword)
            return false;
        return bcrypt_1.default.compare(password, hashedPassword);
    }
    /**
     * Create a new user
     */
    static async createUser(input) {
        const hashedPassword = await this.hashPassword(input.password);
        const user = await prisma_1.prisma.user.create({
            data: {
                email: input.email,
                password: hashedPassword,
                name: input.name,
                avatar: input.avatar,
                role: input.role || 'user',
            },
        });
        return this.toUserResponse(user);
    }
    /**
     * Find user by email
     */
    static async findByEmail(email) {
        const user = await prisma_1.prisma.user.findUnique({
            where: { email },
        });
        return user ? this.toUserResponse(user) : null;
    }
    /**
     * Find user by email for authentication (includes password)
     */
    static async findByEmailForAuth(email) {
        const user = await prisma_1.prisma.user.findUnique({
            where: { email },
        });
        return user;
    }
    /**
     * Find user by ID
     */
    static async findById(id) {
        const user = await prisma_1.prisma.user.findUnique({
            where: { id },
        });
        return user ? this.toUserResponse(user) : null;
    }
    /**
     * Get user by ID (alias for findById)
     */
    static async getUserById(id) {
        return this.findById(id);
    }
    /**
     * Update user
     */
    static async updateUser(id, input) {
        const user = await prisma_1.prisma.user.update({
            where: { id },
            data: input,
        });
        return this.toUserResponse(user);
    }
    /**
     * Delete user
     */
    static async deleteUser(id) {
        await prisma_1.prisma.user.delete({
            where: { id },
        });
    }
    /**
     * Get all users (admin only)
     */
    static async getAllUsers(page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const [users, total] = await Promise.all([
            prisma_1.prisma.user.findMany({
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
            }),
            prisma_1.prisma.user.count(),
        ]);
        return {
            users: users.map(this.toUserResponse),
            total,
        };
    }
    /**
     * Check if user exists
     */
    static async userExists(email) {
        const user = await prisma_1.prisma.user.findUnique({
            where: { email },
            select: { id: true },
        });
        return !!user;
    }
    /**
     * Enable/disable user
     */
    static async toggleUserStatus(id, disabled) {
        const user = await prisma_1.prisma.user.update({
            where: { id },
            data: { disabled },
        });
        return this.toUserResponse(user);
    }
    /**
     * Update user role
     */
    static async updateUserRole(id, role) {
        const user = await prisma_1.prisma.user.update({
            where: { id },
            data: { role },
        });
        return this.toUserResponse(user);
    }
    /**
     * Update user password
     */
    static async updatePassword(id, newPassword) {
        const hashedPassword = await this.hashPassword(newPassword);
        await prisma_1.prisma.user.update({
            where: { id },
            data: { password: hashedPassword },
        });
    }
    /**
     * Find user by phone number
     */
    static async findByPhoneNumber(phoneNumber) {
        const user = await prisma_1.prisma.user.findUnique({
            where: { phoneNumber },
        });
        return user ? this.toUserResponse(user) : null;
    }
    /**
     * Find user by phone number for authentication (includes password)
     */
    static async findByPhoneNumberForAuth(phoneNumber) {
        const user = await prisma_1.prisma.user.findUnique({
            where: { phoneNumber },
        });
        return user;
    }
    /**
     * Create user with phone number
     */
    static async createUserWithPhone(input) {
        const hashedPassword = input.password ? await this.hashPassword(input.password) : null;
        const user = await prisma_1.prisma.user.create({
            data: {
                email: input.email,
                password: hashedPassword,
                name: input.name,
                avatar: input.avatar,
                role: input.role || 'user',
                phoneNumber: input.phoneNumber,
                phoneNumberVerified: !!input.phoneNumber, // If phone number is provided, assume it's verified
            },
        });
        return this.toUserResponse(user);
    }
    /**
     * Update user's phone number
     */
    static async updatePhoneNumber(id, phoneNumber, verified = false) {
        const user = await prisma_1.prisma.user.update({
            where: { id },
            data: {
                phoneNumber,
                phoneNumberVerified: verified,
            },
        });
        return this.toUserResponse(user);
    }
    /**
     * Check if phone number exists
     */
    static async phoneNumberExists(phoneNumber) {
        const user = await prisma_1.prisma.user.findUnique({
            where: { phoneNumber },
            select: { id: true },
        });
        return !!user;
    }
    /**
     * Verify user's phone number
     */
    static async verifyPhoneNumber(id) {
        const user = await prisma_1.prisma.user.update({
            where: { id },
            data: { phoneNumberVerified: true },
        });
        return this.toUserResponse(user);
    }
    /**
     * Bind WeChat account to user
     */
    static async bindWeChatAccount(userId, wechatData) {
        // This would typically be handled by Better-Auth's account linking
        // For now, we'll store it in the accounts table via Better-Auth
        await prisma_1.prisma.account.create({
            data: {
                userId,
                accountId: wechatData.openid,
                providerId: 'wechat',
                // Store additional WeChat data as JSON in a text field if needed
            },
        });
    }
    /**
     * Find user by WeChat OpenID
     */
    static async findByWeChatOpenId(openid) {
        const account = await prisma_1.prisma.account.findUnique({
            where: {
                providerId_accountId: {
                    providerId: 'wechat',
                    accountId: openid,
                },
            },
            include: {
                user: true,
            },
        });
        return account?.user ? this.toUserResponse(account.user) : null;
    }
    /**
     * Convert database user to response format (updated to include phone number)
     */
    static toUserResponse(user) {
        return {
            id: user.id,
            email: user.email,
            name: user.name,
            avatar: user.avatar,
            emailVerified: user.emailVerified,
            disabled: user.disabled,
            role: user.role,
            phoneNumber: user.phoneNumber,
            phoneNumberVerified: user.phoneNumberVerified,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
        };
    }
}
exports.UserService = UserService;
UserService.SALT_ROUNDS = 12;
