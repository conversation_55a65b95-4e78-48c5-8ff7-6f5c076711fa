"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulerService = void 0;
const paymentService_1 = require("./paymentService");
const notificationService_1 = require("./notificationService");
class SchedulerService {
    constructor(app) {
        this.intervals = [];
        this.app = app;
    }
    /**
     * Start all scheduled tasks
     */
    start() {
        this.app.log.info('Starting scheduler service...');
        // Process payment splits every hour
        const paymentSplitInterval = setInterval(async () => {
            try {
                await paymentService_1.PaymentService.processPendingSplits();
                this.app.log.info('Payment splits processed successfully');
            }
            catch (error) {
                this.app.log.error('Error processing payment splits:', error);
            }
        }, 60 * 60 * 1000); // 1 hour
        // Clean up expired confirmation tokens every 30 minutes
        const tokenCleanupInterval = setInterval(async () => {
            try {
                await notificationService_1.NotificationService.cleanupExpiredTokens();
                this.app.log.info('Expired confirmation tokens cleaned up');
            }
            catch (error) {
                this.app.log.error('Error cleaning up expired tokens:', error);
            }
        }, 30 * 60 * 1000); // 30 minutes
        this.intervals.push(paymentSplitInterval, tokenCleanupInterval);
        this.app.log.info('Scheduler service started');
    }
    /**
     * Stop all scheduled tasks
     */
    stop() {
        this.app.log.info('Stopping scheduler service...');
        this.intervals.forEach(interval => {
            clearInterval(interval);
        });
        this.intervals = [];
        this.app.log.info('Scheduler service stopped');
    }
    /**
     * Manually trigger payment split processing
     */
    async triggerPaymentSplitProcessing() {
        this.app.log.info('Manually triggering payment split processing...');
        try {
            await paymentService_1.PaymentService.processPendingSplits();
            this.app.log.info('Manual payment split processing completed');
        }
        catch (error) {
            this.app.log.error('Error in manual payment split processing:', error);
            throw error;
        }
    }
    /**
     * Manually trigger token cleanup
     */
    async triggerTokenCleanup() {
        this.app.log.info('Manually triggering token cleanup...');
        try {
            await notificationService_1.NotificationService.cleanupExpiredTokens();
            this.app.log.info('Manual token cleanup completed');
        }
        catch (error) {
            this.app.log.error('Error in manual token cleanup:', error);
            throw error;
        }
    }
}
exports.SchedulerService = SchedulerService;
