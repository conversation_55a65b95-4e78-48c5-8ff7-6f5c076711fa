"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TutorCareerService = void 0;
const prisma_1 = require("../lib/prisma");
class TutorCareerService {
    /**
     * Add career record for a tutor
     */
    static async addCareer(data) {
        // Validate years
        const currentYear = new Date().getFullYear();
        if (data.startYear < 1900 || data.startYear > currentYear) {
            throw new Error('Invalid start year');
        }
        if (!data.current && data.endYear && (data.endYear < data.startYear || data.endYear > currentYear)) {
            throw new Error('Invalid end year');
        }
        if (data.current && data.endYear) {
            throw new Error('Current position cannot have an end year');
        }
        if (!data.current && !data.endYear) {
            throw new Error('Non-current position must have an end year');
        }
        // Check if tutor exists
        const tutor = await prisma_1.prisma.tutorProfile.findUnique({
            where: { id: data.tutorId }
        });
        if (!tutor) {
            throw new Error('<PERSON><PERSON> profile not found');
        }
        // If this is marked as current, update other current positions
        if (data.current) {
            await prisma_1.prisma.tutorCareer.updateMany({
                where: {
                    tutorId: data.tutorId,
                    current: true
                },
                data: {
                    current: false
                }
            });
        }
        return await prisma_1.prisma.tutorCareer.create({
            data
        });
    }
    /**
     * Get all career records for a tutor
     */
    static async getTutorCareer(tutorId) {
        return await prisma_1.prisma.tutorCareer.findMany({
            where: { tutorId },
            orderBy: [
                { current: 'desc' },
                { startYear: 'desc' }
            ]
        });
    }
    /**
     * Get career record by ID
     */
    static async getCareerById(careerId) {
        return await prisma_1.prisma.tutorCareer.findUnique({
            where: { id: careerId }
        });
    }
    /**
     * Update career record
     */
    static async updateCareer(careerId, data) {
        const existing = await prisma_1.prisma.tutorCareer.findUnique({
            where: { id: careerId }
        });
        if (!existing) {
            throw new Error('Career record not found');
        }
        // Validate years if provided
        const currentYear = new Date().getFullYear();
        if (data.startYear && (data.startYear < 1900 || data.startYear > currentYear)) {
            throw new Error('Invalid start year');
        }
        const startYear = data.startYear ?? existing.startYear;
        const current = data.current ?? existing.current;
        // If updating to current position, endYear should be null
        // If updating to non-current position, endYear should be provided or kept from existing
        let endYear;
        if (current) {
            endYear = null; // Current positions don't have end year
        }
        else {
            endYear = data.endYear ?? existing.endYear;
            if (!endYear) {
                throw new Error('Non-current position must have an end year');
            }
            if (endYear < startYear || endYear > currentYear) {
                throw new Error('Invalid end year');
            }
        }
        // If this is being marked as current, update other current positions
        if (data.current && !existing.current) {
            await prisma_1.prisma.tutorCareer.updateMany({
                where: {
                    tutorId: existing.tutorId,
                    current: true,
                    id: { not: careerId }
                },
                data: {
                    current: false
                }
            });
        }
        return await prisma_1.prisma.tutorCareer.update({
            where: { id: careerId },
            data: {
                ...data,
                endYear,
                updatedAt: new Date()
            }
        });
    }
    /**
     * Delete career record
     */
    static async deleteCareer(careerId) {
        const existing = await prisma_1.prisma.tutorCareer.findUnique({
            where: { id: careerId }
        });
        if (!existing) {
            throw new Error('Career record not found');
        }
        await prisma_1.prisma.tutorCareer.delete({
            where: { id: careerId }
        });
    }
    /**
     * Bulk update career records for a tutor
     */
    static async bulkUpdateCareer(tutorId, careers) {
        // Validate tutor exists
        const tutor = await prisma_1.prisma.tutorProfile.findUnique({
            where: { id: tutorId }
        });
        if (!tutor) {
            throw new Error('Tutor profile not found');
        }
        // Validate all career data
        const currentYear = new Date().getFullYear();
        let currentCount = 0;
        for (const career of careers) {
            if (career.startYear < 1900 || career.startYear > currentYear) {
                throw new Error('Invalid start year');
            }
            if (!career.current && career.endYear && (career.endYear < career.startYear || career.endYear > currentYear)) {
                throw new Error('Invalid end year');
            }
            if (career.current && career.endYear) {
                throw new Error('Current position cannot have an end year');
            }
            if (!career.current && !career.endYear) {
                throw new Error('Non-current position must have an end year');
            }
            if (career.current) {
                currentCount++;
            }
        }
        if (currentCount > 1) {
            throw new Error('Only one position can be marked as current');
        }
        // Use transaction to replace all career records
        return await prisma_1.prisma.$transaction(async (tx) => {
            // Delete existing career records
            await tx.tutorCareer.deleteMany({
                where: { tutorId }
            });
            // Create new career records
            const results = [];
            for (const career of careers) {
                const created = await tx.tutorCareer.create({
                    data: {
                        ...career,
                        tutorId
                    }
                });
                results.push(created);
            }
            return results;
        });
    }
    /**
     * Get current position for a tutor
     */
    static async getCurrentPosition(tutorId) {
        return await prisma_1.prisma.tutorCareer.findFirst({
            where: {
                tutorId,
                current: true
            }
        });
    }
    /**
     * Get career records by company
     */
    static async getCareerByCompany(tutorId, company) {
        return await prisma_1.prisma.tutorCareer.findMany({
            where: {
                tutorId,
                company: {
                    contains: company,
                    mode: 'insensitive'
                }
            },
            orderBy: { startYear: 'desc' }
        });
    }
    /**
     * Get career records by title
     */
    static async getCareerByTitle(tutorId, title) {
        return await prisma_1.prisma.tutorCareer.findMany({
            where: {
                tutorId,
                title: {
                    contains: title,
                    mode: 'insensitive'
                }
            },
            orderBy: { startYear: 'desc' }
        });
    }
    /**
     * Calculate total years of experience
     */
    static async getTotalExperience(tutorId) {
        const careers = await this.getTutorCareer(tutorId);
        const currentYear = new Date().getFullYear();
        let totalYears = 0;
        for (const career of careers) {
            const endYear = career.current ? currentYear : (career.endYear || currentYear);
            const years = endYear - career.startYear;
            totalYears += Math.max(0, years);
        }
        return totalYears;
    }
    /**
     * Get career summary
     */
    static async getCareerSummary(tutorId) {
        const careers = await this.getTutorCareer(tutorId);
        const totalExperience = await this.getTotalExperience(tutorId);
        const currentPosition = await this.getCurrentPosition(tutorId);
        const companies = [...new Set(careers.map(career => career.company))];
        return {
            totalExperience,
            currentPosition,
            totalPositions: careers.length,
            companies
        };
    }
    /**
     * Validate career data
     */
    static validateCareerData(data) {
        const errors = [];
        const currentYear = new Date().getFullYear();
        if ('startYear' in data && data.startYear) {
            if (data.startYear < 1900 || data.startYear > currentYear) {
                errors.push('Start year must be between 1900 and ' + currentYear);
            }
        }
        if ('endYear' in data && data.endYear) {
            if (data.endYear > currentYear) {
                errors.push('End year cannot be in the future');
            }
            if ('startYear' in data && data.startYear && data.endYear < data.startYear) {
                errors.push('End year must be after start year');
            }
        }
        if ('current' in data && data.current && 'endYear' in data && data.endYear) {
            errors.push('Current position cannot have an end year');
        }
        if ('current' in data && data.current === false && !('endYear' in data && data.endYear)) {
            errors.push('Non-current position must have an end year');
        }
        if ('title' in data && data.title && data.title.trim().length < 2) {
            errors.push('Title must be at least 2 characters long');
        }
        if ('company' in data && data.company && data.company.trim().length < 2) {
            errors.push('Company must be at least 2 characters long');
        }
        return errors;
    }
}
exports.TutorCareerService = TutorCareerService;
