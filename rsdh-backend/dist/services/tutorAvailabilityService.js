"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TutorAvailabilityService = void 0;
const prisma_1 = require("../lib/prisma");
class TutorAvailabilityService {
    /**
     * Add availability slot for a tutor
     */
    static async addAvailability(data) {
        // Validate time format
        if (!this.isValidTimeFormat(data.startTime) || !this.isValidTimeFormat(data.endTime)) {
            throw new Error('Invalid time format. Use HH:MM format');
        }
        // Validate day of week
        if (data.dayOfWeek < 0 || data.dayOfWeek > 6) {
            throw new Error('Invalid day of week. Use 0-6 (Sunday-Saturday)');
        }
        // Validate start time is before end time
        if (data.startTime >= data.endTime) {
            throw new Error('Start time must be before end time');
        }
        // Check if tutor exists
        const tutor = await prisma_1.prisma.tutorProfile.findUnique({
            where: { id: data.tutorId }
        });
        if (!tutor) {
            throw new Error('Tutor profile not found');
        }
        // Check for overlapping availability on the same day
        const overlapping = await prisma_1.prisma.tutorAvailability.findFirst({
            where: {
                tutorId: data.tutorId,
                dayOfWeek: data.dayOfWeek,
                OR: [
                    {
                        AND: [
                            { startTime: { lte: data.startTime } },
                            { endTime: { gt: data.startTime } }
                        ]
                    },
                    {
                        AND: [
                            { startTime: { lt: data.endTime } },
                            { endTime: { gte: data.endTime } }
                        ]
                    },
                    {
                        AND: [
                            { startTime: { gte: data.startTime } },
                            { endTime: { lte: data.endTime } }
                        ]
                    }
                ]
            }
        });
        if (overlapping) {
            throw new Error('Time slot overlaps with existing availability');
        }
        return await prisma_1.prisma.tutorAvailability.create({
            data
        });
    }
    /**
     * Get all availability slots for a tutor
     */
    static async getTutorAvailability(tutorId) {
        return await prisma_1.prisma.tutorAvailability.findMany({
            where: { tutorId },
            orderBy: [
                { dayOfWeek: 'asc' },
                { startTime: 'asc' }
            ]
        });
    }
    /**
     * Update availability slot
     */
    static async updateAvailability(availabilityId, data) {
        const existing = await prisma_1.prisma.tutorAvailability.findUnique({
            where: { id: availabilityId }
        });
        if (!existing) {
            throw new Error('Availability slot not found');
        }
        // Validate time format if provided
        if (data.startTime && !this.isValidTimeFormat(data.startTime)) {
            throw new Error('Invalid start time format. Use HH:MM format');
        }
        if (data.endTime && !this.isValidTimeFormat(data.endTime)) {
            throw new Error('Invalid end time format. Use HH:MM format');
        }
        // Validate day of week if provided
        if (data.dayOfWeek !== undefined && (data.dayOfWeek < 0 || data.dayOfWeek > 6)) {
            throw new Error('Invalid day of week. Use 0-6 (Sunday-Saturday)');
        }
        // Prepare updated data
        const updatedData = {
            dayOfWeek: data.dayOfWeek ?? existing.dayOfWeek,
            startTime: data.startTime ?? existing.startTime,
            endTime: data.endTime ?? existing.endTime
        };
        // Validate start time is before end time
        if (updatedData.startTime >= updatedData.endTime) {
            throw new Error('Start time must be before end time');
        }
        // Check for overlapping availability on the same day (excluding current slot)
        const overlapping = await prisma_1.prisma.tutorAvailability.findFirst({
            where: {
                tutorId: existing.tutorId,
                dayOfWeek: updatedData.dayOfWeek,
                id: { not: availabilityId },
                OR: [
                    {
                        AND: [
                            { startTime: { lte: updatedData.startTime } },
                            { endTime: { gt: updatedData.startTime } }
                        ]
                    },
                    {
                        AND: [
                            { startTime: { lt: updatedData.endTime } },
                            { endTime: { gte: updatedData.endTime } }
                        ]
                    },
                    {
                        AND: [
                            { startTime: { gte: updatedData.startTime } },
                            { endTime: { lte: updatedData.endTime } }
                        ]
                    }
                ]
            }
        });
        if (overlapping) {
            throw new Error('Time slot overlaps with existing availability');
        }
        return await prisma_1.prisma.tutorAvailability.update({
            where: { id: availabilityId },
            data: updatedData
        });
    }
    /**
     * Delete availability slot
     */
    static async deleteAvailability(availabilityId) {
        const existing = await prisma_1.prisma.tutorAvailability.findUnique({
            where: { id: availabilityId }
        });
        if (!existing) {
            throw new Error('Availability slot not found');
        }
        await prisma_1.prisma.tutorAvailability.delete({
            where: { id: availabilityId }
        });
    }
    /**
     * Get availability for a specific day
     */
    static async getAvailabilityByDay(tutorId, dayOfWeek) {
        if (dayOfWeek < 0 || dayOfWeek > 6) {
            throw new Error('Invalid day of week. Use 0-6 (Sunday-Saturday)');
        }
        return await prisma_1.prisma.tutorAvailability.findMany({
            where: {
                tutorId,
                dayOfWeek
            },
            orderBy: { startTime: 'asc' }
        });
    }
    /**
     * Bulk update availability for a tutor
     */
    static async bulkUpdateAvailability(tutorId, availabilities) {
        // Validate tutor exists
        const tutor = await prisma_1.prisma.tutorProfile.findUnique({
            where: { id: tutorId }
        });
        if (!tutor) {
            throw new Error('Tutor profile not found');
        }
        // Validate all availability data
        for (const availability of availabilities) {
            if (!this.isValidTimeFormat(availability.startTime) || !this.isValidTimeFormat(availability.endTime)) {
                throw new Error('Invalid time format. Use HH:MM format');
            }
            if (availability.dayOfWeek < 0 || availability.dayOfWeek > 6) {
                throw new Error('Invalid day of week. Use 0-6 (Sunday-Saturday)');
            }
            if (availability.startTime >= availability.endTime) {
                throw new Error('Start time must be before end time');
            }
        }
        // Check for overlaps within the new data
        for (let i = 0; i < availabilities.length; i++) {
            for (let j = i + 1; j < availabilities.length; j++) {
                const a = availabilities[i];
                const b = availabilities[j];
                if (a.dayOfWeek === b.dayOfWeek) {
                    if ((a.startTime <= b.startTime && a.endTime > b.startTime) ||
                        (a.startTime < b.endTime && a.endTime >= b.endTime) ||
                        (a.startTime >= b.startTime && a.endTime <= b.endTime)) {
                        throw new Error('Time slots overlap within the provided data');
                    }
                }
            }
        }
        // Use transaction to replace all availability
        return await prisma_1.prisma.$transaction(async (tx) => {
            // Delete existing availability
            await tx.tutorAvailability.deleteMany({
                where: { tutorId }
            });
            // Create new availability slots
            const results = [];
            for (const availability of availabilities) {
                const created = await tx.tutorAvailability.create({
                    data: {
                        ...availability,
                        tutorId
                    }
                });
                results.push(created);
            }
            return results;
        });
    }
    /**
     * Validate time format (HH:MM)
     */
    static isValidTimeFormat(time) {
        const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
        return timeRegex.test(time);
    }
    /**
     * Get formatted availability for display
     */
    static async getFormattedAvailability(tutorId) {
        const availability = await this.getTutorAvailability(tutorId);
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const formatted = {};
        for (const slot of availability) {
            const dayName = dayNames[slot.dayOfWeek];
            if (!formatted[dayName]) {
                formatted[dayName] = [];
            }
            formatted[dayName].push({
                id: slot.id,
                startTime: slot.startTime,
                endTime: slot.endTime
            });
        }
        return formatted;
    }
}
exports.TutorAvailabilityService = TutorAvailabilityService;
