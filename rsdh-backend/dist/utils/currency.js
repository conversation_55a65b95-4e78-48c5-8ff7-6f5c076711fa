"use strict";
/**
 * Currency utility functions for handling monetary amounts
 * Uses BigInt to store amounts in fen (1/100 CNY) to avoid floating point precision issues
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.yuanToFen = yuanToFen;
exports.fenToYuan = fenToYuan;
exports.formatFenAsYuan = formatFenAsYuan;
exports.formatFenAsYuanDisplay = formatFenAsYuanDisplay;
exports.parseYuanToFen = parseYuanToFen;
exports.addFen = addFen;
exports.subtractFen = subtractFen;
exports.multiplyFenByPercentage = multiplyFenByPercentage;
exports.calculatePercentageSplit = calculatePercentageSplit;
exports.validateNonNegativeFen = validateNonNegativeFen;
exports.legacyFloatToFen = legacyFloatToFen;
exports.formatFenWithCurrency = formatFenWithCurrency;
/**
 * Convert yuan (元) to fen (分) - multiply by 100
 * @param yuan Amount in yuan (can be number or string)
 * @returns Amount in fen as BigInt
 */
function yuanToFen(yuan) {
    if (typeof yuan === 'string') {
        yuan = parseFloat(yuan);
    }
    if (isNaN(yuan)) {
        throw new Error('Invalid yuan amount');
    }
    // Round to 2 decimal places to avoid precision issues
    const rounded = Math.round(yuan * 100);
    return BigInt(rounded);
}
/**
 * Convert fen (分) to yuan (元) - divide by 100
 * @param fen Amount in fen as BigInt
 * @returns Amount in yuan as number
 */
function fenToYuan(fen) {
    return Number(fen) / 100;
}
/**
 * Format fen amount as yuan string with 2 decimal places
 * @param fen Amount in fen as BigInt
 * @returns Formatted yuan string (e.g., "12.34")
 */
function formatFenAsYuan(fen) {
    const yuan = fenToYuan(fen);
    return yuan.toFixed(2);
}
/**
 * Format fen amount as yuan string for display (removes trailing zeros)
 * @param fen Amount in fen as BigInt
 * @returns Formatted yuan string (e.g., "12.3" instead of "12.30")
 */
function formatFenAsYuanDisplay(fen) {
    const yuan = fenToYuan(fen);
    return yuan.toString();
}
/**
 * Parse yuan string to fen BigInt
 * @param yuanStr Yuan amount as string (e.g., "12.34")
 * @returns Amount in fen as BigInt
 */
function parseYuanToFen(yuanStr) {
    const yuan = parseFloat(yuanStr);
    if (isNaN(yuan)) {
        throw new Error('Invalid yuan string');
    }
    return yuanToFen(yuan);
}
/**
 * Add two fen amounts
 * @param fen1 First amount in fen
 * @param fen2 Second amount in fen
 * @returns Sum in fen
 */
function addFen(fen1, fen2) {
    return fen1 + fen2;
}
/**
 * Subtract two fen amounts
 * @param fen1 First amount in fen
 * @param fen2 Second amount in fen
 * @returns Difference in fen
 */
function subtractFen(fen1, fen2) {
    return fen1 - fen2;
}
/**
 * Multiply fen amount by a percentage
 * @param fen Amount in fen
 * @param percentage Percentage as decimal (e.g., 0.7 for 70%)
 * @returns Result in fen
 */
function multiplyFenByPercentage(fen, percentage) {
    const result = Number(fen) * percentage;
    return BigInt(Math.round(result));
}
/**
 * Calculate percentage split of fen amount
 * @param totalFen Total amount in fen
 * @param percentage Percentage as decimal (e.g., 0.7 for 70%)
 * @returns Object with split amounts
 */
function calculatePercentageSplit(totalFen, percentage) {
    const primaryAmount = multiplyFenByPercentage(totalFen, percentage);
    const remainderAmount = totalFen - primaryAmount;
    return {
        primaryAmount,
        remainderAmount
    };
}
/**
 * Validate that a fen amount is non-negative
 * @param fen Amount in fen
 * @throws Error if amount is negative
 */
function validateNonNegativeFen(fen) {
    if (fen < 0n) {
        throw new Error('Amount cannot be negative');
    }
}
/**
 * Convert legacy float amounts to fen (for migration purposes)
 * @param floatAmount Legacy float amount
 * @returns Amount in fen as BigInt
 */
function legacyFloatToFen(floatAmount) {
    if (floatAmount === null || floatAmount === undefined) {
        return 0n;
    }
    return yuanToFen(floatAmount);
}
/**
 * Convert fen to display format with currency symbol
 * @param fen Amount in fen
 * @param currency Currency code (default: CNY)
 * @returns Formatted string with currency symbol
 */
function formatFenWithCurrency(fen, currency = 'CNY') {
    const yuan = formatFenAsYuanDisplay(fen);
    switch (currency) {
        case 'CNY':
            return `¥${yuan}`;
        case 'USD':
            return `$${yuan}`;
        case 'EUR':
            return `€${yuan}`;
        default:
            return `${yuan} ${currency}`;
    }
}
