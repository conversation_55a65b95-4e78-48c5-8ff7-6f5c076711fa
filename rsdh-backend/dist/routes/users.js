"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.userRoutes = userRoutes;
const userService_1 = require("../services/userService");
const auth_1 = require("../middleware/auth");
async function userRoutes(fastify) {
    // Get current user profile
    fastify.get('/profile', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Users'],
            summary: 'Get current user profile',
            description: 'Get the profile of the currently authenticated user',
            security: [{ bearerAuth: [] }],
            response: {
                200: {
                    description: 'User profile',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        email: { type: 'string' },
                        name: { type: 'string' },
                        avatar: { type: 'string' },
                        emailVerified: { type: 'boolean' },
                        disabled: { type: 'boolean' },
                        role: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' },
                    },
                },
                401: {
                    description: 'Unauthorized',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' },
                    },
                },
            },
        },
    }, async (request, reply) => {
        try {
            const user = await userService_1.UserService.getUserById(request.user.id);
            if (!user) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'User not found'
                });
            }
            reply.send({
                id: user.id,
                email: user.email,
                name: user.name,
                avatar: user.avatar,
                emailVerified: user.emailVerified,
                disabled: user.disabled,
                role: user.role,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
            });
        }
        catch (error) {
            request.log.error('Error getting user profile:', error);
            reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to get user profile'
            });
        }
    });
    // Update current user profile
    fastify.put('/profile', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Users'],
            summary: 'Update current user profile',
            description: 'Update the profile of the currently authenticated user',
            security: [{ bearerAuth: [] }],
            body: {
                type: 'object',
                properties: {
                    name: { type: 'string' },
                    avatar: { type: 'string', format: 'uri' },
                },
            },
            response: {
                200: {
                    description: 'Updated user profile',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        email: { type: 'string' },
                        name: { type: 'string' },
                        avatar: { type: 'string' },
                        emailVerified: { type: 'boolean' },
                        disabled: { type: 'boolean' },
                        role: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' },
                    },
                },
                401: {
                    description: 'Unauthorized',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' },
                    },
                },
            },
        },
    }, async (request, reply) => {
        try {
            const { name, avatar } = request.body;
            const updatedUser = await userService_1.UserService.updateUser(request.user.id, {
                name,
                avatar,
            });
            reply.send({
                id: updatedUser.id,
                email: updatedUser.email,
                name: updatedUser.name,
                avatar: updatedUser.avatar,
                emailVerified: updatedUser.emailVerified,
                disabled: updatedUser.disabled,
                role: updatedUser.role,
                createdAt: updatedUser.createdAt,
                updatedAt: updatedUser.updatedAt,
            });
        }
        catch (error) {
            request.log.error('Error updating user profile:', error);
            reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to update user profile'
            });
        }
    });
    // Get user by ID (admin only)
    fastify.get('/:id', {
        preHandler: [auth_1.authenticateUser, auth_1.requireAdmin],
        schema: {
            tags: ['Users'],
            summary: 'Get user by ID',
            description: 'Get a user by their ID (admin only)',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                properties: {
                    id: { type: 'string' },
                },
                required: ['id'],
            },
            response: {
                200: {
                    description: 'User details',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        email: { type: 'string' },
                        name: { type: 'string' },
                        avatar: { type: 'string' },
                        emailVerified: { type: 'boolean' },
                        disabled: { type: 'boolean' },
                        role: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' },
                    },
                },
                404: {
                    description: 'User not found',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' },
                    },
                },
            },
        },
    }, async (request, reply) => {
        try {
            const { id } = request.params;
            const user = await userService_1.UserService.getUserById(id);
            if (!user) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'User not found',
                });
            }
            reply.send({
                id: user.id,
                email: user.email,
                name: user.name,
                avatar: user.avatar,
                emailVerified: user.emailVerified,
                disabled: user.disabled,
                role: user.role,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
            });
        }
        catch (error) {
            request.log.error('Error getting user by ID:', error);
            reply.status(500).send({
                error: 'Internal Server Error',
                message: 'An error occurred while fetching the user',
            });
        }
    });
    // Get all users (admin only)
    fastify.get('/', {
        preHandler: [auth_1.authenticateUser, auth_1.requireAdmin],
        schema: {
            tags: ['Users'],
            summary: 'Get all users',
            description: 'Get a paginated list of all users (admin only)',
            security: [{ bearerAuth: [] }],
            querystring: {
                type: 'object',
                properties: {
                    page: { type: 'integer', minimum: 1, default: 1 },
                    limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
                },
            },
            response: {
                200: {
                    description: 'List of users',
                    type: 'object',
                    properties: {
                        users: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    id: { type: 'string' },
                                    email: { type: 'string' },
                                    name: { type: 'string' },
                                    avatar: { type: 'string' },
                                    emailVerified: { type: 'boolean' },
                                    disabled: { type: 'boolean' },
                                    role: { type: 'string' },
                                    createdAt: { type: 'string', format: 'date-time' },
                                    updatedAt: { type: 'string', format: 'date-time' },
                                },
                            },
                        },
                        total: { type: 'integer' },
                        page: { type: 'integer' },
                        limit: { type: 'integer' },
                    },
                },
            },
        },
    }, async (request, reply) => {
        try {
            const { page = 1, limit = 10 } = request.query;
            const result = await userService_1.UserService.getAllUsers(page, limit);
            reply.send({
                ...result,
                page,
                limit,
            });
        }
        catch (error) {
            request.log.error('Error getting all users:', error);
            reply.status(500).send({
                error: 'Internal Server Error',
                message: 'An error occurred while fetching users',
            });
        }
    });
    // Toggle user status (admin only)
    fastify.patch('/:id/status', {
        preHandler: [auth_1.authenticateUser, auth_1.requireAdmin],
        schema: {
            tags: ['Users'],
            summary: 'Toggle user status',
            description: 'Enable or disable a user account (admin only)',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                properties: {
                    id: { type: 'string' },
                },
                required: ['id'],
            },
            body: {
                type: 'object',
                properties: {
                    disabled: { type: 'boolean' },
                },
                required: ['disabled'],
            },
        },
    }, async (request, reply) => {
        try {
            const { id } = request.params;
            const { disabled } = request.body;
            const user = await userService_1.UserService.toggleUserStatus(id, disabled);
            reply.send(user);
        }
        catch (error) {
            request.log.error('Error toggling user status:', error);
            reply.status(500).send({
                error: 'Internal Server Error',
                message: 'An error occurred while updating user status',
            });
        }
    });
    // Update user role (admin only)
    fastify.patch('/:id/role', {
        preHandler: [auth_1.authenticateUser, auth_1.requireAdmin],
        schema: {
            tags: ['Users'],
            summary: 'Update user role',
            description: 'Update a user\'s role (admin only)',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                properties: {
                    id: { type: 'string' },
                },
                required: ['id'],
            },
            body: {
                type: 'object',
                properties: {
                    role: { type: 'string', enum: ['user', 'admin'] },
                },
                required: ['role'],
            },
        },
    }, async (request, reply) => {
        try {
            const { id } = request.params;
            const { role } = request.body;
            const user = await userService_1.UserService.updateUserRole(id, role);
            reply.send(user);
        }
        catch (error) {
            request.log.error('Error updating user role:', error);
            reply.status(500).send({
                error: 'Internal Server Error',
                message: 'An error occurred while updating user role',
            });
        }
    });
}
