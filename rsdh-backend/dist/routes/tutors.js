"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.tutorRoutes = tutorRoutes;
const tutorService_1 = require("../services/tutorService");
const auth_1 = require("../middleware/auth");
const currency_1 = require("../utils/currency");
async function tutorRoutes(fastify) {
    // Apply to become a tutor
    fastify.post('/apply', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutors'],
            summary: 'Apply to become a tutor',
            description: 'Submit an application to become a tutor with education and career information',
            security: [{ bearerAuth: [] }],
            body: {
                type: 'object',
                required: ['education', 'career'],
                properties: {
                    title: { type: 'string', description: 'Professional title' },
                    bio: { type: 'string', description: 'Biography/description' },
                    rate: { type: 'number', minimum: 0, description: 'Hourly rate' },
                    education: {
                        type: 'array',
                        minItems: 1,
                        items: {
                            type: 'object',
                            required: ['degree', 'fieldOfStudy', 'institution', 'startYear'],
                            properties: {
                                degree: { type: 'string' },
                                fieldOfStudy: { type: 'string' },
                                institution: { type: 'string' },
                                startYear: { type: 'integer', minimum: 1900, maximum: new Date().getFullYear() },
                                endYear: { type: 'integer', minimum: 1900, maximum: new Date().getFullYear() + 10 },
                                description: { type: 'string' },
                                is985: { type: 'boolean', description: 'Whether institution is a 985 university' },
                                is211: { type: 'boolean', description: 'Whether institution is a 211 university' }
                            }
                        }
                    },
                    career: {
                        type: 'array',
                        minItems: 1,
                        items: {
                            type: 'object',
                            required: ['title', 'company', 'startYear'],
                            properties: {
                                title: { type: 'string' },
                                company: { type: 'string' },
                                startYear: { type: 'integer', minimum: 1900, maximum: new Date().getFullYear() },
                                endYear: { type: 'integer', minimum: 1900, maximum: new Date().getFullYear() + 10 },
                                current: { type: 'boolean', default: false },
                                description: { type: 'string' }
                            }
                        }
                    }
                }
            },
            response: {
                201: {
                    description: 'Tutor application submitted successfully',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        userId: { type: 'string' },
                        title: { type: 'string' },
                        bio: { type: 'string' },
                        rate: { type: 'number' },
                        status: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' }
                    }
                },
                400: {
                    description: 'Bad Request',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const applicationData = request.body;
            const tutorProfile = await tutorService_1.TutorService.applyToBecomeTutor({
                ...applicationData,
                userId: request.user.id
            });
            return reply.status(201).send(tutorProfile);
        }
        catch (error) {
            request.log.error('Error applying to become tutor:', error);
            if (error instanceof Error) {
                if (error.message === 'User already has a tutor profile') {
                    return reply.status(400).send({
                        error: 'Bad Request',
                        message: error.message
                    });
                }
                if (error.message === 'User not found') {
                    return reply.status(404).send({
                        error: 'Not Found',
                        message: error.message
                    });
                }
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to submit tutor application'
            });
        }
    });
    // Get current user's tutor profile
    fastify.get('/profile', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutors'],
            summary: 'Get current user tutor profile',
            description: 'Get the tutor profile of the currently authenticated user',
            security: [{ bearerAuth: [] }],
            response: {
                200: {
                    description: 'Tutor profile',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        userId: { type: 'string' },
                        title: { type: 'string' },
                        bio: { type: 'string' },
                        rate: { type: 'number' },
                        status: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' },
                        user: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                email: { type: 'string' },
                                name: { type: 'string' },
                                avatar: { type: 'string' }
                            }
                        },
                        education: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    id: { type: 'string' },
                                    degree: { type: 'string' },
                                    fieldOfStudy: { type: 'string' },
                                    institution: { type: 'string' },
                                    startYear: { type: 'integer' },
                                    endYear: { type: 'integer' },
                                    description: { type: 'string' }
                                }
                            }
                        },
                        career: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    id: { type: 'string' },
                                    title: { type: 'string' },
                                    company: { type: 'string' },
                                    startYear: { type: 'integer' },
                                    endYear: { type: 'integer' },
                                    current: { type: 'boolean' },
                                    description: { type: 'string' }
                                }
                            }
                        },
                        averageRating: { type: 'number' },
                        _count: {
                            type: 'object',
                            properties: {
                                reviews: { type: 'integer' },
                                appointments: { type: 'integer' }
                            }
                        }
                    }
                },
                404: {
                    description: 'Tutor profile not found',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const tutorProfile = await tutorService_1.TutorService.getTutorByUserId(request.user.id);
            if (!tutorProfile) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'Tutor profile not found'
                });
            }
            // Format prices for frontend
            const formattedProfile = {
                ...tutorProfile,
                rate: (0, currency_1.formatFenAsYuan)(tutorProfile.rate || 0n),
                hourlyRate: (0, currency_1.formatFenAsYuan)(tutorProfile.hourlyRate || 0n),
                halfHourRate: (0, currency_1.formatFenAsYuan)(tutorProfile.halfHourRate || 0n)
            };
            return reply.send(formattedProfile);
        }
        catch (error) {
            request.log.error('Error getting tutor profile:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to get tutor profile'
            });
        }
    });
    // Update tutor profile
    fastify.put('/profile', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutors'],
            summary: 'Update tutor profile',
            description: 'Update the tutor profile of the currently authenticated user',
            security: [{ bearerAuth: [] }],
            body: {
                type: 'object',
                properties: {
                    title: { type: 'string' },
                    bio: { type: 'string' },
                    rate: { type: 'number', minimum: 0 }
                }
            },
            response: {
                200: {
                    description: 'Tutor profile updated successfully',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        userId: { type: 'string' },
                        title: { type: 'string' },
                        bio: { type: 'string' },
                        rate: { type: 'number' },
                        status: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' }
                    }
                },
                404: {
                    description: 'Tutor profile not found',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            // First get the tutor profile to get the tutor ID
            const existingProfile = await tutorService_1.TutorService.getTutorByUserId(request.user.id);
            if (!existingProfile) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'Tutor profile not found'
                });
            }
            const updateData = request.body;
            const updatedProfile = await tutorService_1.TutorService.updateTutorProfile(existingProfile.id, updateData);
            return reply.send(updatedProfile);
        }
        catch (error) {
            request.log.error('Error updating tutor profile:', error);
            if (error instanceof Error && error.message === 'Tutor profile not found') {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: error.message
                });
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to update tutor profile'
            });
        }
    });
    // Get tutor by ID (public endpoint)
    fastify.get('/:tutorId', {
        schema: {
            tags: ['Tutors'],
            summary: 'Get tutor by ID',
            description: 'Get detailed information about a specific tutor',
            params: {
                type: 'object',
                required: ['tutorId'],
                properties: {
                    tutorId: { type: 'string', description: 'Tutor ID' }
                }
            },
            response: {
                200: {
                    description: 'Tutor details',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        userId: { type: 'string' },
                        title: { type: 'string' },
                        bio: { type: 'string' },
                        rate: { type: 'number' },
                        status: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' },
                        user: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                email: { type: 'string' },
                                name: { type: 'string' },
                                avatar: { type: 'string' }
                            }
                        },
                        education: { type: 'array' },
                        career: { type: 'array' },
                        availability: { type: 'array' },
                        averageRating: { type: 'number' },
                        _count: {
                            type: 'object',
                            properties: {
                                reviews: { type: 'integer' },
                                appointments: { type: 'integer' }
                            }
                        }
                    }
                },
                404: {
                    description: 'Tutor not found',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { tutorId } = request.params;
            const tutor = await tutorService_1.TutorService.getTutorById(tutorId);
            if (!tutor) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'Tutor not found'
                });
            }
            // Only show approved tutors to public
            if (tutor.status !== 'approved') {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'Tutor not found'
                });
            }
            return reply.send(tutor);
        }
        catch (error) {
            request.log.error('Error getting tutor:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to get tutor'
            });
        }
    });
    // Get all tutors (public endpoint with pagination)
    fastify.get('/', {
        schema: {
            tags: ['Tutors'],
            summary: 'Get all tutors',
            description: 'Get a paginated list of approved tutors',
            querystring: {
                type: 'object',
                properties: {
                    page: { type: 'integer', minimum: 1, default: 1 },
                    limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
                    search: { type: 'string', description: 'Search in title, bio, or name' },
                    // Advanced filters
                    institution: { type: 'string', description: 'Filter by institution name (fuzzy search)' },
                    graduationYearFrom: { type: 'integer', minimum: 1900, description: 'Filter by graduation year from' },
                    graduationYearTo: { type: 'integer', minimum: 1900, description: 'Filter by graduation year to' },
                    career: { type: 'string', description: 'Filter by career/job title or company (fuzzy search)' },
                    degree: { type: 'string', description: 'Filter by degree (fuzzy search)' },
                    is985: { type: 'boolean', description: 'Filter by 985 university graduates' },
                    is211: { type: 'boolean', description: 'Filter by 211 university graduates' },
                    ratingFrom: { type: 'number', minimum: 0, maximum: 5, description: 'Filter by minimum rating' },
                    ratingTo: { type: 'number', minimum: 0, maximum: 5, description: 'Filter by maximum rating' },
                    priceFrom: { type: 'number', minimum: 0, description: 'Filter by minimum hourly price (CNY)' },
                    priceTo: { type: 'number', minimum: 0, description: 'Filter by maximum hourly price (CNY)' }
                }
            },
            response: {
                200: {
                    description: 'List of tutors',
                    type: 'object',
                    properties: {
                        tutors: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    id: { type: 'string' },
                                    userId: { type: 'string' },
                                    title: { type: 'string' },
                                    bio: { type: 'string' },
                                    rate: { type: 'number' },
                                    status: { type: 'string' },
                                    createdAt: { type: 'string', format: 'date-time' },
                                    updatedAt: { type: 'string', format: 'date-time' },
                                    user: {
                                        type: 'object',
                                        properties: {
                                            id: { type: 'string' },
                                            name: { type: 'string' },
                                            avatar: { type: 'string' }
                                        }
                                    },
                                    averageRating: { type: 'number' },
                                    _count: {
                                        type: 'object',
                                        properties: {
                                            reviews: { type: 'integer' },
                                            appointments: { type: 'integer' }
                                        }
                                    }
                                }
                            }
                        },
                        total: { type: 'integer' },
                        page: { type: 'integer' },
                        limit: { type: 'integer' },
                        totalPages: { type: 'integer' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { page, limit, search, institution, graduationYearFrom, graduationYearTo, career, degree, is985, is211, ratingFrom, ratingTo, priceFrom, priceTo } = request.query;
            const result = await tutorService_1.TutorService.getAllTutors({
                page: page || 1,
                limit: limit || 10,
                status: 'approved', // Only show approved tutors to public
                search,
                institution,
                graduationYearFrom,
                graduationYearTo,
                career,
                degree,
                is985,
                is211,
                ratingFrom,
                ratingTo,
                priceFrom,
                priceTo
            });
            // Format prices for frontend
            const formattedResult = {
                ...result,
                tutors: result.tutors.map(tutor => ({
                    ...tutor,
                    rate: (0, currency_1.formatFenAsYuan)(tutor.rate || 0n),
                    hourlyRate: (0, currency_1.formatFenAsYuan)(tutor.hourlyRate || 0n),
                    halfHourRate: (0, currency_1.formatFenAsYuan)(tutor.halfHourRate || 0n)
                }))
            };
            return reply.send(formattedResult);
        }
        catch (error) {
            request.log.error('Error getting tutors:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to get tutors'
            });
        }
    });
    // Admin: Get all tutor applications
    fastify.get('/admin/applications', {
        preHandler: [auth_1.authenticateUser, auth_1.requireAdmin],
        schema: {
            tags: ['Tutors', 'Admin'],
            summary: 'Get all tutor applications (Admin)',
            description: 'Get a paginated list of all tutor applications for admin review',
            security: [{ bearerAuth: [] }],
            querystring: {
                type: 'object',
                properties: {
                    page: { type: 'integer', minimum: 1, default: 1 },
                    limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
                    status: { type: 'string', enum: ['pending', 'approved', 'rejected'] },
                    search: { type: 'string' },
                    // Advanced filters (same as public endpoint)
                    institution: { type: 'string', description: 'Filter by institution name (fuzzy search)' },
                    graduationYearFrom: { type: 'integer', minimum: 1900, description: 'Filter by graduation year from' },
                    graduationYearTo: { type: 'integer', minimum: 1900, description: 'Filter by graduation year to' },
                    career: { type: 'string', description: 'Filter by career/job title or company (fuzzy search)' },
                    degree: { type: 'string', description: 'Filter by degree (fuzzy search)' },
                    is985: { type: 'boolean', description: 'Filter by 985 university graduates' },
                    is211: { type: 'boolean', description: 'Filter by 211 university graduates' },
                    ratingFrom: { type: 'number', minimum: 0, maximum: 5, description: 'Filter by minimum rating' },
                    ratingTo: { type: 'number', minimum: 0, maximum: 5, description: 'Filter by maximum rating' },
                    priceFrom: { type: 'number', minimum: 0, description: 'Filter by minimum hourly price (CNY)' },
                    priceTo: { type: 'number', minimum: 0, description: 'Filter by maximum hourly price (CNY)' }
                }
            },
            response: {
                200: {
                    description: 'List of tutor applications',
                    type: 'object',
                    properties: {
                        tutors: { type: 'array' },
                        total: { type: 'integer' },
                        page: { type: 'integer' },
                        limit: { type: 'integer' },
                        totalPages: { type: 'integer' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { page, limit, status, search, institution, graduationYearFrom, graduationYearTo, career, degree, is985, is211, ratingFrom, ratingTo, priceFrom, priceTo } = request.query;
            const result = await tutorService_1.TutorService.getAllTutors({
                page: page || 1,
                limit: limit || 10,
                status,
                search,
                institution,
                graduationYearFrom,
                graduationYearTo,
                career,
                degree,
                is985,
                is211,
                ratingFrom,
                ratingTo,
                priceFrom,
                priceTo
            });
            return reply.send(result);
        }
        catch (error) {
            request.log.error('Error getting tutor applications:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to get tutor applications'
            });
        }
    });
    // Admin: Approve or reject tutor application
    fastify.patch('/admin/:tutorId/status', {
        preHandler: [auth_1.authenticateUser, auth_1.requireAdmin],
        schema: {
            tags: ['Tutors', 'Admin'],
            summary: 'Update tutor application status (Admin)',
            description: 'Approve or reject a tutor application',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                required: ['tutorId'],
                properties: {
                    tutorId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                required: ['status'],
                properties: {
                    status: { type: 'string', enum: ['approved', 'rejected'] }
                }
            },
            response: {
                200: {
                    description: 'Tutor status updated successfully',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        userId: { type: 'string' },
                        status: { type: 'string' },
                        updatedAt: { type: 'string', format: 'date-time' }
                    }
                },
                404: {
                    description: 'Tutor not found',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { tutorId } = request.params;
            const { status } = request.body;
            const updatedTutor = await tutorService_1.TutorService.updateTutorStatus(tutorId, status);
            return reply.send(updatedTutor);
        }
        catch (error) {
            request.log.error('Error updating tutor status:', error);
            if (error instanceof Error && error.message === 'Tutor profile not found') {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: error.message
                });
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to update tutor status'
            });
        }
    });
}
