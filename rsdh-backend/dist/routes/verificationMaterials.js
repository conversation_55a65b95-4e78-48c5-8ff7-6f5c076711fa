"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.verificationMaterialRoutes = verificationMaterialRoutes;
const auth_1 = require("../middleware/auth");
const verificationMaterialService_1 = require("../services/verificationMaterialService");
const prisma_1 = require("../lib/prisma");
async function verificationMaterialRoutes(fastify) {
    // Add education verification material
    fastify.post('/education', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Verification Materials'],
            summary: 'Add education verification material',
            description: 'Upload verification material for an education record',
            security: [{ bearerAuth: [] }],
            body: {
                type: 'object',
                required: ['educationId', 'fileId', 'materialType'],
                properties: {
                    educationId: { type: 'string', description: 'Education record ID' },
                    fileId: { type: 'string', description: 'Uploaded file ID' },
                    materialType: {
                        type: 'string',
                        enum: ['diploma', 'transcript', 'certificate', 'degree_certificate', 'other'],
                        description: 'Type of verification material'
                    },
                    description: { type: 'string', description: 'Optional description' }
                }
            },
            response: {
                201: {
                    description: 'Verification material added successfully',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        educationId: { type: 'string' },
                        fileId: { type: 'string' },
                        materialType: { type: 'string' },
                        description: { type: 'string' },
                        status: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' },
                        file: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                originalName: { type: 'string' },
                                mimeType: { type: 'string' },
                                size: { type: 'number' }
                            }
                        }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const data = request.body;
            const verification = await verificationMaterialService_1.VerificationMaterialService.addEducationVerification(data);
            return reply.status(201).send(verification);
        }
        catch (error) {
            request.log.error('Error adding education verification:', error);
            return reply.status(400).send({
                error: 'Bad Request',
                message: error instanceof Error ? error.message : 'Failed to add verification material'
            });
        }
    });
    // Add career verification material
    fastify.post('/career', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Verification Materials'],
            summary: 'Add career verification material',
            description: 'Upload verification material for a career record',
            security: [{ bearerAuth: [] }],
            body: {
                type: 'object',
                required: ['careerId', 'fileId', 'materialType'],
                properties: {
                    careerId: { type: 'string', description: 'Career record ID' },
                    fileId: { type: 'string', description: 'Uploaded file ID' },
                    materialType: {
                        type: 'string',
                        enum: ['work_certificate', 'business_card', 'contract', 'employment_letter', 'other'],
                        description: 'Type of verification material'
                    },
                    description: { type: 'string', description: 'Optional description' }
                }
            },
            response: {
                201: {
                    description: 'Verification material added successfully',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        careerId: { type: 'string' },
                        fileId: { type: 'string' },
                        materialType: { type: 'string' },
                        description: { type: 'string' },
                        status: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const data = request.body;
            const verification = await verificationMaterialService_1.VerificationMaterialService.addCareerVerification(data);
            return reply.status(201).send(verification);
        }
        catch (error) {
            request.log.error('Error adding career verification:', error);
            return reply.status(400).send({
                error: 'Bad Request',
                message: error instanceof Error ? error.message : 'Failed to add verification material'
            });
        }
    });
    // Get education verification materials
    fastify.get('/education/:educationId', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Verification Materials'],
            summary: 'Get education verification materials',
            description: 'Get all verification materials for an education record',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                required: ['educationId'],
                properties: {
                    educationId: { type: 'string' }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { educationId } = request.params;
            const verifications = await verificationMaterialService_1.VerificationMaterialService.getEducationVerifications(educationId);
            return reply.send(verifications);
        }
        catch (error) {
            request.log.error('Error getting education verifications:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to get verification materials'
            });
        }
    });
    // Get career verification materials
    fastify.get('/career/:careerId', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Verification Materials'],
            summary: 'Get career verification materials',
            description: 'Get all verification materials for a career record',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                required: ['careerId'],
                properties: {
                    careerId: { type: 'string' }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { careerId } = request.params;
            const verifications = await verificationMaterialService_1.VerificationMaterialService.getCareerVerifications(careerId);
            return reply.send(verifications);
        }
        catch (error) {
            request.log.error('Error getting career verifications:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to get verification materials'
            });
        }
    });
    // Get all verification materials for current tutor
    fastify.get('/tutor/my-verifications', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Verification Materials'],
            summary: 'Get my verification materials',
            description: 'Get all verification materials for the current tutor',
            security: [{ bearerAuth: [] }]
        }
    }, async (request, reply) => {
        try {
            // Get tutor profile
            const tutorProfile = await prisma_1.prisma.tutorProfile.findUnique({
                where: { userId: request.user.id }
            });
            if (!tutorProfile) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'Tutor profile not found'
                });
            }
            const verifications = await verificationMaterialService_1.VerificationMaterialService.getTutorVerifications(tutorProfile.id);
            return reply.send(verifications);
        }
        catch (error) {
            request.log.error('Error getting tutor verifications:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to get verification materials'
            });
        }
    });
    // Delete education verification material
    fastify.delete('/education/:verificationId', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Verification Materials'],
            summary: 'Delete education verification material',
            description: 'Delete an education verification material',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                required: ['verificationId'],
                properties: {
                    verificationId: { type: 'string' }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { verificationId } = request.params;
            await verificationMaterialService_1.VerificationMaterialService.deleteEducationVerification(verificationId, request.user.id);
            return reply.status(204).send();
        }
        catch (error) {
            request.log.error('Error deleting education verification:', error);
            if (error instanceof Error) {
                if (error.message === 'Education verification not found') {
                    return reply.status(404).send({
                        error: 'Not Found',
                        message: error.message
                    });
                }
                if (error.message === 'Unauthorized to delete this verification') {
                    return reply.status(403).send({
                        error: 'Forbidden',
                        message: error.message
                    });
                }
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to delete verification material'
            });
        }
    });
    // Delete career verification material
    fastify.delete('/career/:verificationId', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Verification Materials'],
            summary: 'Delete career verification material',
            description: 'Delete a career verification material',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                required: ['verificationId'],
                properties: {
                    verificationId: { type: 'string' }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { verificationId } = request.params;
            await verificationMaterialService_1.VerificationMaterialService.deleteCareerVerification(verificationId, request.user.id);
            return reply.status(204).send();
        }
        catch (error) {
            request.log.error('Error deleting career verification:', error);
            if (error instanceof Error) {
                if (error.message === 'Career verification not found') {
                    return reply.status(404).send({
                        error: 'Not Found',
                        message: error.message
                    });
                }
                if (error.message === 'Unauthorized to delete this verification') {
                    return reply.status(403).send({
                        error: 'Forbidden',
                        message: error.message
                    });
                }
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to delete verification material'
            });
        }
    });
    // Admin routes for verification review
    // Get pending verifications for admin review
    fastify.get('/admin/pending', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Admin - Verification Materials'],
            summary: 'Get pending verifications',
            description: 'Get all pending verification materials for admin review',
            security: [{ bearerAuth: [] }]
        }
    }, async (request, reply) => {
        try {
            // Check if user is admin
            if (request.user.role !== 'admin') {
                return reply.status(403).send({
                    error: 'Forbidden',
                    message: 'Admin access required'
                });
            }
            const verifications = await verificationMaterialService_1.VerificationMaterialService.getPendingVerifications();
            return reply.send(verifications);
        }
        catch (error) {
            request.log.error('Error getting pending verifications:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to get pending verifications'
            });
        }
    });
    // Update education verification status (admin only)
    fastify.put('/admin/education/:verificationId/status', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Admin - Verification Materials'],
            summary: 'Update education verification status',
            description: 'Approve or reject an education verification material',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                required: ['verificationId'],
                properties: {
                    verificationId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                required: ['status'],
                properties: {
                    status: {
                        type: 'string',
                        enum: ['approved', 'rejected'],
                        description: 'New verification status'
                    },
                    reviewNotes: { type: 'string', description: 'Optional review notes' }
                }
            }
        }
    }, async (request, reply) => {
        try {
            // Check if user is admin
            if (request.user.role !== 'admin') {
                return reply.status(403).send({
                    error: 'Forbidden',
                    message: 'Admin access required'
                });
            }
            const { verificationId } = request.params;
            const { status, reviewNotes } = request.body;
            const verification = await verificationMaterialService_1.VerificationMaterialService.updateEducationVerificationStatus(verificationId, {
                status,
                reviewNotes,
                reviewedById: request.user.id
            });
            return reply.send(verification);
        }
        catch (error) {
            request.log.error('Error updating education verification status:', error);
            if (error instanceof Error && error.message === 'Education verification not found') {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: error.message
                });
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to update verification status'
            });
        }
    });
    // Update career verification status (admin only)
    fastify.put('/admin/career/:verificationId/status', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Admin - Verification Materials'],
            summary: 'Update career verification status',
            description: 'Approve or reject a career verification material',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                required: ['verificationId'],
                properties: {
                    verificationId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                required: ['status'],
                properties: {
                    status: {
                        type: 'string',
                        enum: ['approved', 'rejected'],
                        description: 'New verification status'
                    },
                    reviewNotes: { type: 'string', description: 'Optional review notes' }
                }
            }
        }
    }, async (request, reply) => {
        try {
            // Check if user is admin
            if (request.user.role !== 'admin') {
                return reply.status(403).send({
                    error: 'Forbidden',
                    message: 'Admin access required'
                });
            }
            const { verificationId } = request.params;
            const { status, reviewNotes } = request.body;
            const verification = await verificationMaterialService_1.VerificationMaterialService.updateCareerVerificationStatus(verificationId, {
                status,
                reviewNotes,
                reviewedById: request.user.id
            });
            return reply.send(verification);
        }
        catch (error) {
            request.log.error('Error updating career verification status:', error);
            if (error instanceof Error && error.message === 'Career verification not found') {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: error.message
                });
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to update verification status'
            });
        }
    });
}
