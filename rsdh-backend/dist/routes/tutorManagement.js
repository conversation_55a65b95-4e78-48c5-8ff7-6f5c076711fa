"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.tutorManagementRoutes = tutorManagementRoutes;
const tutorAvailabilityService_1 = require("../services/tutorAvailabilityService");
const tutorEducationService_1 = require("../services/tutorEducationService");
const tutorCareerService_1 = require("../services/tutorCareerService");
const tutorService_1 = require("../services/tutorService");
const auth_1 = require("../middleware/auth");
async function tutorManagementRoutes(fastify) {
    // ===== AVAILABILITY MANAGEMENT =====
    // Get tutor availability
    fastify.get('/availability', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Get tutor availability',
            description: 'Get all availability slots for the current tutor',
            security: [{ bearerAuth: [] }],
            response: {
                200: {
                    description: 'Tutor availability slots',
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            tutorId: { type: 'string' },
                            dayOfWeek: { type: 'integer', minimum: 0, maximum: 6 },
                            startTime: { type: 'string', pattern: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$' },
                            endTime: { type: 'string', pattern: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$' },
                            createdAt: { type: 'string', format: 'date-time' },
                            updatedAt: { type: 'string', format: 'date-time' }
                        }
                    }
                },
                404: {
                    description: 'Tutor profile not found',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            // Get tutor profile first
            const tutorProfile = await tutorService_1.TutorService.getTutorByUserId(request.user.id);
            if (!tutorProfile) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'Tutor profile not found'
                });
            }
            const availability = await tutorAvailabilityService_1.TutorAvailabilityService.getTutorAvailability(tutorProfile.id);
            return reply.send(availability);
        }
        catch (error) {
            request.log.error('Error getting tutor availability:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to get tutor availability'
            });
        }
    });
    // Add availability slot
    fastify.post('/availability', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Add availability slot',
            description: 'Add a new availability slot for the current tutor',
            security: [{ bearerAuth: [] }],
            body: {
                type: 'object',
                required: ['dayOfWeek', 'startTime', 'endTime'],
                properties: {
                    dayOfWeek: { type: 'integer', minimum: 0, maximum: 6, description: '0=Sunday, 6=Saturday' },
                    startTime: { type: 'string', pattern: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$', description: 'Format: HH:MM' },
                    endTime: { type: 'string', pattern: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$', description: 'Format: HH:MM' }
                }
            },
            response: {
                201: {
                    description: 'Availability slot created successfully',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        tutorId: { type: 'string' },
                        dayOfWeek: { type: 'integer' },
                        startTime: { type: 'string' },
                        endTime: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' }
                    }
                },
                400: {
                    description: 'Bad Request',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            // Get tutor profile first
            const tutorProfile = await tutorService_1.TutorService.getTutorByUserId(request.user.id);
            if (!tutorProfile) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'Tutor profile not found'
                });
            }
            const availabilityData = request.body;
            const availability = await tutorAvailabilityService_1.TutorAvailabilityService.addAvailability({
                ...availabilityData,
                tutorId: tutorProfile.id
            });
            return reply.status(201).send(availability);
        }
        catch (error) {
            request.log.error('Error adding availability:', error);
            if (error instanceof Error) {
                if (error.message.includes('Invalid') || error.message.includes('overlaps')) {
                    return reply.status(400).send({
                        error: 'Bad Request',
                        message: error.message
                    });
                }
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to add availability slot'
            });
        }
    });
    // Update availability slot
    fastify.put('/availability/:availabilityId', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Update availability slot',
            description: 'Update an existing availability slot',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                required: ['availabilityId'],
                properties: {
                    availabilityId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                properties: {
                    dayOfWeek: { type: 'integer', minimum: 0, maximum: 6 },
                    startTime: { type: 'string', pattern: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$' },
                    endTime: { type: 'string', pattern: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$' }
                }
            },
            response: {
                200: {
                    description: 'Availability slot updated successfully',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        tutorId: { type: 'string' },
                        dayOfWeek: { type: 'integer' },
                        startTime: { type: 'string' },
                        endTime: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' }
                    }
                },
                404: {
                    description: 'Availability slot not found',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { availabilityId } = request.params;
            const updateData = request.body;
            const updatedAvailability = await tutorAvailabilityService_1.TutorAvailabilityService.updateAvailability(availabilityId, updateData);
            return reply.send(updatedAvailability);
        }
        catch (error) {
            request.log.error('Error updating availability:', error);
            if (error instanceof Error) {
                if (error.message === 'Availability slot not found') {
                    return reply.status(404).send({
                        error: 'Not Found',
                        message: error.message
                    });
                }
                if (error.message.includes('Invalid') || error.message.includes('overlaps')) {
                    return reply.status(400).send({
                        error: 'Bad Request',
                        message: error.message
                    });
                }
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to update availability slot'
            });
        }
    });
    // Delete availability slot
    fastify.delete('/availability/:availabilityId', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Delete availability slot',
            description: 'Delete an availability slot',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                required: ['availabilityId'],
                properties: {
                    availabilityId: { type: 'string' }
                }
            },
            response: {
                204: {
                    description: 'Availability slot deleted successfully'
                },
                404: {
                    description: 'Availability slot not found',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { availabilityId } = request.params;
            await tutorAvailabilityService_1.TutorAvailabilityService.deleteAvailability(availabilityId);
            return reply.status(204).send();
        }
        catch (error) {
            request.log.error('Error deleting availability:', error);
            if (error instanceof Error && error.message === 'Availability slot not found') {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: error.message
                });
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to delete availability slot'
            });
        }
    });
    // Bulk update availability
    fastify.put('/availability', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Bulk update availability',
            description: 'Replace all availability slots for the current tutor',
            security: [{ bearerAuth: [] }],
            body: {
                type: 'array',
                items: {
                    type: 'object',
                    required: ['dayOfWeek', 'startTime', 'endTime'],
                    properties: {
                        dayOfWeek: { type: 'integer', minimum: 0, maximum: 6 },
                        startTime: { type: 'string', pattern: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$' },
                        endTime: { type: 'string', pattern: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$' }
                    }
                }
            },
            response: {
                200: {
                    description: 'Availability updated successfully',
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            tutorId: { type: 'string' },
                            dayOfWeek: { type: 'integer' },
                            startTime: { type: 'string' },
                            endTime: { type: 'string' },
                            createdAt: { type: 'string', format: 'date-time' },
                            updatedAt: { type: 'string', format: 'date-time' }
                        }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            // Get tutor profile first
            const tutorProfile = await tutorService_1.TutorService.getTutorByUserId(request.user.id);
            if (!tutorProfile) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'Tutor profile not found'
                });
            }
            const availabilities = request.body;
            const updatedAvailabilities = await tutorAvailabilityService_1.TutorAvailabilityService.bulkUpdateAvailability(tutorProfile.id, availabilities.map(a => ({ ...a, tutorId: tutorProfile.id })));
            return reply.send(updatedAvailabilities);
        }
        catch (error) {
            request.log.error('Error bulk updating availability:', error);
            if (error instanceof Error && error.message.includes('Invalid')) {
                return reply.status(400).send({
                    error: 'Bad Request',
                    message: error.message
                });
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to update availability'
            });
        }
    });
    // Get formatted availability
    fastify.get('/availability/formatted', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Get formatted availability',
            description: 'Get availability slots grouped by day name',
            security: [{ bearerAuth: [] }],
            response: {
                200: {
                    description: 'Formatted availability by day',
                    type: 'object',
                    additionalProperties: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                startTime: { type: 'string' },
                                endTime: { type: 'string' }
                            }
                        }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            // Get tutor profile first
            const tutorProfile = await tutorService_1.TutorService.getTutorByUserId(request.user.id);
            if (!tutorProfile) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'Tutor profile not found'
                });
            }
            const formattedAvailability = await tutorAvailabilityService_1.TutorAvailabilityService.getFormattedAvailability(tutorProfile.id);
            return reply.send(formattedAvailability);
        }
        catch (error) {
            request.log.error('Error getting formatted availability:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to get formatted availability'
            });
        }
    });
    // ===== EDUCATION MANAGEMENT =====
    // Get tutor education
    fastify.get('/education', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Get tutor education',
            description: 'Get all education records for the current tutor',
            security: [{ bearerAuth: [] }],
            response: {
                200: {
                    description: 'Tutor education records',
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            tutorId: { type: 'string' },
                            degree: { type: 'string' },
                            fieldOfStudy: { type: 'string' },
                            institution: { type: 'string' },
                            startYear: { type: 'integer' },
                            endYear: { type: 'integer' },
                            description: { type: 'string' },
                            createdAt: { type: 'string', format: 'date-time' },
                            updatedAt: { type: 'string', format: 'date-time' }
                        }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            // Get tutor profile first
            const tutorProfile = await tutorService_1.TutorService.getTutorByUserId(request.user.id);
            if (!tutorProfile) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'Tutor profile not found'
                });
            }
            const education = await tutorEducationService_1.TutorEducationService.getTutorEducation(tutorProfile.id);
            return reply.send(education);
        }
        catch (error) {
            request.log.error('Error getting tutor education:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to get tutor education'
            });
        }
    });
    // Add education record
    fastify.post('/education', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Add education record',
            description: 'Add a new education record for the current tutor',
            security: [{ bearerAuth: [] }],
            body: {
                type: 'object',
                required: ['degree', 'fieldOfStudy', 'institution', 'startYear'],
                properties: {
                    degree: { type: 'string' },
                    fieldOfStudy: { type: 'string' },
                    institution: { type: 'string' },
                    startYear: { type: 'integer', minimum: 1900, maximum: new Date().getFullYear() + 10 },
                    endYear: { type: 'integer', minimum: 1900, maximum: new Date().getFullYear() + 10 },
                    description: { type: 'string' }
                }
            },
            response: {
                201: {
                    description: 'Education record created successfully',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        tutorId: { type: 'string' },
                        degree: { type: 'string' },
                        fieldOfStudy: { type: 'string' },
                        institution: { type: 'string' },
                        startYear: { type: 'integer' },
                        endYear: { type: 'integer' },
                        description: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            // Get tutor profile first
            const tutorProfile = await tutorService_1.TutorService.getTutorByUserId(request.user.id);
            if (!tutorProfile) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'Tutor profile not found'
                });
            }
            const educationData = request.body;
            const education = await tutorEducationService_1.TutorEducationService.addEducation({
                ...educationData,
                tutorId: tutorProfile.id
            });
            return reply.status(201).send(education);
        }
        catch (error) {
            request.log.error('Error adding education:', error);
            if (error instanceof Error && error.message.includes('Invalid')) {
                return reply.status(400).send({
                    error: 'Bad Request',
                    message: error.message
                });
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to add education record'
            });
        }
    });
    // Update education record
    fastify.put('/education/:educationId', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Update education record',
            description: 'Update an existing education record',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                required: ['educationId'],
                properties: {
                    educationId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                properties: {
                    degree: { type: 'string' },
                    fieldOfStudy: { type: 'string' },
                    institution: { type: 'string' },
                    startYear: { type: 'integer', minimum: 1900, maximum: new Date().getFullYear() + 10 },
                    endYear: { type: 'integer', minimum: 1900, maximum: new Date().getFullYear() + 10 },
                    description: { type: 'string' }
                }
            },
            response: {
                200: {
                    description: 'Education record updated successfully',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        tutorId: { type: 'string' },
                        degree: { type: 'string' },
                        fieldOfStudy: { type: 'string' },
                        institution: { type: 'string' },
                        startYear: { type: 'integer' },
                        endYear: { type: 'integer' },
                        description: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { educationId } = request.params;
            const updateData = request.body;
            const updatedEducation = await tutorEducationService_1.TutorEducationService.updateEducation(educationId, updateData);
            return reply.send(updatedEducation);
        }
        catch (error) {
            request.log.error('Error updating education:', error);
            if (error instanceof Error) {
                if (error.message === 'Education record not found') {
                    return reply.status(404).send({
                        error: 'Not Found',
                        message: error.message
                    });
                }
                if (error.message.includes('Invalid')) {
                    return reply.status(400).send({
                        error: 'Bad Request',
                        message: error.message
                    });
                }
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to update education record'
            });
        }
    });
    // Delete education record
    fastify.delete('/education/:educationId', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Delete education record',
            description: 'Delete an education record',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                required: ['educationId'],
                properties: {
                    educationId: { type: 'string' }
                }
            },
            response: {
                204: {
                    description: 'Education record deleted successfully'
                },
                404: {
                    description: 'Education record not found',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { educationId } = request.params;
            await tutorEducationService_1.TutorEducationService.deleteEducation(educationId);
            return reply.status(204).send();
        }
        catch (error) {
            request.log.error('Error deleting education:', error);
            if (error instanceof Error && error.message === 'Education record not found') {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: error.message
                });
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to delete education record'
            });
        }
    });
    // ===== CAREER MANAGEMENT =====
    // Get tutor career
    fastify.get('/career', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Get tutor career',
            description: 'Get all career records for the current tutor',
            security: [{ bearerAuth: [] }],
            response: {
                200: {
                    description: 'Tutor career records',
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            tutorId: { type: 'string' },
                            title: { type: 'string' },
                            company: { type: 'string' },
                            startYear: { type: 'integer' },
                            endYear: { type: 'integer' },
                            current: { type: 'boolean' },
                            description: { type: 'string' },
                            createdAt: { type: 'string', format: 'date-time' },
                            updatedAt: { type: 'string', format: 'date-time' }
                        }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            // Get tutor profile first
            const tutorProfile = await tutorService_1.TutorService.getTutorByUserId(request.user.id);
            if (!tutorProfile) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'Tutor profile not found'
                });
            }
            const career = await tutorCareerService_1.TutorCareerService.getTutorCareer(tutorProfile.id);
            return reply.send(career);
        }
        catch (error) {
            request.log.error('Error getting tutor career:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to get tutor career'
            });
        }
    });
    // Add career record
    fastify.post('/career', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Add career record',
            description: 'Add a new career record for the current tutor',
            security: [{ bearerAuth: [] }],
            body: {
                type: 'object',
                required: ['title', 'company', 'startYear', 'current'],
                properties: {
                    title: { type: 'string' },
                    company: { type: 'string' },
                    startYear: { type: 'integer', minimum: 1900, maximum: new Date().getFullYear() },
                    endYear: { type: 'integer', minimum: 1900, maximum: new Date().getFullYear() },
                    current: { type: 'boolean' },
                    description: { type: 'string' }
                }
            },
            response: {
                201: {
                    description: 'Career record created successfully',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        tutorId: { type: 'string' },
                        title: { type: 'string' },
                        company: { type: 'string' },
                        startYear: { type: 'integer' },
                        endYear: { type: 'integer' },
                        current: { type: 'boolean' },
                        description: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            // Get tutor profile first
            const tutorProfile = await tutorService_1.TutorService.getTutorByUserId(request.user.id);
            if (!tutorProfile) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'Tutor profile not found'
                });
            }
            const careerData = request.body;
            const career = await tutorCareerService_1.TutorCareerService.addCareer({
                ...careerData,
                tutorId: tutorProfile.id
            });
            return reply.status(201).send(career);
        }
        catch (error) {
            request.log.error('Error adding career:', error);
            if (error instanceof Error && error.message.includes('Invalid')) {
                return reply.status(400).send({
                    error: 'Bad Request',
                    message: error.message
                });
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to add career record'
            });
        }
    });
    // Update career record
    fastify.put('/career/:careerId', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Update career record',
            description: 'Update an existing career record',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                required: ['careerId'],
                properties: {
                    careerId: { type: 'string' }
                }
            },
            body: {
                type: 'object',
                properties: {
                    title: { type: 'string' },
                    company: { type: 'string' },
                    startYear: { type: 'integer', minimum: 1900, maximum: new Date().getFullYear() },
                    endYear: { type: 'integer', minimum: 1900, maximum: new Date().getFullYear() },
                    current: { type: 'boolean' },
                    description: { type: 'string' }
                }
            },
            response: {
                200: {
                    description: 'Career record updated successfully',
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        tutorId: { type: 'string' },
                        title: { type: 'string' },
                        company: { type: 'string' },
                        startYear: { type: 'integer' },
                        endYear: { type: 'integer' },
                        current: { type: 'boolean' },
                        description: { type: 'string' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { careerId } = request.params;
            const updateData = request.body;
            const updatedCareer = await tutorCareerService_1.TutorCareerService.updateCareer(careerId, updateData);
            return reply.send(updatedCareer);
        }
        catch (error) {
            request.log.error('Error updating career:', error);
            if (error instanceof Error) {
                if (error.message === 'Career record not found') {
                    return reply.status(404).send({
                        error: 'Not Found',
                        message: error.message
                    });
                }
                if (error.message.includes('Invalid')) {
                    return reply.status(400).send({
                        error: 'Bad Request',
                        message: error.message
                    });
                }
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to update career record'
            });
        }
    });
    // Delete career record
    fastify.delete('/career/:careerId', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Delete career record',
            description: 'Delete a career record',
            security: [{ bearerAuth: [] }],
            params: {
                type: 'object',
                required: ['careerId'],
                properties: {
                    careerId: { type: 'string' }
                }
            },
            response: {
                204: {
                    description: 'Career record deleted successfully'
                },
                404: {
                    description: 'Career record not found',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { careerId } = request.params;
            await tutorCareerService_1.TutorCareerService.deleteCareer(careerId);
            return reply.status(204).send();
        }
        catch (error) {
            request.log.error('Error deleting career:', error);
            if (error instanceof Error && error.message === 'Career record not found') {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: error.message
                });
            }
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to delete career record'
            });
        }
    });
    // Get career summary
    fastify.get('/career/summary', {
        preHandler: auth_1.authenticateUser,
        schema: {
            tags: ['Tutor Management'],
            summary: 'Get career summary',
            description: 'Get career summary including total experience and current position',
            security: [{ bearerAuth: [] }],
            response: {
                200: {
                    description: 'Career summary',
                    type: 'object',
                    properties: {
                        totalExperience: { type: 'integer', description: 'Total years of experience' },
                        currentPosition: {
                            type: 'object',
                            nullable: true,
                            properties: {
                                id: { type: 'string' },
                                title: { type: 'string' },
                                company: { type: 'string' },
                                startYear: { type: 'integer' },
                                current: { type: 'boolean' },
                                description: { type: 'string' }
                            }
                        },
                        totalPositions: { type: 'integer' },
                        companies: {
                            type: 'array',
                            items: { type: 'string' }
                        }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            // Get tutor profile first
            const tutorProfile = await tutorService_1.TutorService.getTutorByUserId(request.user.id);
            if (!tutorProfile) {
                return reply.status(404).send({
                    error: 'Not Found',
                    message: 'Tutor profile not found'
                });
            }
            const summary = await tutorCareerService_1.TutorCareerService.getCareerSummary(tutorProfile.id);
            return reply.send(summary);
        }
        catch (error) {
            request.log.error('Error getting career summary:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to get career summary'
            });
        }
    });
}
