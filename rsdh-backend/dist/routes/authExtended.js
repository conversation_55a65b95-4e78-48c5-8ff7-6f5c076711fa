"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authExtendedRoutes = authExtendedRoutes;
const userService_1 = require("../services/userService");
const smsService_1 = require("../services/smsService");
const auth_1 = require("../middleware/auth");
const bcrypt_1 = __importDefault(require("bcrypt"));
async function authExtendedRoutes(fastify) {
    // Send phone verification code
    fastify.post('/phone/send-code', {
        schema: {
            tags: ['Authentication Extended'],
            summary: 'Send phone verification code',
            description: 'Send SMS verification code for phone number authentication',
            body: {
                type: 'object',
                required: ['phoneNumber', 'type'],
                properties: {
                    phoneNumber: { type: 'string' },
                    type: { type: 'string', enum: ['login', 'register', 'bind'] },
                },
            },
            response: {
                200: {
                    description: 'Code sent successfully',
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        success: { type: 'boolean' },
                    },
                },
                400: {
                    description: 'Invalid phone number or request',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' },
                    },
                },
            },
        },
    }, async (request, reply) => {
        try {
            const { phoneNumber, type } = request.body;
            // Validate phone number
            if (!smsService_1.SMSService.validatePhoneNumber(phoneNumber)) {
                return reply.status(400).send({
                    error: 'Invalid Phone Number',
                    message: 'Please provide a valid phone number',
                });
            }
            const normalizedPhone = smsService_1.SMSService.normalizePhoneNumber(phoneNumber);
            // Check if phone number exists for different types
            const existingUser = await userService_1.UserService.findByPhoneNumber(normalizedPhone);
            if (type === 'register' && existingUser) {
                return reply.status(400).send({
                    error: 'Phone Number Exists',
                    message: 'This phone number is already registered',
                });
            }
            if (type === 'login' && !existingUser) {
                return reply.status(400).send({
                    error: 'Phone Number Not Found',
                    message: 'This phone number is not registered',
                });
            }
            // Generate and send OTP using our SMS service
            try {
                const code = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit code
                const smsService = new smsService_1.SMSService(fastify);
                const success = await smsService.sendVerificationCode({
                    phoneNumber: normalizedPhone,
                    code,
                });
                if (!success) {
                    return reply.status(500).send({
                        error: 'SMS Service Error',
                        message: 'Failed to send verification code',
                    });
                }
                // Store the code in memory/cache for verification (simplified for demo)
                // In production, you'd want to use Redis or a database
                const codeKey = `otp:${normalizedPhone}`;
                fastify.otpCodes = fastify.otpCodes || new Map();
                fastify.otpCodes.set(codeKey, {
                    code,
                    expiresAt: Date.now() + 5 * 60 * 1000, // 5 minutes
                    attempts: 0,
                });
                return {
                    message: 'Verification code sent successfully',
                    success: true,
                };
            }
            catch (error) {
                fastify.log.error('Failed to send OTP:', error);
                return reply.status(500).send({
                    error: 'SMS Service Error',
                    message: 'Failed to send verification code',
                });
            }
        }
        catch (error) {
            fastify.log.error('Phone code send error:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to send verification code',
            });
        }
    });
    // Phone number login (with password or code)
    fastify.post('/phone/login', {
        schema: {
            tags: ['Authentication Extended'],
            summary: 'Login with phone number',
            description: 'Login using phone number with either password or verification code',
            body: {
                type: 'object',
                required: ['phoneNumber', 'loginType'],
                properties: {
                    phoneNumber: { type: 'string' },
                    password: { type: 'string' },
                    code: { type: 'string' },
                    loginType: { type: 'string', enum: ['password', 'code'] },
                },
            },
            response: {
                200: {
                    description: 'Login successful',
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        token: { type: 'string' },
                        user: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                email: { type: 'string' },
                                name: { type: 'string' },
                                phoneNumber: { type: 'string' },
                                role: { type: 'string' },
                            },
                        },
                    },
                },
                401: {
                    description: 'Authentication failed',
                    type: 'object',
                    properties: {
                        error: { type: 'string' },
                        message: { type: 'string' },
                    },
                },
            },
        },
    }, async (request, reply) => {
        try {
            const { phoneNumber, password, code, loginType } = request.body;
            if (!smsService_1.SMSService.validatePhoneNumber(phoneNumber)) {
                return reply.status(400).send({
                    error: 'Invalid Phone Number',
                    message: 'Please provide a valid phone number',
                });
            }
            const normalizedPhone = smsService_1.SMSService.normalizePhoneNumber(phoneNumber);
            if (loginType === 'code') {
                if (!code) {
                    return reply.status(400).send({
                        error: 'Missing Code',
                        message: 'Verification code is required for code login',
                    });
                }
                // Verify OTP code
                try {
                    const codeKey = `otp:${normalizedPhone}`;
                    const otpCodes = fastify.otpCodes || new Map();
                    const storedOtp = otpCodes.get(codeKey);
                    if (!storedOtp) {
                        return reply.status(401).send({
                            error: 'Invalid Code',
                            message: 'No verification code found for this phone number',
                        });
                    }
                    if (Date.now() > storedOtp.expiresAt) {
                        otpCodes.delete(codeKey);
                        return reply.status(401).send({
                            error: 'Expired Code',
                            message: 'The verification code has expired',
                        });
                    }
                    if (storedOtp.attempts >= 3) {
                        otpCodes.delete(codeKey);
                        return reply.status(401).send({
                            error: 'Too Many Attempts',
                            message: 'Too many verification attempts. Please request a new code',
                        });
                    }
                    if (storedOtp.code !== code) {
                        storedOtp.attempts++;
                        return reply.status(401).send({
                            error: 'Invalid Code',
                            message: 'The verification code is incorrect',
                        });
                    }
                    // Code is valid, remove it and find/create user
                    otpCodes.delete(codeKey);
                    let user = await userService_1.UserService.findByPhoneNumber(normalizedPhone);
                    if (!user) {
                        // Create new user with phone number
                        user = await userService_1.UserService.createUserWithPhone({
                            email: `${normalizedPhone.replace(/[^\d]/g, '')}@phone.rsdh.local`,
                            password: '', // No password for phone-only users
                            name: normalizedPhone,
                            phoneNumber: normalizedPhone,
                        });
                    }
                    // Generate token (simplified for now)
                    const token = Buffer.from(user.email).toString('base64');
                    return {
                        message: 'Login successful',
                        token,
                        user: {
                            id: user.id,
                            email: user.email,
                            name: user.name,
                            phoneNumber: user.phoneNumber,
                            role: user.role,
                        },
                    };
                }
                catch (error) {
                    fastify.log.error('Code verification error:', error);
                    return reply.status(401).send({
                        error: 'Invalid Code',
                        message: 'The verification code is invalid or has expired',
                    });
                }
            }
            else if (loginType === 'password') {
                if (!password) {
                    return reply.status(400).send({
                        error: 'Missing Password',
                        message: 'Password is required for password login',
                    });
                }
                // Find user by phone number
                const user = await userService_1.UserService.findByPhoneNumberForAuth(normalizedPhone);
                if (!user || !user.password) {
                    return reply.status(401).send({
                        error: 'Invalid Credentials',
                        message: 'Invalid phone number or password',
                    });
                }
                // Verify password
                const isValidPassword = await bcrypt_1.default.compare(password, user.password);
                if (!isValidPassword) {
                    return reply.status(401).send({
                        error: 'Invalid Credentials',
                        message: 'Invalid phone number or password',
                    });
                }
                // Generate token (simplified for now)
                const token = Buffer.from(user.email).toString('base64');
                return {
                    message: 'Login successful',
                    token,
                    user: {
                        id: user.id,
                        email: user.email,
                        name: user.name,
                        phoneNumber: user.phoneNumber,
                        role: user.role,
                    },
                };
            }
            return reply.status(400).send({
                error: 'Invalid Login Type',
                message: 'Login type must be either "password" or "code"',
            });
        }
        catch (error) {
            fastify.log.error('Phone login error:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Login failed',
            });
        }
    });
    // Bind phone number to existing account
    fastify.post('/phone/bind', {
        preHandler: [auth_1.authenticateUser],
        schema: {
            tags: ['Authentication Extended'],
            summary: 'Bind phone number to account',
            description: 'Bind a phone number to the current user account',
            security: [{ bearerAuth: [] }],
            body: {
                type: 'object',
                required: ['phoneNumber', 'code'],
                properties: {
                    phoneNumber: { type: 'string' },
                    code: { type: 'string' },
                },
            },
            response: {
                200: {
                    description: 'Phone number bound successfully',
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        success: { type: 'boolean' },
                    },
                },
            },
        },
    }, async (request, reply) => {
        try {
            const { phoneNumber, code } = request.body;
            const userId = request.user?.id;
            if (!userId) {
                return reply.status(401).send({
                    error: 'Unauthorized',
                    message: 'User not authenticated',
                });
            }
            if (!smsService_1.SMSService.validatePhoneNumber(phoneNumber)) {
                return reply.status(400).send({
                    error: 'Invalid Phone Number',
                    message: 'Please provide a valid phone number',
                });
            }
            const normalizedPhone = smsService_1.SMSService.normalizePhoneNumber(phoneNumber);
            // Check if phone number is already bound to another account
            const existingUser = await userService_1.UserService.findByPhoneNumber(normalizedPhone);
            if (existingUser && existingUser.id !== userId) {
                return reply.status(400).send({
                    error: 'Phone Number Taken',
                    message: 'This phone number is already bound to another account',
                });
            }
            // Verify the code
            try {
                const codeKey = `otp:${normalizedPhone}`;
                const otpCodes = fastify.otpCodes || new Map();
                const storedOtp = otpCodes.get(codeKey);
                if (!storedOtp) {
                    return reply.status(400).send({
                        error: 'Invalid Code',
                        message: 'No verification code found for this phone number',
                    });
                }
                if (Date.now() > storedOtp.expiresAt) {
                    otpCodes.delete(codeKey);
                    return reply.status(400).send({
                        error: 'Expired Code',
                        message: 'The verification code has expired',
                    });
                }
                if (storedOtp.attempts >= 3) {
                    otpCodes.delete(codeKey);
                    return reply.status(400).send({
                        error: 'Too Many Attempts',
                        message: 'Too many verification attempts. Please request a new code',
                    });
                }
                if (storedOtp.code !== code) {
                    storedOtp.attempts++;
                    return reply.status(400).send({
                        error: 'Invalid Code',
                        message: 'The verification code is incorrect',
                    });
                }
                // Code is valid, remove it and update user's phone number
                otpCodes.delete(codeKey);
                await userService_1.UserService.updatePhoneNumber(userId, normalizedPhone, true);
                return {
                    message: 'Phone number bound successfully',
                    success: true,
                };
            }
            catch (error) {
                fastify.log.error('Phone bind error:', error);
                return reply.status(400).send({
                    error: 'Invalid Code',
                    message: 'The verification code is invalid or has expired',
                });
            }
        }
        catch (error) {
            fastify.log.error('Phone bind error:', error);
            return reply.status(500).send({
                error: 'Internal Server Error',
                message: 'Failed to bind phone number',
            });
        }
    });
}
