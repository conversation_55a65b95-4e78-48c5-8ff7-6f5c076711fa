"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerSwagger = registerSwagger;
const swagger_1 = __importDefault(require("@fastify/swagger"));
const swagger_ui_1 = __importDefault(require("@fastify/swagger-ui"));
async function registerSwagger(app) {
    // Note: better-auth doesn't provide generateOpenAPISchema method
    // We'll let Fastify auto-generate the documentation from route schemas
    try {
        // Register Swagger
        await app.register(swagger_1.default, {
            openapi: {
                openapi: '3.0.0',
                info: {
                    title: 'Life Navigation API',
                    description: 'API documentation for Life Navigation application with user management',
                    version: '1.0.0',
                },
                servers: [
                    { url: 'http://localhost:3001', description: 'Development' },
                ],
                components: {
                    securitySchemes: {
                        bearerAuth: {
                            type: 'http',
                            scheme: 'bearer',
                            bearerFormat: 'JWT',
                        },
                    },
                },
                paths: {
                // Paths will be auto-generated from route schemas
                },
                tags: [
                    { name: 'Authentication', description: 'Authentication endpoints' },
                    { name: 'Users', description: 'User management endpoints' },
                    { name: 'Tutors', description: 'Tutor management endpoints' },
                    { name: 'Tutor Management', description: 'Tutor profile management endpoints' },
                    { name: 'Appointments', description: 'Appointment booking and management endpoints' },
                    { name: 'Reviews', description: 'Review and rating endpoints' },
                    { name: 'Admin', description: 'Admin-only endpoints' },
                    { name: 'Health', description: 'Health check endpoints' }
                ]
            },
            hideUntagged: false // Show all routes, even those without tags
        });
        // Register Swagger UI
        await app.register(swagger_ui_1.default, {
            routePrefix: '/documentation',
            uiConfig: {
                docExpansion: 'list',
                deepLinking: false,
            },
            staticCSP: true,
            transformSpecification: (swaggerObject) => {
                // Ensure the base path is correct
                swaggerObject.servers = [{ url: '/' }];
                return swaggerObject;
            },
        });
        // Add a redirect from /docs to /documentation
        app.get('/docs', async (_request, reply) => {
            return reply.redirect('/documentation/');
        });
        app.log.info('Swagger documentation is available at /documentation');
    }
    catch (error) {
        app.log.error('Failed to initialize Swagger:', error);
        throw error;
    }
}
