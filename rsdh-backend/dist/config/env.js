"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fastify_plugin_1 = __importDefault(require("fastify-plugin"));
const env_1 = __importDefault(require("@fastify/env"));
const validator_1 = require("validator");
const validateEmail = (email, fieldName) => {
    if (email && !(0, validator_1.isEmail)(email)) {
        throw new Error(`${fieldName} must be a valid email address`);
    }
    return true;
};
const schema = {
    type: 'object',
    required: ['DATABASE_URL', 'JWT_SECRET', 'ADMIN_EMAIL', 'ADMIN_PASSWORD', 'EMAIL_HOST', 'EMAIL_USER', 'EMAIL_PASS'],
    properties: {
        DATABASE_URL: { type: 'string', default: 'postgresql://postgres:postgres@localhost:5432/rsdh_dev' },
        NODE_ENV: { type: 'string', default: 'development' },
        PORT: { type: 'number', default: 3001 },
        APP_NAME: { type: 'string', default: 'Life Navigation' },
        APP_URL: { type: 'string', default: 'http://localhost:3000' },
        CORS_ORIGIN: { type: 'string', default: 'http://localhost:3000' },
        TRUSTED_ORIGINS: { type: 'string', default: 'http://localhost:3000' },
        AUTH_URL: { type: 'string', default: 'http://localhost:3000' },
        JWT_SECRET: { type: 'string', default: 'your-secret-key-here' },
        // SMS Configuration
        SMS_PROVIDER: { type: 'string', default: 'mock' }, // aliyun, tencent, mock
        SMS_ACCESS_KEY_ID: { type: 'string' },
        SMS_ACCESS_KEY_SECRET: { type: 'string' },
        SMS_SIGN_NAME: { type: 'string', default: '人生导航' },
        SMS_TEMPLATE_CODE: { type: 'string', default: 'SMS_123456789' },
        // WeChat OAuth Configuration
        WECHAT_APP_ID: { type: 'string' },
        WECHAT_APP_SECRET: { type: 'string' },
        JWT_EXPIRES_IN: { type: 'string', default: '1d' },
        REFRESH_TOKEN_EXPIRES_IN: { type: 'string', default: '7d' },
        ADMIN_EMAIL: { type: 'string', default: '<EMAIL>' },
        ADMIN_PASSWORD: { type: 'string' },
        TEST_EMAIL: { type: 'string', default: '<EMAIL>' },
        TEST_PASSWORD: { type: 'string' },
        SUPPORT_EMAIL: { type: 'string', default: '<EMAIL>' },
        EMAIL_HOST: { type: 'string' },
        EMAIL_PORT: { type: 'number', default: 587 },
        EMAIL_USER: { type: 'string' },
        EMAIL_PASS: { type: 'string' },
        EMAIL_FROM: { type: 'string' },
    }
};
const envPlugin = (0, fastify_plugin_1.default)(async (fastify) => {
    await fastify.register(env_1.default, {
        confKey: 'config',
        schema: schema,
        dotenv: true
    });
    // Post-validation logic
    validateEmail(fastify.config.ADMIN_EMAIL, 'ADMIN_EMAIL');
    if (fastify.config.TEST_EMAIL) {
        validateEmail(fastify.config.TEST_EMAIL, 'TEST_EMAIL');
    }
    if (fastify.config.ADMIN_PASSWORD && fastify.config.ADMIN_PASSWORD.length < 8) {
        throw new Error('ADMIN_PASSWORD must be at least 8 characters long');
    }
});
exports.default = envPlugin;
