const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');
const FormData = require('form-data');

// 测试配置
const API_BASE_URL = 'http://127.0.0.1:3001';
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'Test1234';

// 创建测试图片文件
function createTestImage() {
  const testImagePath = path.join(__dirname, 'test-image.jpg');
  // Create a minimal JPEG file header (this is just for testing, not a real image)
  const jpegHeader = Buffer.from([
    0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
    0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xD9
  ]);
  fs.writeFileSync(testImagePath, jpegHeader);
  return testImagePath;
}

// 登录获取token
async function login() {
  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: TEST_EMAIL,
        password: TEST_PASSWORD,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Login failed: ${response.status} ${errorData}`);
    }

    const data = await response.json();
    console.log('✅ Login successful');
    return data.token || data.access_token;
  } catch (error) {
    console.error('❌ Login failed:', error.message);
    throw error;
  }
}

// 测试文件上传
async function testFileUpload(token) {
  try {
    const testFilePath = createTestImage();
    const form = new FormData();

    form.append('file', fs.createReadStream(testFilePath), {
      filename: 'test-image.jpg',
      contentType: 'image/jpeg',
    });
    form.append('category', 'general');
    form.append('isPublic', 'false');

    const response = await fetch(`${API_BASE_URL}/api/files/upload`, {
      method: 'POST',
      body: form,
      headers: {
        'Authorization': `Bearer ${token}`,
        ...form.getHeaders(),
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Upload failed: ${response.status} ${errorData}`);
    }

    const uploadResult = await response.json();
    console.log('✅ File upload successful:', {
      id: uploadResult.id,
      originalName: uploadResult.originalName,
      size: uploadResult.size,
      category: uploadResult.category,
    });

    // 清理测试文件
    fs.unlinkSync(testFilePath);

    return uploadResult;
  } catch (error) {
    console.error('❌ File upload failed:', error.message);
    throw error;
  }
}

// 测试获取文件信息
async function testGetFileInfo(token, fileId) {
  try {
    const response = await fetch(`${API_BASE_URL}/api/files/${fileId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Get file info failed: ${response.status} ${errorData}`);
    }

    const fileInfo = await response.json();
    console.log('✅ Get file info successful:', {
      id: fileInfo.id,
      originalName: fileInfo.originalName,
      url: fileInfo.url ? 'URL generated' : 'No URL',
    });

    return fileInfo;
  } catch (error) {
    console.error('❌ Get file info failed:', error.message);
    throw error;
  }
}

// 测试获取文件列表
async function testListFiles(token) {
  try {
    const response = await fetch(`${API_BASE_URL}/api/files?limit=5`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`List files failed: ${response.status} ${errorData}`);
    }

    const result = await response.json();
    console.log('✅ List files successful:', {
      totalFiles: result.pagination.total,
      filesReturned: result.files.length,
    });

    return result;
  } catch (error) {
    console.error('❌ List files failed:', error.message);
    throw error;
  }
}

// 测试存储使用统计
async function testStorageUsage(token) {
  try {
    const response = await fetch(`${API_BASE_URL}/api/files/usage/stats`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Get storage usage failed: ${response.status} ${errorData}`);
    }

    const usage = await response.json();
    console.log('✅ Get storage usage successful:', {
      totalFiles: usage.totalFiles,
      totalSize: usage.totalSizeFormatted,
    });

    return usage;
  } catch (error) {
    console.error('❌ Get storage usage failed:', error.message);
    throw error;
  }
}

// 测试删除文件
async function testDeleteFile(token, fileId) {
  try {
    const response = await fetch(`${API_BASE_URL}/api/files/${fileId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Delete file failed: ${response.status} ${errorData}`);
    }

    const result = await response.json();
    console.log('✅ Delete file successful:', result.message);

    return result;
  } catch (error) {
    console.error('❌ Delete file failed:', error.message);
    throw error;
  }
}

// 主测试函数
async function runTests() {
  console.log('🧪 Starting file upload API tests...\n');

  try {
    // 1. 登录
    console.log('1. Testing login...');
    const token = await login();
    console.log('');

    // 2. 上传文件
    console.log('2. Testing file upload...');
    const uploadResult = await testFileUpload(token);
    console.log('');

    // 3. 获取文件信息
    console.log('3. Testing get file info...');
    await testGetFileInfo(token, uploadResult.id);
    console.log('');

    // 4. 获取文件列表
    console.log('4. Testing list files...');
    await testListFiles(token);
    console.log('');

    // 5. 获取存储使用统计
    console.log('5. Testing storage usage...');
    await testStorageUsage(token);
    console.log('');

    // 6. 删除文件
    console.log('6. Testing delete file...');
    await testDeleteFile(token, uploadResult.id);
    console.log('');

    console.log('🎉 All tests passed successfully!');

  } catch (error) {
    console.error('💥 Test suite failed:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  login,
  testFileUpload,
  testGetFileInfo,
  testListFiles,
  testStorageUsage,
  testDeleteFile,
};
