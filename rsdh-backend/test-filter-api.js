const fetch = require('node-fetch');

const BASE_URL = 'http://127.0.0.1:3003';

async function login() {
  const response = await fetch(`${BASE_URL}/api/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email: 'eric<PERSON>@diff-lab.com',
      password: 'Test1234'
    })
  });

  if (!response.ok) {
    throw new Error(`Login failed: ${response.status}`);
  }

  const data = await response.json();
  return data.token;
}

async function createTestTutor(token) {
  console.log('Creating test tutor...');

  const tutorData = {
    title: 'Senior Software Engineer',
    bio: 'Experienced software engineer from top tech company',
    hourlyRate: 300,
    halfHourRate: 150,
    education: [
      {
        degree: 'Bachelor',
        fieldOfStudy: 'Computer Science',
        institution: 'Tsinghua University',
        startYear: 2015,
        endYear: 2019,
        is985: true,
        is211: true,
        description: 'Computer Science degree from top university'
      }
    ],
    career: [
      {
        title: 'Software Engineer',
        company: 'ByteDance',
        startYear: 2019,
        current: true,
        description: 'Working on recommendation algorithms'
      }
    ]
  };

  const response = await fetch(`${BASE_URL}/api/tutors/apply`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(tutorData)
  });

  if (!response.ok) {
    const error = await response.text();
    console.error('Failed to create tutor:', error);
    return null;
  }

  const tutor = await response.json();
  console.log('✅ Test tutor created:', tutor.id);
  return tutor;
}

async function approveTutor(token, tutorId) {
  console.log('Approving tutor...');

  const response = await fetch(`${BASE_URL}/api/tutors/admin/${tutorId}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      status: 'approved'
    })
  });

  if (!response.ok) {
    const error = await response.text();
    console.error('Failed to approve tutor:', error);
    return false;
  }

  console.log('✅ Tutor approved');
  return true;
}

async function testFilters(token) {
  console.log('\n🧪 Testing filter functionality...\n');

  // Test 1: Get all tutors
  console.log('1. Testing basic tutor listing...');
  let response = await fetch(`${BASE_URL}/api/tutors`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  let data = await response.json();
  console.log(`   Found ${data.tutors.length} tutors`);

  // Test 2: Filter by 985 university
  console.log('2. Testing 985 university filter...');
  response = await fetch(`${BASE_URL}/api/tutors?is985=true`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  data = await response.json();
  console.log(`   Found ${data.tutors.length} tutors from 985 universities`);

  // Test 3: Filter by institution
  console.log('3. Testing institution filter...');
  response = await fetch(`${BASE_URL}/api/tutors?institution=Tsinghua`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  data = await response.json();
  console.log(`   Found ${data.tutors.length} tutors from Tsinghua`);

  // Test 4: Filter by price range
  console.log('4. Testing price range filter...');
  response = await fetch(`${BASE_URL}/api/tutors?priceFrom=200&priceTo=400`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  data = await response.json();
  console.log(`   Found ${data.tutors.length} tutors in price range 200-400`);

  // Test 5: Filter by career
  console.log('5. Testing career filter...');
  response = await fetch(`${BASE_URL}/api/tutors?career=Software`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  data = await response.json();
  console.log(`   Found ${data.tutors.length} tutors with Software career`);

  // Test 6: Combined filters
  console.log('6. Testing combined filters...');
  response = await fetch(`${BASE_URL}/api/tutors?is985=true&priceFrom=200&career=Software`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  data = await response.json();
  console.log(`   Found ${data.tutors.length} tutors matching combined filters`);

  // Test 7: Filter that should return no results
  console.log('7. Testing filter with no results...');
  response = await fetch(`${BASE_URL}/api/tutors?priceFrom=1000`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  data = await response.json();
  console.log(`   Found ${data.tutors.length} tutors with price >= 1000 (should be 0)`);

  console.log('\n✅ All filter tests completed!');
}

async function main() {
  try {
    console.log('🚀 Starting filter API test...\n');

    // Login as admin to create and approve tutor
    const adminResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Test12345'
      })
    });

    if (!adminResponse.ok) {
      throw new Error('Admin login failed');
    }

    const adminData = await adminResponse.json();
    const adminToken = adminData.token;

    // Login as regular user
    const token = await login();
    console.log('✅ Logged in successfully');

    // Create test tutor
    const tutor = await createTestTutor(token);
    if (!tutor) {
      console.error('❌ Failed to create test tutor');
      return;
    }

    // Approve tutor as admin
    const approved = await approveTutor(adminToken, tutor.id);
    if (!approved) {
      console.error('❌ Failed to approve tutor');
      return;
    }

    // Test filters
    await testFilters(token);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

main();
