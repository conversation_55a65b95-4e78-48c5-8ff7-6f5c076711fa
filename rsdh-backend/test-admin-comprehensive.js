const fetch = require('node-fetch');

// Test configuration
const BASE_URL = 'http://localhost:3001';
const ADMIN_TOKEN = Buffer.from('<EMAIL>').toString('base64');

// Test results tracking
let testResults = {
    passed: 0,
    failed: 0,
    total: 0,
    details: []
};

// Helper function to make authenticated requests
async function makeRequest(endpoint, options = {}) {
    const url = `${BASE_URL}${endpoint}`;
    const defaultOptions = {
        headers: {
            'Authorization': `Bearer ${ADMIN_TOKEN}`,
            'Content-Type': 'application/json',
            ...options.headers
        }
    };
    
    return fetch(url, { ...defaultOptions, ...options });
}

// Helper function to log test results
function logTest(name, passed, details = '') {
    testResults.total++;
    if (passed) {
        testResults.passed++;
        console.log(`✅ ${name}`);
    } else {
        testResults.failed++;
        console.log(`❌ ${name}: ${details}`);
    }
    testResults.details.push({ name, passed, details });
}

// Test functions
async function testDashboardStats() {
    console.log('\n📊 测试仪表板统计API...');
    
    try {
        const response = await makeRequest('/api/admin/dashboard/stats');
        const data = await response.json();
        
        if (response.ok) {
            // Validate data structure
            const hasRequiredFields = data.users && data.tutors && data.appointments && data.payments;
            logTest('仪表板统计API响应', hasRequiredFields, hasRequiredFields ? '' : '缺少必需字段');
            
            // Validate data types
            const validTypes = typeof data.users.total === 'number' && 
                              typeof data.tutors.total === 'number' &&
                              typeof data.appointments.total === 'number' &&
                              typeof data.payments.totalRevenue === 'number';
            logTest('仪表板数据类型验证', validTypes, validTypes ? '' : '数据类型不正确');
            
            console.log(`   用户总数: ${data.users.total}`);
            console.log(`   导师总数: ${data.tutors.total}`);
            console.log(`   预约总数: ${data.appointments.total}`);
            console.log(`   总收入: ¥${data.payments.totalRevenue}`);
        } else {
            logTest('仪表板统计API响应', false, `HTTP ${response.status}`);
        }
    } catch (error) {
        logTest('仪表板统计API响应', false, error.message);
    }
}

async function testUserGrowthAPI() {
    console.log('\n📈 测试用户增长API...');
    
    try {
        const response = await makeRequest('/api/admin/dashboard/user-growth?months=6');
        const data = await response.json();
        
        if (response.ok) {
            const isArray = Array.isArray(data);
            logTest('用户增长数据格式', isArray, isArray ? '' : '返回数据不是数组');
            
            if (isArray && data.length > 0) {
                const hasValidStructure = data.every(item => item.month && typeof item.count === 'string');
                logTest('用户增长数据结构', hasValidStructure, hasValidStructure ? '' : '数据结构不正确');
                console.log(`   数据点数量: ${data.length}`);
            }
        } else {
            logTest('用户增长API响应', false, `HTTP ${response.status}`);
        }
    } catch (error) {
        logTest('用户增长API响应', false, error.message);
    }
}

async function testAppointmentTrends() {
    console.log('\n📅 测试预约趋势API...');
    
    try {
        const response = await makeRequest('/api/admin/dashboard/appointment-trends');
        const data = await response.json();
        
        if (response.ok) {
            const isArray = Array.isArray(data);
            logTest('预约趋势数据格式', isArray, isArray ? '' : '返回数据不是数组');
            
            if (isArray && data.length > 0) {
                const hasValidStructure = data.every(item => 
                    item.month && 
                    typeof item.total === 'string' && 
                    typeof item.completed === 'string'
                );
                logTest('预约趋势数据结构', hasValidStructure, hasValidStructure ? '' : '数据结构不正确');
                console.log(`   趋势数据点: ${data.length}`);
            }
        } else {
            logTest('预约趋势API响应', false, `HTTP ${response.status}`);
        }
    } catch (error) {
        logTest('预约趋势API响应', false, error.message);
    }
}

async function testLowRatingTutors() {
    console.log('\n⚠️ 测试低分导师API...');
    
    try {
        const response = await makeRequest('/api/admin/tutors/low-rating?threshold=3.0&limit=5');
        const data = await response.json();
        
        if (response.ok) {
            const hasValidStructure = data.tutors && data.pagination;
            logTest('低分导师API结构', hasValidStructure, hasValidStructure ? '' : '缺少必需字段');
            
            if (hasValidStructure) {
                const isValidArray = Array.isArray(data.tutors);
                logTest('低分导师数据格式', isValidArray, isValidArray ? '' : '导师数据不是数组');
                console.log(`   低分导师数量: ${data.tutors.length}`);
                console.log(`   分页信息: 第${data.pagination.page}页，共${data.pagination.total}条`);
            }
        } else {
            logTest('低分导师API响应', false, `HTTP ${response.status}`);
        }
    } catch (error) {
        logTest('低分导师API响应', false, error.message);
    }
}

async function testRevenueAnalytics() {
    console.log('\n💰 测试收入分析API...');
    
    try {
        const response = await makeRequest('/api/admin/analytics/revenue?period=month&months=6');
        const data = await response.json();
        
        if (response.ok) {
            const hasValidStructure = data.revenueTrends && data.paymentMethods && data.topTutors;
            logTest('收入分析API结构', hasValidStructure, hasValidStructure ? '' : '缺少必需字段');
            
            if (hasValidStructure) {
                const validArrays = Array.isArray(data.revenueTrends) && 
                                  Array.isArray(data.paymentMethods) && 
                                  Array.isArray(data.topTutors);
                logTest('收入分析数据格式', validArrays, validArrays ? '' : '数据格式不正确');
                
                console.log(`   收入趋势数据点: ${data.revenueTrends.length}`);
                console.log(`   支付方式数量: ${data.paymentMethods.length}`);
                console.log(`   顶级导师数量: ${data.topTutors.length}`);
            }
        } else {
            logTest('收入分析API响应', false, `HTTP ${response.status}`);
        }
    } catch (error) {
        logTest('收入分析API响应', false, error.message);
    }
}

async function testSystemHealth() {
    console.log('\n🏥 测试系统健康API...');
    
    try {
        const response = await makeRequest('/api/admin/system/health');
        const data = await response.json();
        
        if (response.ok) {
            const hasValidStructure = data.database && data.activity && data.pending;
            logTest('系统健康API结构', hasValidStructure, hasValidStructure ? '' : '缺少必需字段');
            
            if (hasValidStructure) {
                const hasUptime = typeof data.uptime === 'number';
                const hasMemory = data.memory && typeof data.memory.heapUsed === 'number';
                logTest('系统健康数据完整性', hasUptime && hasMemory, 
                       hasUptime && hasMemory ? '' : '缺少运行时间或内存信息');
                
                console.log(`   系统运行时间: ${Math.floor(data.uptime / 3600)}小时`);
                console.log(`   内存使用: ${Math.round(data.memory.heapUsed / 1024 / 1024)}MB`);
                console.log(`   待处理验证: ${data.pending.verifications}`);
            }
        } else {
            logTest('系统健康API响应', false, `HTTP ${response.status}`);
        }
    } catch (error) {
        logTest('系统健康API响应', false, error.message);
    }
}

async function testTutorStatusUpdate() {
    console.log('\n🔄 测试导师状态更新API...');
    
    try {
        // First, try to get a tutor to test with
        const tutorsResponse = await makeRequest('/api/admin/tutors/low-rating?limit=1');
        
        if (tutorsResponse.ok) {
            const tutorsData = await tutorsResponse.json();
            
            if (tutorsData.tutors && tutorsData.tutors.length > 0) {
                const tutorId = tutorsData.tutors[0].id;
                
                // Test status update
                const updateResponse = await makeRequest(`/api/admin/tutors/${tutorId}/status`, {
                    method: 'PATCH',
                    body: JSON.stringify({
                        status: 'approved',
                        reason: '测试状态更新'
                    })
                });
                
                if (updateResponse.ok) {
                    logTest('导师状态更新API', true);
                } else {
                    logTest('导师状态更新API', false, `HTTP ${updateResponse.status}`);
                }
            } else {
                logTest('导师状态更新API', true, '跳过 - 无可测试导师');
            }
        } else {
            logTest('导师状态更新API', false, '无法获取测试导师');
        }
    } catch (error) {
        logTest('导师状态更新API', false, error.message);
    }
}

async function testAPIDocumentation() {
    console.log('\n📚 测试API文档...');
    
    try {
        const response = await fetch(`${BASE_URL}/documentation`);
        
        if (response.ok) {
            logTest('API文档可访问性', true);
            
            // Test JSON schema
            const jsonResponse = await fetch(`${BASE_URL}/documentation/json`);
            if (jsonResponse.ok) {
                const schema = await jsonResponse.json();
                const hasSwaggerInfo = schema.info && schema.paths;
                logTest('API文档JSON格式', hasSwaggerInfo, hasSwaggerInfo ? '' : '缺少Swagger信息');
                
                if (hasSwaggerInfo) {
                    const adminPaths = Object.keys(schema.paths).filter(path => path.includes('/admin'));
                    console.log(`   管理员API端点数量: ${adminPaths.length}`);
                }
            } else {
                logTest('API文档JSON格式', false, 'JSON文档不可访问');
            }
        } else {
            logTest('API文档可访问性', false, `HTTP ${response.status}`);
        }
    } catch (error) {
        logTest('API文档可访问性', false, error.message);
    }
}

async function testAuthentication() {
    console.log('\n🔐 测试认证机制...');
    
    try {
        // Test without token
        const noAuthResponse = await fetch(`${BASE_URL}/api/admin/dashboard/stats`);
        const noAuthPassed = noAuthResponse.status === 401;
        logTest('无认证访问拒绝', noAuthPassed, noAuthPassed ? '' : '应该返回401状态码');
        
        // Test with invalid token
        const invalidAuthResponse = await fetch(`${BASE_URL}/api/admin/dashboard/stats`, {
            headers: {
                'Authorization': 'Bearer invalid-token',
                'Content-Type': 'application/json'
            }
        });
        const invalidAuthPassed = invalidAuthResponse.status === 401;
        logTest('无效认证拒绝', invalidAuthPassed, invalidAuthPassed ? '' : '应该返回401状态码');
        
        // Test with valid token
        const validAuthResponse = await makeRequest('/api/admin/dashboard/stats');
        const validAuthPassed = validAuthResponse.ok;
        logTest('有效认证通过', validAuthPassed, validAuthPassed ? '' : '有效认证应该通过');
        
    } catch (error) {
        logTest('认证机制测试', false, error.message);
    }
}

async function runAllTests() {
    console.log('🚀 开始运行管理员面板综合测试...\n');
    console.log('=' * 50);
    
    const startTime = Date.now();
    
    // Run all test functions
    await testAuthentication();
    await testDashboardStats();
    await testUserGrowthAPI();
    await testAppointmentTrends();
    await testLowRatingTutors();
    await testRevenueAnalytics();
    await testSystemHealth();
    await testTutorStatusUpdate();
    await testAPIDocumentation();
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    // Print summary
    console.log('\n' + '=' * 50);
    console.log('📋 测试总结');
    console.log('=' * 50);
    console.log(`总测试数: ${testResults.total}`);
    console.log(`通过: ${testResults.passed} ✅`);
    console.log(`失败: ${testResults.failed} ❌`);
    console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
    console.log(`测试时间: ${duration.toFixed(2)}秒`);
    
    if (testResults.failed > 0) {
        console.log('\n❌ 失败的测试:');
        testResults.details
            .filter(test => !test.passed)
            .forEach(test => console.log(`   - ${test.name}: ${test.details}`));
    }
    
    console.log('\n🎉 测试完成!');
    
    // Exit with appropriate code
    process.exit(testResults.failed > 0 ? 1 : 0);
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Run tests
runAllTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
});
