# 🛡️ 高考志愿填报平台 - 管理员面板

一个功能完整的管理员面板系统，为高考志愿填报平台提供全面的后台管理功能。

## ✨ 功能特性

### 📊 数据仪表板
- **实时统计**: 用户、导师、预约、支付数据概览
- **趋势分析**: 用户增长、预约趋势、收入分析
- **可视化图表**: 直观的数据展示和分析

### 👥 用户管理
- **用户列表**: 分页浏览所有注册用户
- **搜索筛选**: 按邮箱、姓名、角色、状态筛选
- **账户管理**: 启用/禁用用户账户
- **角色分配**: 设置用户角色权限

### 🎓 导师管理
- **导师审核**: 审核导师申请和认证材料
- **状态管理**: 管理导师账户状态
- **评分监控**: 监控导师评分和服务质量
- **预警系统**: 自动识别低分导师

### 📅 预约管理
- **预约统计**: 预约数量、完成率、取消率
- **状态跟踪**: 实时监控预约状态
- **数据分析**: 预约趋势和模式分析

### 💰 支付管理
- **交易记录**: 完整的支付交易历史
- **收入分析**: 收入趋势和支付方式分布
- **财务报表**: 详细的财务数据统计

### 🔍 系统监控
- **健康检查**: 系统状态和性能监控
- **错误追踪**: 系统异常和错误统计

## 🚀 快速开始

### 前置要求
- Node.js 16+ 
- npm 或 pnpm
- PostgreSQL 数据库

### 一键启动
```bash
# 克隆项目后，在项目根目录执行
./start-admin-panel.sh
```

### 手动启动

#### 1. 启动后端
```bash
cd rsdh-backend
npm install
npm run build
npm run dev
```

#### 2. 启动前端
```bash
cd rsdh-frontend
npm install
npm run dev
```

### 访问地址
- 🌐 **管理员面板**: http://localhost:3000/admin/dashboard
- 📚 **API文档**: http://localhost:3001/documentation
- 🧪 **测试页面**: 打开 `test-admin-panel.html`

## 👤 默认管理员账户

```
邮箱: <EMAIL>
角色: admin
```

## 📱 界面预览

### 仪表板
- 数据概览卡片
- 用户增长趋势图
- 预约统计图表
- 收入分析图表

### 管理页面
- 用户管理列表
- 导师审核界面
- 预约管理面板
- 支付统计报表

## 🔧 技术栈

### 后端
- **框架**: Fastify + TypeScript
- **数据库**: PostgreSQL + Prisma ORM
- **认证**: JWT + 角色权限
- **文档**: Swagger/OpenAPI

### 前端
- **框架**: React + TypeScript
- **UI**: Radix UI + Tailwind CSS
- **图表**: Recharts
- **路由**: React Router

## 📁 项目结构

```
├── rsdh-backend/           # 后端服务
│   ├── src/routes/admin.ts # 管理员API
│   └── src/middleware/     # 中间件
├── rsdh-frontend/          # 前端应用
│   ├── src/pages/Admin*    # 管理员页面
│   └── src/components/     # UI组件
├── test-admin-panel.html   # 测试页面
├── start-admin-panel.sh    # 启动脚本
└── stop-admin-panel.sh     # 停止脚本
```

## 🔐 安全特性

- JWT身份验证
- 角色权限控制
- API访问限制
- 输入验证和清理
- SQL注入防护

## 📊 API端点

### 仪表板
- `GET /api/admin/dashboard/stats` - 统计数据
- `GET /api/admin/dashboard/user-growth` - 用户增长
- `GET /api/admin/dashboard/appointment-trends` - 预约趋势

### 管理功能
- `GET /api/admin/tutors/low-rating` - 低分导师
- `PATCH /api/admin/tutors/:id/status` - 更新导师状态
- `GET /api/admin/analytics/revenue` - 收入分析
- `GET /api/admin/system/health` - 系统健康

## 🧪 测试

### API测试
```bash
# 运行后端测试
cd rsdh-backend
node test-admin-api.js
```

### 功能测试
1. 打开测试页面 `test-admin-panel.html`
2. 点击各个API测试按钮
3. 查看响应数据和状态

## 🛠️ 开发指南

### 添加新的管理功能
1. 在 `rsdh-backend/src/routes/admin.ts` 添加API端点
2. 在 `rsdh-frontend/src/pages/` 创建对应页面
3. 在 `AdminLayout.tsx` 添加导航链接
4. 在 `App.tsx` 配置路由

### 自定义样式
- 修改 Tailwind 配置
- 使用 Radix UI 组件
- 添加自定义 CSS

## 📈 性能优化

- 数据分页加载
- 图表懒加载
- API响应缓存
- 组件代码分割

## 🔄 停止服务

```bash
# 使用停止脚本
./stop-admin-panel.sh

# 或手动停止
kill $(cat backend.pid)
kill $(cat frontend.pid)
```

## 📝 日志

- **后端日志**: `backend.log`
- **前端日志**: `frontend.log`
- **数据库日志**: Prisma 查询日志

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 📞 支持

如有问题，请联系开发团队或查看项目文档。

---

**🎉 管理员面板已就绪，开始管理您的平台吧！**
