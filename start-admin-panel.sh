#!/bin/bash

# 管理员面板启动脚本
# 用于快速启动后端和前端服务

echo "🚀 启动管理员面板系统..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

echo "✅ Node.js 和 npm 已安装"

# 启动后端服务
echo "📡 启动后端服务..."
cd rsdh-backend

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装后端依赖..."
    npm install
fi

# 检查数据库连接
echo "🗄️ 检查数据库连接..."
npx prisma db push

# 构建项目
echo "🔨 构建后端项目..."
npm run build

# 启动后端服务（后台运行）
echo "🚀 启动后端服务 (端口 3001)..."
nohup npm run dev > ../backend.log 2>&1 &
BACKEND_PID=$!

# 等待后端启动
sleep 5

# 检查后端是否启动成功
if curl -s http://localhost:3001/api/admin/dashboard/stats > /dev/null; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败，请检查日志"
    cat ../backend.log
    exit 1
fi

# 返回根目录
cd ..

# 启动前端服务
echo "🎨 启动前端服务..."
cd rsdh-frontend

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 启动前端服务（后台运行）
echo "🚀 启动前端服务 (端口 3000)..."
nohup npm run dev > ../frontend.log 2>&1 &
FRONTEND_PID=$!

# 等待前端启动
sleep 10

# 返回根目录
cd ..

echo ""
echo "🎉 管理员面板系统启动完成！"
echo ""
echo "📊 访问地址："
echo "  - 管理员面板: http://localhost:3000/admin/dashboard"
echo "  - API文档: http://localhost:3001/documentation"
echo "  - 测试页面: file://$(pwd)/test-admin-panel.html"
echo ""
echo "👤 管理员账户："
echo "  - 邮箱: <EMAIL>"
echo "  - 角色: admin"
echo ""
echo "🔧 可用的管理功能："
echo "  - 用户管理: /admin/users"
echo "  - 导师管理: /admin/tutors"
echo "  - 预约管理: /admin/appointments"
echo "  - 支付管理: /admin/payments"
echo "  - 认证审核: /admin/verification"
echo ""
echo "📝 日志文件："
echo "  - 后端日志: backend.log"
echo "  - 前端日志: frontend.log"
echo ""
echo "🛑 停止服务："
echo "  - 后端PID: $BACKEND_PID"
echo "  - 前端PID: $FRONTEND_PID"
echo "  - 停止命令: kill $BACKEND_PID $FRONTEND_PID"
echo ""

# 保存PID到文件
echo $BACKEND_PID > backend.pid
echo $FRONTEND_PID > frontend.pid

echo "💡 提示: 使用 ./stop-admin-panel.sh 停止所有服务"
echo ""
echo "🌐 正在打开浏览器..."

# 尝试打开浏览器
if command -v xdg-open &> /dev/null; then
    xdg-open "file://$(pwd)/test-admin-panel.html"
elif command -v open &> /dev/null; then
    open "file://$(pwd)/test-admin-panel.html"
else
    echo "请手动打开浏览器访问: file://$(pwd)/test-admin-panel.html"
fi

echo "✨ 管理员面板已就绪！"
