version: '3.8'

services:
  web:
    build: .
    container_name: rsdh-frontend
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.rsdh-frontend.rule=Host(`your-domain.com`)"  # Replace with your domain
      - "traefik.http.routers.rsdh-frontend.entrypoints=websecure"
      - "traefik.http.routers.rsdh-frontend.tls.certresolver=myresolver"
      - "traefik.docker.network=traefik-public"
    networks:
      - traefik-public

networks:
  traefik-public:
    external: true
