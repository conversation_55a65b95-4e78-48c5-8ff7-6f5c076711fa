import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { X, RotateCcw } from "lucide-react";
import { TutorFilters } from "@/hooks/useTutors";

interface TutorAdvancedFiltersProps {
  filters: TutorFilters;
  onFiltersChange: (filters: TutorFilters) => void;
  onClose: () => void;
}

const TutorAdvancedFilters: React.FC<TutorAdvancedFiltersProps> = ({
  filters,
  onFiltersChange,
  onClose
}) => {
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 30 }, (_, i) => currentYear - i);
  
  const degrees = ['本科', '硕士', '博士', 'Bachelor', 'Master', 'PhD'];
  const careers = ['软件工程师', '教师', '医生', '律师', '金融分析师', '研究员', '产品经理', '设计师'];

  const updateFilter = (key: keyof TutorFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      searchText: filters.searchText, // Keep search text
      page: 1,
      limit: filters.limit
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.institution) count++;
    if (filters.graduationYearFrom || filters.graduationYearTo) count++;
    if (filters.career) count++;
    if (filters.degree) count++;
    if (filters.is985 !== undefined) count++;
    if (filters.is211 !== undefined) count++;
    if (filters.ratingFrom !== undefined || filters.ratingTo !== undefined) count++;
    if (filters.priceFrom !== undefined || filters.priceTo !== undefined) count++;
    return count;
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg font-semibold">高级筛选</CardTitle>
        <div className="flex items-center space-x-2">
          {getActiveFiltersCount() > 0 && (
            <Badge variant="secondary">
              {getActiveFiltersCount()} 个筛选条件
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-gray-500 hover:text-gray-700"
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            清除
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 院校筛选 */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">院校</Label>
          <div className="space-y-2">
            <Input
              placeholder="搜索院校名称"
              value={filters.institution || ''}
              onChange={(e) => updateFilter('institution', e.target.value || undefined)}
            />
            <div className="flex flex-wrap gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is985"
                  checked={filters.is985 === true}
                  onCheckedChange={(checked) => 
                    updateFilter('is985', checked ? true : undefined)
                  }
                />
                <Label htmlFor="is985" className="text-sm">985院校</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is211"
                  checked={filters.is211 === true}
                  onCheckedChange={(checked) => 
                    updateFilter('is211', checked ? true : undefined)
                  }
                />
                <Label htmlFor="is211" className="text-sm">211院校</Label>
              </div>
            </div>
          </div>
        </div>

        {/* 毕业年份 */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">毕业年份</Label>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label className="text-xs text-gray-500">从</Label>
              <Select
                value={filters.graduationYearFrom?.toString() || ''}
                onValueChange={(value) => 
                  updateFilter('graduationYearFrom', value ? parseInt(value) : undefined)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择年份" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">不限</SelectItem>
                  {years.map(year => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}年
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs text-gray-500">到</Label>
              <Select
                value={filters.graduationYearTo?.toString() || ''}
                onValueChange={(value) => 
                  updateFilter('graduationYearTo', value ? parseInt(value) : undefined)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择年份" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">不限</SelectItem>
                  {years.map(year => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}年
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 学历 */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">学历</Label>
          <Select
            value={filters.degree || ''}
            onValueChange={(value) => updateFilter('degree', value || undefined)}
          >
            <SelectTrigger>
              <SelectValue placeholder="选择学历" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">不限</SelectItem>
              {degrees.map(degree => (
                <SelectItem key={degree} value={degree}>
                  {degree}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 职业 */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">职业</Label>
          <Input
            placeholder="搜索职业或公司"
            value={filters.career || ''}
            onChange={(e) => updateFilter('career', e.target.value || undefined)}
          />
          <div className="flex flex-wrap gap-2">
            {careers.map(career => (
              <Button
                key={career}
                variant={filters.career === career ? "default" : "outline"}
                size="sm"
                onClick={() => 
                  updateFilter('career', filters.career === career ? undefined : career)
                }
              >
                {career}
              </Button>
            ))}
          </div>
        </div>

        {/* 价格范围 */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">价格范围 (元/小时)</Label>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label className="text-xs text-gray-500">最低价格</Label>
              <Input
                type="number"
                placeholder="0"
                value={filters.priceFrom || ''}
                onChange={(e) => 
                  updateFilter('priceFrom', e.target.value ? parseInt(e.target.value) : undefined)
                }
              />
            </div>
            <div>
              <Label className="text-xs text-gray-500">最高价格</Label>
              <Input
                type="number"
                placeholder="1000"
                value={filters.priceTo || ''}
                onChange={(e) => 
                  updateFilter('priceTo', e.target.value ? parseInt(e.target.value) : undefined)
                }
              />
            </div>
          </div>
        </div>

        {/* 评分范围 */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">评分范围</Label>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label className="text-xs text-gray-500">最低评分</Label>
              <Select
                value={filters.ratingFrom?.toString() || ''}
                onValueChange={(value) => 
                  updateFilter('ratingFrom', value ? parseFloat(value) : undefined)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择评分" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">不限</SelectItem>
                  <SelectItem value="1">1星及以上</SelectItem>
                  <SelectItem value="2">2星及以上</SelectItem>
                  <SelectItem value="3">3星及以上</SelectItem>
                  <SelectItem value="4">4星及以上</SelectItem>
                  <SelectItem value="4.5">4.5星及以上</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs text-gray-500">最高评分</Label>
              <Select
                value={filters.ratingTo?.toString() || ''}
                onValueChange={(value) => 
                  updateFilter('ratingTo', value ? parseFloat(value) : undefined)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择评分" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">不限</SelectItem>
                  <SelectItem value="2">2星及以下</SelectItem>
                  <SelectItem value="3">3星及以下</SelectItem>
                  <SelectItem value="4">4星及以下</SelectItem>
                  <SelectItem value="5">5星及以下</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 应用按钮 */}
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button onClick={onClose}>
            应用筛选
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default TutorAdvancedFilters;
