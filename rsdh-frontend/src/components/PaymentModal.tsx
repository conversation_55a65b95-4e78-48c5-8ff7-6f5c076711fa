import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, CreditCard, CheckCircle, XCircle, Clock } from 'lucide-react';
import { useCreateWeChatPayment, usePaymentStatus, initiateWeChatPayment } from '@/hooks/usePayment';
import { CreateAppointmentResponse } from '@/lib/api';
import { toast } from 'sonner';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  appointmentResult: CreateAppointmentResponse;
  onPaymentSuccess: () => void;
}

export const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  appointmentResult,
  onPaymentSuccess,
}) => {
  const [paymentStep, setPaymentStep] = useState<'pending' | 'processing' | 'completed' | 'failed'>('pending');
  const [transactionId, setTransactionId] = useState<string | undefined>();

  const createWeChatPayment = useCreateWeChatPayment();
  const { data: paymentStatus, isLoading: isLoadingStatus } = usePaymentStatus(
    appointmentResult.paymentId,
    paymentStep === 'processing'
  );

  // Handle payment status changes
  useEffect(() => {
    if (paymentStatus) {
      if (paymentStatus.status === 'completed') {
        setPaymentStep('completed');
        onPaymentSuccess();
        toast.success('支付成功！预约已确认');
      } else if (paymentStatus.status === 'failed') {
        setPaymentStep('failed');
        toast.error('支付失败，请重试');
      }
    }
  }, [paymentStatus, onPaymentSuccess]);

  const handleWeChatPayment = async () => {
    if (!appointmentResult.paymentId) {
      toast.error('支付信息错误');
      return;
    }

    try {
      setPaymentStep('processing');
      
      // Create WeChat payment
      const paymentResponse = await createWeChatPayment.mutateAsync({
        paymentId: appointmentResult.paymentId,
        data: {}, // OpenID would be included here in real implementation
      });

      // Initiate WeChat payment
      const paymentResult = await initiateWeChatPayment(paymentResponse);
      
      if (paymentResult.success) {
        setTransactionId(paymentResult.transactionId);
        // Payment status will be updated via polling
      } else {
        setPaymentStep('failed');
        toast.error('支付失败，请重试');
      }
    } catch (error) {
      console.error('Payment error:', error);
      setPaymentStep('failed');
      toast.error('支付过程中出现错误');
    }
  };

  const handleClose = () => {
    if (paymentStep === 'processing') {
      toast.warning('支付正在进行中，请稍候');
      return;
    }
    onClose();
  };

  const getStatusIcon = () => {
    switch (paymentStep) {
      case 'pending':
        return <CreditCard className="h-8 w-8 text-blue-500" />;
      case 'processing':
        return <Loader2 className="h-8 w-8 text-yellow-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-8 w-8 text-green-500" />;
      case 'failed':
        return <XCircle className="h-8 w-8 text-red-500" />;
      default:
        return <Clock className="h-8 w-8 text-gray-500" />;
    }
  };

  const getStatusText = () => {
    switch (paymentStep) {
      case 'pending':
        return '等待支付';
      case 'processing':
        return '支付处理中...';
      case 'completed':
        return '支付成功';
      case 'failed':
        return '支付失败';
      default:
        return '未知状态';
    }
  };

  const getStatusColor = () => {
    switch (paymentStep) {
      case 'pending':
        return 'default';
      case 'processing':
        return 'secondary';
      case 'completed':
        return 'default';
      case 'failed':
        return 'destructive';
      default:
        return 'default';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>支付预约费用</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Payment Status */}
          <Card>
            <CardHeader className="text-center">
              <div className="flex justify-center mb-2">
                {getStatusIcon()}
              </div>
              <CardTitle className="text-lg">
                <Badge variant={getStatusColor() as any} className="text-sm">
                  {getStatusText()}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-2">
              <div className="text-2xl font-bold text-primary">
                ¥{appointmentResult.price}
              </div>
              <CardDescription>
                预约费用 • {appointmentResult.appointment.duration}分钟咨询
              </CardDescription>
              {transactionId && (
                <div className="text-xs text-muted-foreground">
                  交易号: {transactionId}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Appointment Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">预约详情</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">预约时间:</span>
                <span>{new Date(appointmentResult.appointment.start_time).toLocaleString('zh-CN')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">咨询时长:</span>
                <span>{appointmentResult.appointment.duration}分钟</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">咨询方式:</span>
                <span>{appointmentResult.appointment.meeting_type === 'online' ? '在线咨询' : '线下咨询'}</span>
              </div>
            </CardContent>
          </Card>

          {/* Payment Actions */}
          <div className="flex gap-2">
            {paymentStep === 'pending' && (
              <>
                <Button
                  onClick={handleWeChatPayment}
                  disabled={createWeChatPayment.isPending}
                  className="flex-1"
                >
                  {createWeChatPayment.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      处理中...
                    </>
                  ) : (
                    <>
                      <CreditCard className="mr-2 h-4 w-4" />
                      微信支付
                    </>
                  )}
                </Button>
                <Button variant="outline" onClick={handleClose}>
                  取消
                </Button>
              </>
            )}
            
            {paymentStep === 'processing' && (
              <Button disabled className="flex-1">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                支付处理中...
              </Button>
            )}
            
            {(paymentStep === 'completed' || paymentStep === 'failed') && (
              <Button onClick={handleClose} className="flex-1">
                {paymentStep === 'completed' ? '完成' : '重试'}
              </Button>
            )}
          </div>

          {paymentStep === 'processing' && (
            <div className="text-center text-sm text-muted-foreground">
              请在微信中完成支付，支付完成后会自动更新状态
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
