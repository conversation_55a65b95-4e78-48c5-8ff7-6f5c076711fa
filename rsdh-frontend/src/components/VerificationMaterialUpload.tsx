import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Upload, FileText, CheckCircle, X, AlertCircle } from 'lucide-react';
import { toast } from "sonner";
import { apiClient } from '@/lib/api';

interface VerificationMaterial {
  id: string;
  materialType: string;
  description?: string;
  status: 'pending' | 'approved' | 'rejected';
  file: {
    id: string;
    originalName: string;
    size: number;
  };
  reviewNotes?: string;
  createdAt: string;
}

interface VerificationMaterialUploadProps {
  type: 'education' | 'career';
  recordId: string;
  recordTitle: string;
  onUploadComplete?: (material: VerificationMaterial) => void;
  existingMaterials?: VerificationMaterial[];
}

const EDUCATION_MATERIAL_TYPES = [
  { value: 'diploma', label: '毕业证书' },
  { value: 'transcript', label: '成绩单' },
  { value: 'certificate', label: '学位证书' },
  { value: 'degree_certificate', label: '学历证明' },
  { value: 'other', label: '其他' }
];

const CAREER_MATERIAL_TYPES = [
  { value: 'work_certificate', label: '在职证明' },
  { value: 'business_card', label: '工牌/名片' },
  { value: 'contract', label: '劳动合同' },
  { value: 'employment_letter', label: '录用通知书' },
  { value: 'other', label: '其他' }
];

const VerificationMaterialUpload: React.FC<VerificationMaterialUploadProps> = ({
  type,
  recordId,
  recordTitle,
  onUploadComplete,
  existingMaterials = []
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [materialType, setMaterialType] = useState('');
  const [description, setDescription] = useState('');

  const materialTypes = type === 'education' ? EDUCATION_MATERIAL_TYPES : CAREER_MATERIAL_TYPES;

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('请上传图片或PDF文档');
        return;
      }

      // Validate file size (10MB max)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('文件大小不能超过10MB');
        return;
      }

      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !materialType) {
      toast.error('请选择文件和材料类型');
      return;
    }

    setIsUploading(true);

    try {
      // First upload the file
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('category', 'verification');

      const uploadResponse = await fetch('/api/files/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: formData
      });

      if (!uploadResponse.ok) {
        throw new Error('文件上传失败');
      }

      const uploadedFile = await uploadResponse.json();

      // Then create verification material record
      const endpoint = type === 'education' ? '/verification-materials/education' : '/verification-materials/career';
      const recordKey = type === 'education' ? 'educationId' : 'careerId';

      const verificationData = {
        [recordKey]: recordId,
        fileId: uploadedFile.id,
        materialType,
        description: description.trim() || undefined
      };

      const verificationResponse = await apiClient.post(endpoint, verificationData);

      toast.success('验证材料上传成功');
      
      // Reset form
      setSelectedFile(null);
      setMaterialType('');
      setDescription('');
      
      // Clear file input
      const fileInput = document.getElementById('file-input') as HTMLInputElement;
      if (fileInput) {
        fileInput.value = '';
      }

      if (onUploadComplete) {
        onUploadComplete(verificationResponse);
      }

    } catch (error) {
      console.error('Upload error:', error);
      toast.error(error instanceof Error ? error.message : '上传失败');
    } finally {
      setIsUploading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'rejected':
        return <X className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved':
        return '已通过';
      case 'rejected':
        return '未通过';
      default:
        return '待审核';
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card className="border-0 shadow-sm">
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <FileText className="h-4 w-4" />
          {recordTitle} - 验证材料
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Existing Materials */}
        {existingMaterials.length > 0 && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">已上传材料</Label>
            {existingMaterials.map((material) => (
              <div key={material.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <FileText className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">{material.file.originalName}</p>
                    <p className="text-xs text-gray-500">
                      {materialTypes.find(t => t.value === material.materialType)?.label} • 
                      {formatFileSize(material.file.size)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(material.status)}
                  <span className="text-xs">{getStatusText(material.status)}</span>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Upload New Material */}
        <div className="space-y-4">
          <Label className="text-sm font-medium">上传新材料</Label>
          
          <div>
            <Label htmlFor="material-type">材料类型 *</Label>
            <Select value={materialType} onValueChange={setMaterialType}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="请选择材料类型" />
              </SelectTrigger>
              <SelectContent>
                {materialTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="file-input">选择文件 *</Label>
            <div className="mt-1">
              <input
                id="file-input"
                type="file"
                accept="image/*,.pdf,.doc,.docx"
                onChange={handleFileSelect}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              {selectedFile && (
                <p className="mt-1 text-xs text-gray-500">
                  已选择: {selectedFile.name} ({formatFileSize(selectedFile.size)})
                </p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="description">说明（可选）</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="请简要说明该材料的相关信息"
              className="mt-1"
              rows={3}
            />
          </div>

          <Button
            onClick={handleUpload}
            disabled={!selectedFile || !materialType || isUploading}
            className="w-full"
          >
            {isUploading ? (
              <>
                <Upload className="h-4 w-4 mr-2 animate-spin" />
                上传中...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                上传验证材料
              </>
            )}
          </Button>
        </div>

        <div className="text-xs text-gray-500 space-y-1">
          <p>• 支持格式：JPG、PNG、GIF、PDF、DOC、DOCX</p>
          <p>• 文件大小：最大10MB</p>
          <p>• 上传后将由管理员审核，请确保材料清晰可见</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default VerificationMaterialUpload;
