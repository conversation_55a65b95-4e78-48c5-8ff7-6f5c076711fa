import React, { useState, useRef, useCallback } from 'react';
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent } from "@/components/ui/card";
import { Upload, X, File, Image, Video, Music, FileText } from 'lucide-react';
import { toast } from "sonner";

interface FileUploadProps {
  onUploadComplete?: (file: UploadedFile) => void;
  onUploadError?: (error: string) => void;
  accept?: string;
  maxSize?: number; // in bytes
  category?: string;
  isPublic?: boolean;
  multiple?: boolean;
  className?: string;
}

interface UploadedFile {
  id: string;
  originalName: string;
  fileName: string;
  mimeType: string;
  size: number;
  category: string;
  isPublic: boolean;
  url: string;
  createdAt: string;
}

interface FileWithProgress {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
  uploadedFile?: UploadedFile;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onUploadComplete,
  onUploadError,
  accept = "image/*,video/*,audio/*,.pdf,.doc,.docx",
  maxSize = 10 * 1024 * 1024, // 10MB
  category = "general",
  isPublic = false,
  multiple = false,
  className = "",
}) => {
  const [files, setFiles] = useState<FileWithProgress[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <Image className="h-4 w-4" />;
    if (mimeType.startsWith('video/')) return <Video className="h-4 w-4" />;
    if (mimeType.startsWith('audio/')) return <Music className="h-4 w-4" />;
    if (mimeType.includes('pdf') || mimeType.includes('document')) return <FileText className="h-4 w-4" />;
    return <File className="h-4 w-4" />;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = (file: File): string | null => {
    if (file.size > maxSize) {
      return `File size exceeds ${formatFileSize(maxSize)}`;
    }

    if (accept && accept !== "*/*") {
      const acceptedTypes = accept.split(',').map(type => type.trim());
      const isAccepted = acceptedTypes.some(type => {
        if (type.startsWith('.')) {
          return file.name.toLowerCase().endsWith(type.toLowerCase());
        }
        if (type.includes('*')) {
          const baseType = type.split('/')[0];
          return file.type.startsWith(baseType);
        }
        return file.type === type;
      });

      if (!isAccepted) {
        return `File type not accepted. Accepted types: ${accept}`;
      }
    }

    return null;
  };

  const uploadFile = async (fileWithProgress: FileWithProgress): Promise<void> => {
    const { file } = fileWithProgress;
    
    try {
      setFiles(prev => prev.map(f => 
        f.file === file ? { ...f, status: 'uploading', progress: 0 } : f
      ));

      const formData = new FormData();
      formData.append('file', file);
      formData.append('category', category);
      formData.append('isPublic', isPublic.toString());

      const response = await fetch('/api/files/upload', {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Upload failed');
      }

      const uploadedFile: UploadedFile = await response.json();

      setFiles(prev => prev.map(f => 
        f.file === file ? { 
          ...f, 
          status: 'completed', 
          progress: 100, 
          uploadedFile 
        } : f
      ));

      onUploadComplete?.(uploadedFile);
      toast.success(`File "${file.name}" uploaded successfully`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      
      setFiles(prev => prev.map(f => 
        f.file === file ? { 
          ...f, 
          status: 'error', 
          error: errorMessage 
        } : f
      ));

      onUploadError?.(errorMessage);
      toast.error(`Failed to upload "${file.name}": ${errorMessage}`);
    }
  };

  const handleFiles = useCallback((fileList: FileList) => {
    const newFiles: FileWithProgress[] = [];

    Array.from(fileList).forEach(file => {
      const error = validateFile(file);
      if (error) {
        toast.error(`${file.name}: ${error}`);
        return;
      }

      newFiles.push({
        file,
        progress: 0,
        status: 'pending',
      });
    });

    if (multiple) {
      setFiles(prev => [...prev, ...newFiles]);
    } else {
      setFiles(newFiles);
    }

    // Start uploading files
    newFiles.forEach(fileWithProgress => {
      uploadFile(fileWithProgress);
    });
  }, [multiple, maxSize, accept, category, isPublic]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const { files } = e.dataTransfer;
    if (files.length > 0) {
      handleFiles(files);
    }
  }, [handleFiles]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = e.target;
    if (files && files.length > 0) {
      handleFiles(files);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  }, [handleFiles]);

  const removeFile = (fileToRemove: File) => {
    setFiles(prev => prev.filter(f => f.file !== fileToRemove));
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <Card 
        className={`border-2 border-dashed transition-colors cursor-pointer ${
          isDragOver 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={openFileDialog}
      >
        <CardContent className="flex flex-col items-center justify-center py-8 text-center">
          <Upload className="h-12 w-12 text-gray-400 mb-4" />
          <p className="text-lg font-medium text-gray-700 mb-2">
            Drop files here or click to browse
          </p>
          <p className="text-sm text-gray-500">
            Max size: {formatFileSize(maxSize)}
          </p>
          {accept && (
            <p className="text-xs text-gray-400 mt-1">
              Accepted: {accept}
            </p>
          )}
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-2">
          {files.map((fileWithProgress, index) => (
            <Card key={index} className="p-3">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  {getFileIcon(fileWithProgress.file.type)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {fileWithProgress.file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(fileWithProgress.file.size)}
                  </p>
                  
                  {fileWithProgress.status === 'uploading' && (
                    <Progress 
                      value={fileWithProgress.progress} 
                      className="mt-2 h-2"
                    />
                  )}
                  
                  {fileWithProgress.status === 'error' && (
                    <p className="text-xs text-red-500 mt-1">
                      {fileWithProgress.error}
                    </p>
                  )}
                  
                  {fileWithProgress.status === 'completed' && (
                    <p className="text-xs text-green-500 mt-1">
                      Upload completed
                    </p>
                  )}
                </div>

                <div className="flex-shrink-0">
                  {fileWithProgress.status === 'pending' || fileWithProgress.status === 'error' ? (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFile(fileWithProgress.file);
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  ) : fileWithProgress.status === 'completed' ? (
                    <div className="text-green-500">
                      ✓
                    </div>
                  ) : (
                    <div className="text-blue-500">
                      ⏳
                    </div>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default FileUpload;
