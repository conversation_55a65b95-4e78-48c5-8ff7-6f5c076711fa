
import { useQuery } from '@tanstack/react-query';
import { apiClient, Tutor, PaginatedResponse } from '@/lib/api';

export interface TutorFilters {
  searchText?: string;
  page?: number;
  limit?: number;
  // Advanced filters
  institution?: string;
  graduationYearFrom?: number;
  graduationYearTo?: number;
  career?: string;
  degree?: string;
  is985?: boolean;
  is211?: boolean;
  ratingFrom?: number;
  ratingTo?: number;
  priceFrom?: number;
  priceTo?: number;
  // Legacy filters for backward compatibility
  province?: string;
  university?: string;
  year?: string;
}

export const useTutors = (filters?: TutorFilters) => {
  return useQuery({
    queryKey: ['tutors', filters],
    queryFn: async (): Promise<PaginatedResponse<Tutor>> => {
      const params = new URLSearchParams();

      if (filters?.searchText) {
        params.append('search', filters.searchText);
      }
      if (filters?.page) {
        params.append('page', filters.page.toString());
      }
      if (filters?.limit) {
        params.append('limit', filters.limit.toString());
      }

      // Advanced filters
      if (filters?.institution) {
        params.append('institution', filters.institution);
      }
      if (filters?.graduationYearFrom) {
        params.append('graduationYearFrom', filters.graduationYearFrom.toString());
      }
      if (filters?.graduationYearTo) {
        params.append('graduationYearTo', filters.graduationYearTo.toString());
      }
      if (filters?.career) {
        params.append('career', filters.career);
      }
      if (filters?.degree) {
        params.append('degree', filters.degree);
      }
      if (filters?.is985 !== undefined) {
        params.append('is985', filters.is985.toString());
      }
      if (filters?.is211 !== undefined) {
        params.append('is211', filters.is211.toString());
      }
      if (filters?.ratingFrom !== undefined) {
        params.append('ratingFrom', filters.ratingFrom.toString());
      }
      if (filters?.ratingTo !== undefined) {
        params.append('ratingTo', filters.ratingTo.toString());
      }
      if (filters?.priceFrom !== undefined) {
        params.append('priceFrom', filters.priceFrom.toString());
      }
      if (filters?.priceTo !== undefined) {
        params.append('priceTo', filters.priceTo.toString());
      }

      // Legacy filters (map to new filters for backward compatibility)
      if (filters?.university && filters.university !== 'all') {
        params.append('institution', filters.university);
      }
      if (filters?.year && filters.year !== 'all') {
        const year = parseInt(filters.year);
        params.append('graduationYearFrom', year.toString());
        params.append('graduationYearTo', year.toString());
      }

      const queryString = params.toString();
      const endpoint = queryString ? `/tutors?${queryString}` : '/tutors';

      return await apiClient.get<PaginatedResponse<Tutor>>(endpoint);
    },
  });
};

export const useTutor = (tutorId: string) => {
  return useQuery({
    queryKey: ['tutor', tutorId],
    queryFn: async (): Promise<Tutor> => {
      return await apiClient.get<Tutor>(`/tutors/${tutorId}`);
    },
    enabled: !!tutorId,
  });
};
