
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient, CreateAppointmentRequest, CreateAppointmentResponse } from '@/lib/api';
import { toast } from 'sonner';

// Use the CreateAppointmentRequest type from API

export const useCreateAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateAppointmentRequest): Promise<CreateAppointmentResponse> => {
      console.log('Creating appointment:', data);
      return await apiClient.post<CreateAppointmentResponse>('/appointments', data);
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['user-appointments'] });
      queryClient.invalidateQueries({ queryKey: ['tutor-appointments'] });
      console.log('Appointment created:', result.appointment.id, 'Requires payment:', result.requiresPayment);

      if (result.requiresPayment) {
        toast.success(`预约创建成功，需要支付 ¥${result.price}`);
      } else {
        toast.success('免费预约创建成功，已通知导师');
      }
    },
    onError: (error) => {
      console.error('Create appointment error:', error);
      toast.error('预约创建失败，请重试');
    },
  });
};
