import { useMutation, useQuery } from '@tanstack/react-query';
import { apiClient, PaymentRequest, PaymentResponse, PaymentStatus } from '@/lib/api';
import { toast } from 'sonner';

export const useCreateWeChatPayment = () => {
  return useMutation({
    mutationFn: async ({ paymentId, data }: { paymentId: string; data: PaymentRequest }): Promise<PaymentResponse> => {
      console.log('Creating WeChat payment for:', paymentId);
      return await apiClient.createWeChatPayment(paymentId, data);
    },
    onSuccess: (response) => {
      console.log('WeChat payment created:', response);
      // The actual payment will be handled by WeChat SDK
    },
    onError: (error) => {
      console.error('WeChat payment creation error:', error);
      toast.error('支付创建失败，请重试');
    },
  });
};

export const usePaymentStatus = (paymentId: string | undefined, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['payment-status', paymentId],
    queryFn: async (): Promise<PaymentStatus> => {
      if (!paymentId) {
        throw new Error('Payment ID is required');
      }
      return await apiClient.getPaymentStatus(paymentId);
    },
    enabled: enabled && !!paymentId,
    refetchInterval: (data) => {
      // Stop polling if payment is completed or failed
      if (data?.status === 'completed' || data?.status === 'failed') {
        return false;
      }
      // Poll every 3 seconds for pending payments
      return 3000;
    },
  });
};

export const useConfirmAppointment = () => {
  return useMutation({
    mutationFn: async ({ appointmentId, token }: { appointmentId: string; token: string }) => {
      return await apiClient.confirmAppointment(appointmentId, token);
    },
    onSuccess: (response) => {
      toast.success(response.message || '预约确认成功');
    },
    onError: (error) => {
      console.error('Appointment confirmation error:', error);
      toast.error('预约确认失败');
    },
  });
};

export const useRejectAppointment = () => {
  return useMutation({
    mutationFn: async ({ appointmentId, token }: { appointmentId: string; token: string }) => {
      return await apiClient.rejectAppointment(appointmentId, token);
    },
    onSuccess: (response) => {
      toast.success(response.message || '预约已拒绝');
    },
    onError: (error) => {
      console.error('Appointment rejection error:', error);
      toast.error('预约拒绝失败');
    },
  });
};

// Mock WeChat payment function for testing
export const mockWeChatPay = (paymentData: PaymentResponse): Promise<{ success: boolean; transactionId?: string }> => {
  return new Promise((resolve) => {
    // Simulate WeChat payment process
    console.log('Mock WeChat payment initiated with data:', paymentData);
    
    // Simulate user interaction and payment completion
    setTimeout(() => {
      const success = Math.random() > 0.1; // 90% success rate for testing
      
      if (success) {
        const transactionId = `mock_tx_${Date.now()}`;
        console.log('Mock WeChat payment successful:', transactionId);
        resolve({ success: true, transactionId });
      } else {
        console.log('Mock WeChat payment failed');
        resolve({ success: false });
      }
    }, 2000); // 2 second delay to simulate payment process
  });
};

// WeChat payment integration (placeholder for real implementation)
export const initiateWeChatPayment = async (paymentData: PaymentResponse): Promise<{ success: boolean; transactionId?: string }> => {
  // In a real implementation, this would integrate with WeChat Pay SDK
  // For now, we'll use the mock function
  
  if (typeof window !== 'undefined' && (window as any).WeixinJSBridge) {
    // Real WeChat environment
    return new Promise((resolve) => {
      (window as any).WeixinJSBridge.invoke(
        'getBrandWCPayRequest',
        {
          appId: paymentData.prepayId.split('_')[0], // This would be the real app ID
          timeStamp: paymentData.timeStamp,
          nonceStr: paymentData.nonceStr,
          package: paymentData.package,
          signType: paymentData.signType,
          paySign: paymentData.paySign,
        },
        (res: any) => {
          if (res.err_msg === 'get_brand_wcpay_request:ok') {
            resolve({ success: true, transactionId: `wx_${Date.now()}` });
          } else {
            resolve({ success: false });
          }
        }
      );
    });
  } else {
    // Development/testing environment
    return mockWeChatPay(paymentData);
  }
};
