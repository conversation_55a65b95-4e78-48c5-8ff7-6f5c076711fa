import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, FileText, GraduationCap, Briefcase, Plus } from "lucide-react";
import { Link } from "react-router-dom";
import { toast } from 'sonner';
import { apiClient, TutorVerifications, VerificationMaterial } from '@/lib/api';
import VerificationMaterialUpload from '@/components/VerificationMaterialUpload';

interface TutorProfile {
  id: string;
  title?: string;
  bio?: string;
  rate: number;
  status: string;
  education: Array<{
    id: string;
    degree: string;
    fieldOfStudy: string;
    institution: string;
    startYear: number;
    endYear?: number;
    description?: string;
  }>;
  career: Array<{
    id: string;
    title: string;
    company: string;
    startYear: number;
    endYear?: number;
    current: boolean;
    description?: string;
  }>;
}

const TutorVerificationMaterials = () => {
  const [tutorProfile, setTutorProfile] = useState<TutorProfile | null>(null);
  const [verifications, setVerifications] = useState<TutorVerifications | null>(null);
  const [loading, setLoading] = useState(true);
  const [showUploadModal, setShowUploadModal] = useState<{
    type: 'education' | 'career';
    recordId: string;
    recordTitle: string;
  } | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch tutor profile
      const profileResponse = await apiClient.get('/tutors/manage/profile');
      setTutorProfile(profileResponse);

      // Fetch verification materials
      const verificationsResponse = await apiClient.get('/verification-materials/tutor/my-verifications');
      setVerifications(verificationsResponse);

    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleUploadComplete = (material: VerificationMaterial) => {
    // Refresh verification materials
    fetchData();
    setShowUploadModal(null);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge variant="default" className="bg-green-500">已通过</Badge>;
      case 'rejected':
        return <Badge variant="destructive">未通过</Badge>;
      default:
        return <Badge variant="secondary">待审核</Badge>;
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getMaterialTypeLabel = (type: string, category: 'education' | 'career') => {
    const educationTypes: Record<string, string> = {
      'diploma': '毕业证书',
      'transcript': '成绩单',
      'certificate': '学位证书',
      'degree_certificate': '学历证明',
      'other': '其他'
    };

    const careerTypes: Record<string, string> = {
      'work_certificate': '在职证明',
      'business_card': '工牌/名片',
      'contract': '劳动合同',
      'employment_letter': '录用通知书',
      'other': '其他'
    };

    const types = category === 'education' ? educationTypes : careerTypes;
    return types[type] || type;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (!tutorProfile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">未找到导师资料</p>
          <Link to="/tutor-register">
            <Button className="mt-4">申请成为导师</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Link to="/tutor-dashboard">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">验证材料管理</h1>
            <p className="text-gray-600">管理您的教育背景和工作经历验证材料</p>
          </div>
        </div>

        {/* Education Verifications */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GraduationCap className="h-5 w-5" />
                教育背景验证材料
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {tutorProfile.education.map((education) => {
                const educationVerifications = verifications?.educationVerifications.filter(
                  v => v.educationId === education.id
                ) || [];

                return (
                  <div key={education.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-medium">{education.degree} - {education.fieldOfStudy}</h3>
                        <p className="text-sm text-gray-600">{education.institution}</p>
                        <p className="text-xs text-gray-500">
                          {education.startYear} - {education.endYear || '至今'}
                        </p>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setShowUploadModal({
                          type: 'education',
                          recordId: education.id,
                          recordTitle: `${education.degree} - ${education.fieldOfStudy}`
                        })}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        上传材料
                      </Button>
                    </div>

                    {educationVerifications.length > 0 ? (
                      <div className="space-y-2">
                        {educationVerifications.map((verification) => (
                          <div key={verification.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <div className="flex items-center gap-3">
                              <FileText className="h-4 w-4 text-gray-500" />
                              <div>
                                <p className="text-sm font-medium">{verification.file.originalName}</p>
                                <p className="text-xs text-gray-500">
                                  {getMaterialTypeLabel(verification.materialType, 'education')} • 
                                  {formatFileSize(verification.file.size)}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {getStatusBadge(verification.status)}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">暂无验证材料</p>
                    )}
                  </div>
                );
              })}
            </CardContent>
          </Card>

          {/* Career Verifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                工作经历验证材料
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {tutorProfile.career.map((career) => {
                const careerVerifications = verifications?.careerVerifications.filter(
                  v => v.careerId === career.id
                ) || [];

                return (
                  <div key={career.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-medium">{career.title}</h3>
                        <p className="text-sm text-gray-600">{career.company}</p>
                        <p className="text-xs text-gray-500">
                          {career.startYear} - {career.current ? '至今' : career.endYear}
                        </p>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setShowUploadModal({
                          type: 'career',
                          recordId: career.id,
                          recordTitle: `${career.title} - ${career.company}`
                        })}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        上传材料
                      </Button>
                    </div>

                    {careerVerifications.length > 0 ? (
                      <div className="space-y-2">
                        {careerVerifications.map((verification) => (
                          <div key={verification.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <div className="flex items-center gap-3">
                              <FileText className="h-4 w-4 text-gray-500" />
                              <div>
                                <p className="text-sm font-medium">{verification.file.originalName}</p>
                                <p className="text-xs text-gray-500">
                                  {getMaterialTypeLabel(verification.materialType, 'career')} • 
                                  {formatFileSize(verification.file.size)}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {getStatusBadge(verification.status)}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">暂无验证材料</p>
                    )}
                  </div>
                );
              })}
            </CardContent>
          </Card>
        </div>

        {/* Upload Modal */}
        {showUploadModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <VerificationMaterialUpload
                type={showUploadModal.type}
                recordId={showUploadModal.recordId}
                recordTitle={showUploadModal.recordTitle}
                onUploadComplete={handleUploadComplete}
              />
              <Button
                variant="outline"
                className="w-full mt-4"
                onClick={() => setShowUploadModal(null)}
              >
                取消
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TutorVerificationMaterials;
