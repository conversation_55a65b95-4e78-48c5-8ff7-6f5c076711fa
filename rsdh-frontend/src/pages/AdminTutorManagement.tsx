import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { 
  GraduationCap, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Ban, 
  CheckCircle, 
  XCircle,
  Clock,
  Star,
  AlertTriangle,
  Eye,
  UserCheck,
  UserX
} from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { apiClient } from '@/lib/api';
import { toast } from 'sonner';

interface Tutor {
  id: string;
  userId: string;
  title: string | null;
  bio: string | null;
  hourlyRate: number | null;
  halfHourRate: number | null;
  isFree: boolean;
  status: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string | null;
    email: string;
    phoneNumber: string | null;
  };
  _count?: {
    appointments: number;
    reviews: number;
  };
  averageRating?: number;
}

interface TutorListResponse {
  tutors: Tutor[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

const AdminTutorManagement = () => {
  const [tutors, setTutors] = useState<Tutor[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [ratingFilter, setRatingFilter] = useState('all');
  const [selectedTutor, setSelectedTutor] = useState<Tutor | null>(null);
  const [showTutorDetails, setShowTutorDetails] = useState(false);
  const [showStatusDialog, setShowStatusDialog] = useState(false);
  const [statusChangeData, setStatusChangeData] = useState({
    tutorId: '',
    newStatus: '',
    reason: ''
  });

  useEffect(() => {
    fetchTutors();
  }, [pagination.page, pagination.limit, searchTerm, statusFilter, ratingFilter]);

  const fetchTutors = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      });

      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (ratingFilter !== 'all') params.append('rating', ratingFilter);

      const response: TutorListResponse = await apiClient.get(`/tutors?${params.toString()}`);
      
      setTutors(response.tutors);
      setPagination(response.pagination);

    } catch (error) {
      console.error('Error fetching tutors:', error);
      toast.error('获取导师列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleViewTutorDetails = async (tutor: Tutor) => {
    try {
      const tutorDetails = await apiClient.get(`/tutors/${tutor.id}`);
      setSelectedTutor(tutorDetails);
      setShowTutorDetails(true);
    } catch (error) {
      console.error('Error fetching tutor details:', error);
      toast.error('获取导师详情失败');
    }
  };

  const handleStatusChange = (tutorId: string, newStatus: string) => {
    setStatusChangeData({
      tutorId,
      newStatus,
      reason: ''
    });
    setShowStatusDialog(true);
  };

  const submitStatusChange = async () => {
    try {
      await apiClient.patch(`/admin/tutors/${statusChangeData.tutorId}/status`, {
        status: statusChangeData.newStatus,
        reason: statusChangeData.reason
      });
      
      toast.success('导师状态已更新');
      setShowStatusDialog(false);
      setStatusChangeData({ tutorId: '', newStatus: '', reason: '' });
      fetchTutors(); // Refresh the list
    } catch (error) {
      console.error('Error updating tutor status:', error);
      toast.error('更新导师状态失败');
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'suspended': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return '已审核';
      case 'pending': return '待审核';
      case 'rejected': return '已拒绝';
      case 'suspended': return '已停用';
      default: return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'rejected': return <XCircle className="h-4 w-4" />;
      case 'suspended': return <Ban className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return 'text-green-600';
    if (rating >= 4.0) return 'text-blue-600';
    if (rating >= 3.5) return 'text-yellow-600';
    if (rating >= 3.0) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">导师管理</h1>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-sm">
            总计: {pagination.total} 导师
          </Badge>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            筛选条件
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="space-y-2">
              <Label>搜索导师</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="姓名或邮箱..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>状态筛选</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="pending">待审核</SelectItem>
                  <SelectItem value="approved">已审核</SelectItem>
                  <SelectItem value="rejected">已拒绝</SelectItem>
                  <SelectItem value="suspended">已停用</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>评分筛选</Label>
              <Select value={ratingFilter} onValueChange={setRatingFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择评分" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部评分</SelectItem>
                  <SelectItem value="high">4.5+ 高分</SelectItem>
                  <SelectItem value="good">4.0-4.5 良好</SelectItem>
                  <SelectItem value="average">3.5-4.0 一般</SelectItem>
                  <SelectItem value="low">3.5以下 较低</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>每页显示</Label>
              <Select 
                value={pagination.limit.toString()} 
                onValueChange={(value) => setPagination(prev => ({ ...prev, limit: parseInt(value), page: 1 }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button onClick={fetchTutors} className="w-full">
                刷新数据
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tutors Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <GraduationCap className="h-5 w-5 mr-2" />
            导师列表
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>导师信息</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>评分</TableHead>
                    <TableHead>价格</TableHead>
                    <TableHead>统计</TableHead>
                    <TableHead>注册时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tutors.map((tutor) => (
                    <TableRow key={tutor.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{tutor.user.name || '未设置姓名'}</div>
                          <div className="text-sm text-gray-500">{tutor.user.email}</div>
                          {tutor.title && (
                            <div className="text-xs text-gray-400">{tutor.title}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(tutor.status)}>
                          <div className="flex items-center space-x-1">
                            {getStatusIcon(tutor.status)}
                            <span>{getStatusText(tutor.status)}</span>
                          </div>
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {tutor.averageRating ? (
                          <div className={`flex items-center ${getRatingColor(tutor.averageRating)}`}>
                            <Star className="h-4 w-4 mr-1 fill-current" />
                            <span className="font-medium">{tutor.averageRating.toFixed(1)}</span>
                            <span className="text-xs text-gray-500 ml-1">
                              ({tutor._count?.reviews || 0})
                            </span>
                          </div>
                        ) : (
                          <span className="text-gray-400">暂无评分</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {tutor.isFree ? (
                          <Badge variant="secondary" className="text-green-600">免费</Badge>
                        ) : (
                          <div className="text-sm">
                            {tutor.hourlyRate && (
                              <div>¥{tutor.hourlyRate}/小时</div>
                            )}
                            {tutor.halfHourRate && (
                              <div className="text-xs text-gray-500">¥{tutor.halfHourRate}/半小时</div>
                            )}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>预约: {tutor._count?.appointments || 0}</div>
                          <div className="text-xs text-gray-500">
                            评价: {tutor._count?.reviews || 0}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(tutor.createdAt).toLocaleDateString('zh-CN')}
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleViewTutorDetails(tutor)}>
                              <Eye className="h-4 w-4 mr-2" />
                              查看详情
                            </DropdownMenuItem>
                            {tutor.status === 'pending' && (
                              <>
                                <DropdownMenuItem 
                                  onClick={() => handleStatusChange(tutor.id, 'approved')}
                                >
                                  <UserCheck className="h-4 w-4 mr-2" />
                                  审核通过
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => handleStatusChange(tutor.id, 'rejected')}
                                >
                                  <UserX className="h-4 w-4 mr-2" />
                                  审核拒绝
                                </DropdownMenuItem>
                              </>
                            )}
                            {tutor.status === 'approved' && (
                              <DropdownMenuItem 
                                onClick={() => handleStatusChange(tutor.id, 'suspended')}
                              >
                                <Ban className="h-4 w-4 mr-2" />
                                停用导师
                              </DropdownMenuItem>
                            )}
                            {tutor.status === 'suspended' && (
                              <DropdownMenuItem 
                                onClick={() => handleStatusChange(tutor.id, 'approved')}
                              >
                                <CheckCircle className="h-4 w-4 mr-2" />
                                恢复导师
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-500">
                  显示 {((pagination.page - 1) * pagination.limit) + 1} 到{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
                  共 {pagination.total} 条记录
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page <= 1}
                  >
                    上一页
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page >= pagination.totalPages}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Status Change Dialog */}
      <Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>更改导师状态</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>新状态</Label>
              <p className="text-sm font-medium">{getStatusText(statusChangeData.newStatus)}</p>
            </div>
            <div>
              <Label htmlFor="reason">变更原因</Label>
              <Textarea
                id="reason"
                placeholder="请输入状态变更的原因..."
                value={statusChangeData.reason}
                onChange={(e) => setStatusChangeData(prev => ({ ...prev, reason: e.target.value }))}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowStatusDialog(false)}>
                取消
              </Button>
              <Button onClick={submitStatusChange}>
                确认更改
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Tutor Details Dialog */}
      <Dialog open={showTutorDetails} onOpenChange={setShowTutorDetails}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>导师详情</DialogTitle>
          </DialogHeader>
          {selectedTutor && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>姓名</Label>
                  <p className="text-sm">{selectedTutor.user.name || '未设置'}</p>
                </div>
                <div>
                  <Label>邮箱</Label>
                  <p className="text-sm">{selectedTutor.user.email}</p>
                </div>
                <div>
                  <Label>手机号</Label>
                  <p className="text-sm">{selectedTutor.user.phoneNumber || '未设置'}</p>
                </div>
                <div>
                  <Label>状态</Label>
                  <Badge className={getStatusColor(selectedTutor.status)}>
                    {getStatusText(selectedTutor.status)}
                  </Badge>
                </div>
              </div>
              
              {selectedTutor.bio && (
                <div>
                  <Label>个人简介</Label>
                  <p className="text-sm mt-1 p-3 bg-gray-50 rounded">{selectedTutor.bio}</p>
                </div>
              )}
              
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label>价格设置</Label>
                  <div className="text-sm mt-1">
                    {selectedTutor.isFree ? (
                      <Badge variant="secondary" className="text-green-600">免费服务</Badge>
                    ) : (
                      <div>
                        {selectedTutor.hourlyRate && <div>¥{selectedTutor.hourlyRate}/小时</div>}
                        {selectedTutor.halfHourRate && <div>¥{selectedTutor.halfHourRate}/半小时</div>}
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  <Label>评分统计</Label>
                  <div className="text-sm mt-1">
                    {selectedTutor.averageRating ? (
                      <div className={getRatingColor(selectedTutor.averageRating)}>
                        ⭐ {selectedTutor.averageRating.toFixed(1)} ({selectedTutor._count?.reviews || 0} 评价)
                      </div>
                    ) : (
                      <span className="text-gray-400">暂无评分</span>
                    )}
                  </div>
                </div>
                <div>
                  <Label>预约统计</Label>
                  <div className="text-sm mt-1">
                    总预约: {selectedTutor._count?.appointments || 0} 次
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminTutorManagement;
