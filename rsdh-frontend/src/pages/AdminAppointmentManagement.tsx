import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Calendar, 
  Search, 
  Filter, 
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Users,
  TrendingUp,
  MapPin,
  Video,
  RefreshCw
} from "lucide-react";
import { 
  Line<PERSON>hart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  <PERSON>,
  <PERSON>,
  Cell
} from 'recharts';
import { apiClient } from '@/lib/api';
import { toast } from 'sonner';

interface Appointment {
  id: string;
  tutorId: string;
  studentId: string;
  status: string;
  meetingType: string;
  meetingLink: string | null;
  startTime: string;
  endTime: string;
  duration: number;
  price: number;
  currency: string;
  requiresPayment: boolean;
  paymentStatus: string;
  notes: string | null;
  createdAt: string;
  tutor: {
    user: {
      name: string | null;
      email: string;
    };
  };
  student: {
    name: string | null;
    email: string;
  };
  payment?: {
    id: string;
    status: string;
    amount: number;
  };
}

interface AppointmentStats {
  total: number;
  completed: number;
  scheduled: number;
  cancelled: number;
  thisMonth: number;
  completionRate: number;
  averageDuration: number;
  totalRevenue: number;
}

const AdminAppointmentManagement = () => {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [stats, setStats] = useState<AppointmentStats | null>(null);
  const [trendsData, setTrendsData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  useEffect(() => {
    fetchAppointmentData();
  }, [pagination.page, pagination.limit, searchTerm, statusFilter, typeFilter]);

  const fetchAppointmentData = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      });

      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (typeFilter !== 'all') params.append('type', typeFilter);

      const [appointmentsResponse, trendsResponse] = await Promise.all([
        apiClient.get(`/appointments?${params.toString()}`),
        apiClient.get('/admin/dashboard/appointment-trends')
      ]);
      
      setAppointments(appointmentsResponse.appointments || []);
      setPagination(appointmentsResponse.pagination || pagination);
      setTrendsData(trendsResponse);

      // Calculate stats from appointments data
      const allAppointments = appointmentsResponse.appointments || [];
      const total = allAppointments.length;
      const completed = allAppointments.filter((a: Appointment) => a.status === 'completed').length;
      const scheduled = allAppointments.filter((a: Appointment) => a.status === 'scheduled').length;
      const cancelled = allAppointments.filter((a: Appointment) => a.status === 'cancelled').length;
      
      const thisMonth = new Date();
      thisMonth.setDate(1);
      thisMonth.setHours(0, 0, 0, 0);
      
      const thisMonthCount = allAppointments.filter((a: Appointment) => 
        new Date(a.createdAt) >= thisMonth
      ).length;

      const completionRate = total > 0 ? (completed / total) * 100 : 0;
      
      const totalDuration = allAppointments.reduce((sum: number, a: Appointment) => sum + a.duration, 0);
      const averageDuration = total > 0 ? totalDuration / total : 0;
      
      const totalRevenue = allAppointments
        .filter((a: Appointment) => a.status === 'completed')
        .reduce((sum: number, a: Appointment) => sum + a.price, 0);

      setStats({
        total,
        completed,
        scheduled,
        cancelled,
        thisMonth: thisMonthCount,
        completionRate,
        averageDuration,
        totalRevenue
      });

    } catch (error) {
      console.error('Error fetching appointment data:', error);
      toast.error('获取预约数据失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'confirmed': return 'bg-purple-100 text-purple-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'scheduled': return '已预约';
      case 'confirmed': return '已确认';
      case 'cancelled': return '已取消';
      default: return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'scheduled': return <Clock className="h-4 w-4" />;
      case 'confirmed': return <CheckCircle className="h-4 w-4" />;
      case 'cancelled': return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getTypeIcon = (type: string) => {
    return type === 'online' ? <Video className="h-4 w-4" /> : <MapPin className="h-4 w-4" />;
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

  if (loading && !stats) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">预约管理</h1>
        <Button onClick={fetchAppointmentData} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          刷新数据
        </Button>
      </div>

      {/* Appointment Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总预约数</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                <span>本月: {stats.thisMonth}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">完成率</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.completionRate.toFixed(1)}%</div>
              <div className="text-xs text-muted-foreground">
                已完成: {stats.completed} / 总数: {stats.total}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均时长</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageDuration.toFixed(0)}分钟</div>
              <div className="text-xs text-muted-foreground">
                单次预约平均时长
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总收入</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">¥{stats.totalRevenue.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">
                已完成预约收入
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="appointments" className="space-y-4">
        <TabsList>
          <TabsTrigger value="appointments">预约记录</TabsTrigger>
          <TabsTrigger value="analytics">数据分析</TabsTrigger>
        </TabsList>

        <TabsContent value="appointments" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                筛选条件
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="搜索预约..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="预约状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="scheduled">已预约</SelectItem>
                    <SelectItem value="confirmed">已确认</SelectItem>
                    <SelectItem value="completed">已完成</SelectItem>
                    <SelectItem value="cancelled">已取消</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="预约类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="online">在线咨询</SelectItem>
                    <SelectItem value="in_person">线下面谈</SelectItem>
                  </SelectContent>
                </Select>

                <Select 
                  value={pagination.limit.toString()} 
                  onValueChange={(value) => setPagination(prev => ({ ...prev, limit: parseInt(value), page: 1 }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10条/页</SelectItem>
                    <SelectItem value="25">25条/页</SelectItem>
                    <SelectItem value="50">50条/页</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Appointments Table */}
          <Card>
            <CardHeader>
              <CardTitle>预约记录</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>预约信息</TableHead>
                        <TableHead>学生</TableHead>
                        <TableHead>导师</TableHead>
                        <TableHead>时间</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>支付</TableHead>
                        <TableHead>金额</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {appointments.map((appointment) => (
                        <TableRow key={appointment.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium text-sm">
                                {appointment.id.slice(0, 8)}
                              </div>
                              <div className="text-xs text-gray-500">
                                {appointment.duration}分钟
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium text-sm">
                                {appointment.student.name || '未设置姓名'}
                              </div>
                              <div className="text-xs text-gray-500">
                                {appointment.student.email}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium text-sm">
                                {appointment.tutor.user.name || '未设置姓名'}
                              </div>
                              <div className="text-xs text-gray-500">
                                {appointment.tutor.user.email}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="text-sm">
                                {new Date(appointment.startTime).toLocaleDateString('zh-CN')}
                              </div>
                              <div className="text-xs text-gray-500">
                                {new Date(appointment.startTime).toLocaleTimeString('zh-CN', { 
                                  hour: '2-digit', 
                                  minute: '2-digit' 
                                })} - {new Date(appointment.endTime).toLocaleTimeString('zh-CN', { 
                                  hour: '2-digit', 
                                  minute: '2-digit' 
                                })}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              {getTypeIcon(appointment.meetingType)}
                              <span className="ml-2 text-sm">
                                {appointment.meetingType === 'online' ? '在线' : '线下'}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(appointment.status)}>
                              <div className="flex items-center space-x-1">
                                {getStatusIcon(appointment.status)}
                                <span>{getStatusText(appointment.status)}</span>
                              </div>
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {appointment.requiresPayment ? (
                              <Badge className={getPaymentStatusColor(appointment.paymentStatus)}>
                                {appointment.paymentStatus === 'paid' ? '已支付' : 
                                 appointment.paymentStatus === 'pending' ? '待支付' : '失败'}
                              </Badge>
                            ) : (
                              <Badge variant="secondary">免费</Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">
                              {appointment.price > 0 ? `¥${appointment.price.toLocaleString()}` : '免费'}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* Pagination */}
                  <div className="flex items-center justify-between mt-4">
                    <div className="text-sm text-gray-500">
                      显示 {((pagination.page - 1) * pagination.limit) + 1} 到{' '}
                      {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
                      共 {pagination.total} 条记录
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                        disabled={pagination.page <= 1}
                      >
                        上一页
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                        disabled={pagination.page >= pagination.totalPages}
                      >
                        下一页
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>预约趋势</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={trendsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="month" 
                      tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short' })}
                    />
                    <YAxis />
                    <Tooltip 
                      labelFormatter={(value) => new Date(value).toLocaleDateString('zh-CN')}
                    />
                    <Line type="monotone" dataKey="total" stroke="#8884d8" strokeWidth={2} name="总预约" />
                    <Line type="monotone" dataKey="completed" stroke="#82ca9d" strokeWidth={2} name="已完成" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>预约状态分布</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={[
                        { name: '已完成', value: stats?.completed || 0, color: '#00C49F' },
                        { name: '已预约', value: stats?.scheduled || 0, color: '#0088FE' },
                        { name: '已取消', value: stats?.cancelled || 0, color: '#FF8042' }
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {[
                        { name: '已完成', value: stats?.completed || 0, color: '#00C49F' },
                        { name: '已预约', value: stats?.scheduled || 0, color: '#0088FE' },
                        { name: '已取消', value: stats?.cancelled || 0, color: '#FF8042' }
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminAppointmentManagement;
