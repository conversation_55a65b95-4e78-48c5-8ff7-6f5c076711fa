import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  Users, 
  GraduationCap, 
  Calendar, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Ban,
  Activity,
  BarChart3
} from "lucide-react";
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { apiClient } from '@/lib/api';
import { toast } from 'sonner';

interface DashboardStats {
  users: {
    total: number;
    active: number;
    newThisMonth: number;
    growth: number;
  };
  tutors: {
    total: number;
    approved: number;
    pending: number;
    rejected: number;
    averageRating: number;
  };
  appointments: {
    total: number;
    completed: number;
    scheduled: number;
    cancelled: number;
    thisMonth: number;
  };
  payments: {
    totalRevenue: number;
    thisMonthRevenue: number;
    totalTransactions: number;
    averageOrderValue: number;
  };
}

interface LowRatingTutor {
  id: string;
  userId: string;
  name: string;
  email: string;
  averageRating: number;
  reviewCount: number;
  status: string;
}

const AdminDashboardNew = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [lowRatingTutors, setLowRatingTutors] = useState<LowRatingTutor[]>([]);
  const [userGrowthData, setUserGrowthData] = useState<any[]>([]);
  const [appointmentTrends, setAppointmentTrends] = useState<any[]>([]);
  const [revenueData, setRevenueData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      const [
        statsResponse,
        lowRatingResponse,
        userGrowthResponse,
        appointmentTrendsResponse,
        revenueResponse
      ] = await Promise.all([
        apiClient.get('/admin/dashboard/stats'),
        apiClient.get('/admin/tutors/low-rating?threshold=3.0&limit=5'),
        apiClient.get('/admin/dashboard/user-growth?months=6'),
        apiClient.get('/admin/dashboard/appointment-trends'),
        apiClient.get('/admin/analytics/revenue?period=month&months=6')
      ]);

      setStats(statsResponse);
      setLowRatingTutors(lowRatingResponse.tutors || []);
      setUserGrowthData(userGrowthResponse);
      setAppointmentTrends(appointmentTrendsResponse);
      setRevenueData(revenueResponse.revenueTrends || []);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('获取仪表板数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSuspendTutor = async (tutorId: string) => {
    try {
      await apiClient.patch(`/admin/tutors/${tutorId}/status`, {
        status: 'suspended',
        reason: '评分过低，暂时停用'
      });
      
      toast.success('导师已被停用');
      fetchDashboardData(); // Refresh data
    } catch (error) {
      console.error('Error suspending tutor:', error);
      toast.error('停用导师失败');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">数据加载失败</h2>
          <Button onClick={fetchDashboardData}>重试</Button>
        </div>
      </div>
    );
  }

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">管理员仪表板</h1>
        <Button onClick={fetchDashboardData} variant="outline">
          <Activity className="h-4 w-4 mr-2" />
          刷新数据
        </Button>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总用户数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.users.total.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {stats.users.growth >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
              )}
              <span className={stats.users.growth >= 0 ? 'text-green-500' : 'text-red-500'}>
                {Math.abs(stats.users.growth).toFixed(1)}%
              </span>
              <span className="ml-1">本月新增 {stats.users.newThisMonth}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">导师管理</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.tutors.total}</div>
            <div className="flex items-center space-x-2 text-xs">
              <Badge variant="secondary" className="text-green-600">
                已审核: {stats.tutors.approved}
              </Badge>
              <Badge variant="outline" className="text-yellow-600">
                待审核: {stats.tutors.pending}
              </Badge>
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              平均评分: {stats.tutors.averageRating.toFixed(1)}⭐
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">预约统计</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.appointments.total}</div>
            <div className="flex items-center space-x-2 text-xs">
              <Badge variant="secondary" className="text-green-600">
                已完成: {stats.appointments.completed}
              </Badge>
              <Badge variant="outline" className="text-blue-600">
                进行中: {stats.appointments.scheduled}
              </Badge>
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              本月: {stats.appointments.thisMonth}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">收入统计</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{stats.payments.totalRevenue.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">
              本月: ¥{stats.payments.thisMonthRevenue.toLocaleString()}
            </div>
            <div className="text-xs text-muted-foreground">
              平均订单: ¥{stats.payments.averageOrderValue.toFixed(0)}
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">数据概览</TabsTrigger>
          <TabsTrigger value="tutors">导师管理</TabsTrigger>
          <TabsTrigger value="analytics">数据分析</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>用户增长趋势</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={userGrowthData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="month" 
                      tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short' })}
                    />
                    <YAxis />
                    <Tooltip 
                      labelFormatter={(value) => new Date(value).toLocaleDateString('zh-CN')}
                    />
                    <Line type="monotone" dataKey="count" stroke="#8884d8" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>预约趋势</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={appointmentTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="month" 
                      tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short' })}
                    />
                    <YAxis />
                    <Tooltip 
                      labelFormatter={(value) => new Date(value).toLocaleDateString('zh-CN')}
                    />
                    <Bar dataKey="total" fill="#8884d8" />
                    <Bar dataKey="completed" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="tutors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
                低分导师预警
              </CardTitle>
            </CardHeader>
            <CardContent>
              {lowRatingTutors.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <p className="text-gray-500">暂无低分导师需要关注</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {lowRatingTutors.map((tutor) => (
                    <div key={tutor.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium">{tutor.name}</h3>
                          <Badge variant="destructive">
                            {tutor.averageRating.toFixed(1)}⭐
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-500">{tutor.email}</p>
                        <p className="text-xs text-gray-400">
                          {tutor.reviewCount} 条评价
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSuspendTutor(tutor.id)}
                        >
                          <Ban className="h-4 w-4 mr-1" />
                          停用
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>收入趋势分析</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="period" 
                    tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short' })}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(value) => new Date(value).toLocaleDateString('zh-CN')}
                    formatter={(value: any) => [`¥${Number(value).toLocaleString()}`, '收入']}
                  />
                  <Line type="monotone" dataKey="revenue" stroke="#82ca9d" strokeWidth={3} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminDashboardNew;
