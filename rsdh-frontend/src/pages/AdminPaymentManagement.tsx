import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DollarSign,
  Search,
  Filter,
  TrendingUp,
  TrendingDown,
  CreditCard,
  Smartphone,
  AlertCircle,
  CheckCircle,
  Clock,
  RefreshCw
} from "lucide-react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  Pie<PERSON>hart,
  Pie,
  Cell
} from 'recharts';
import { apiClient, PaymentStats, PaymentWithDetails, PaymentListParams, SplitStats, PaymentSplitWithDetails, SplitListParams } from '@/lib/api';
import { toast } from 'sonner';

interface Payment {
  id: string;
  appointmentId: string;
  tutorId: string;
  studentId: string;
  amount: string; // Changed to string for new currency format
  currency: string;
  status: string;
  paymentMethod: string;
  transactionId: string | null;
  paidAt: string | null;
  createdAt: string;
  tutor: {
    user: {
      name: string | null;
      email: string;
    };
  };
  student: {
    name: string | null;
    email: string;
  };
}

interface PaymentStats {
  totalRevenue: number;
  thisMonthRevenue: number;
  totalTransactions: number;
  averageOrderValue: number;
  successRate: number;
  pendingAmount: number;
}

interface RevenueAnalytics {
  revenueTrends: Array<{
    period: string;
    revenue: number;
    transactions: number;
    averageOrderValue: number;
  }>;
  paymentMethods: Array<{
    paymentMethod: string;
    _sum: { amount: number };
    _count: number;
  }>;
  topTutors: Array<{
    id: string;
    name: string;
    email: string;
    totalEarnings: number;
    totalTransactions: number;
    averageOrderValue: number;
  }>;
}

const AdminPaymentManagement = () => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [stats, setStats] = useState<PaymentStats | null>(null);
  const [analytics, setAnalytics] = useState<RevenueAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [methodFilter, setMethodFilter] = useState('all');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  useEffect(() => {
    fetchPaymentData();
  }, [pagination.page, pagination.limit, searchTerm, statusFilter, methodFilter]);

  const fetchPaymentData = async () => {
    try {
      setLoading(true);

      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      });

      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (methodFilter !== 'all') params.append('method', methodFilter);

      const [paymentsResponse, analyticsResponse] = await Promise.all([
        apiClient.get(`/payments?${params.toString()}`),
        apiClient.get('/admin/analytics/revenue?period=month&months=6')
      ]);

      setPayments(paymentsResponse.payments || []);
      setPagination(paymentsResponse.pagination || pagination);
      setAnalytics(analyticsResponse);

      // Calculate stats from payments data
      const totalRevenue = paymentsResponse.payments
        ?.filter((p: Payment) => p.status === 'completed')
        .reduce((sum: number, p: Payment) => sum + p.amount, 0) || 0;

      const thisMonth = new Date();
      thisMonth.setDate(1);
      thisMonth.setHours(0, 0, 0, 0);

      const thisMonthRevenue = paymentsResponse.payments
        ?.filter((p: Payment) => p.status === 'completed' && new Date(p.createdAt) >= thisMonth)
        .reduce((sum: number, p: Payment) => sum + p.amount, 0) || 0;

      const completedPayments = paymentsResponse.payments?.filter((p: Payment) => p.status === 'completed') || [];
      const totalTransactions = completedPayments.length;
      const averageOrderValue = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;

      const totalPayments = paymentsResponse.payments?.length || 0;
      const successRate = totalPayments > 0 ? (totalTransactions / totalPayments) * 100 : 0;

      const pendingAmount = paymentsResponse.payments
        ?.filter((p: Payment) => p.status === 'pending')
        .reduce((sum: number, p: Payment) => sum + p.amount, 0) || 0;

      setStats({
        totalRevenue,
        thisMonthRevenue,
        totalTransactions,
        averageOrderValue,
        successRate,
        pendingAmount
      });

    } catch (error) {
      console.error('Error fetching payment data:', error);
      toast.error('获取支付数据失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'refunded': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'pending': return '待支付';
      case 'processing': return '处理中';
      case 'failed': return '失败';
      case 'refunded': return '已退款';
      default: return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'processing': return <RefreshCw className="h-4 w-4" />;
      case 'failed': return <AlertCircle className="h-4 w-4" />;
      case 'refunded': return <RefreshCw className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'wechat': return <Smartphone className="h-4 w-4" />;
      case 'alipay': return <Smartphone className="h-4 w-4" />;
      default: return <CreditCard className="h-4 w-4" />;
    }
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  if (loading && !stats) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">支付管理</h1>
        <Button onClick={fetchPaymentData} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          刷新数据
        </Button>
      </div>

      {/* Payment Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总收入</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">¥{stats.totalRevenue.toLocaleString()}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                <span>本月: ¥{stats.thisMonthRevenue.toLocaleString()}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">交易统计</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalTransactions}</div>
              <div className="text-xs text-muted-foreground">
                成功率: {stats.successRate.toFixed(1)}%
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均订单</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">¥{stats.averageOrderValue.toFixed(0)}</div>
              <div className="text-xs text-muted-foreground">
                单笔平均金额
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">待处理</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">¥{stats.pendingAmount.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">
                待支付金额
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="transactions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="transactions">交易记录</TabsTrigger>
          <TabsTrigger value="analytics">数据分析</TabsTrigger>
        </TabsList>

        <TabsContent value="transactions" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                筛选条件
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="搜索交易..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="支付状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="completed">已完成</SelectItem>
                    <SelectItem value="pending">待支付</SelectItem>
                    <SelectItem value="processing">处理中</SelectItem>
                    <SelectItem value="failed">失败</SelectItem>
                    <SelectItem value="refunded">已退款</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={methodFilter} onValueChange={setMethodFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="支付方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部方式</SelectItem>
                    <SelectItem value="wechat">微信支付</SelectItem>
                    <SelectItem value="alipay">支付宝</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={pagination.limit.toString()}
                  onValueChange={(value) => setPagination(prev => ({ ...prev, limit: parseInt(value), page: 1 }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10条/页</SelectItem>
                    <SelectItem value="25">25条/页</SelectItem>
                    <SelectItem value="50">50条/页</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Payments Table */}
          <Card>
            <CardHeader>
              <CardTitle>交易记录</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>交易信息</TableHead>
                        <TableHead>用户信息</TableHead>
                        <TableHead>导师信息</TableHead>
                        <TableHead>金额</TableHead>
                        <TableHead>支付方式</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>时间</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {payments.map((payment) => (
                        <TableRow key={payment.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium text-sm">
                                {payment.transactionId || payment.id.slice(0, 8)}
                              </div>
                              <div className="text-xs text-gray-500">
                                预约: {payment.appointmentId.slice(0, 8)}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium text-sm">
                                {payment.student.name || '未设置姓名'}
                              </div>
                              <div className="text-xs text-gray-500">
                                {payment.student.email}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium text-sm">
                                {payment.tutor.user.name || '未设置姓名'}
                              </div>
                              <div className="text-xs text-gray-500">
                                {payment.tutor.user.email}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">
                              ¥{payment.amount.toLocaleString()}
                            </div>
                            <div className="text-xs text-gray-500">
                              {payment.currency}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              {getMethodIcon(payment.paymentMethod)}
                              <span className="ml-2 text-sm">
                                {payment.paymentMethod === 'wechat' ? '微信支付' :
                                 payment.paymentMethod === 'alipay' ? '支付宝' : payment.paymentMethod}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(payment.status)}>
                              <div className="flex items-center space-x-1">
                                {getStatusIcon(payment.status)}
                                <span>{getStatusText(payment.status)}</span>
                              </div>
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {new Date(payment.createdAt).toLocaleDateString('zh-CN')}
                            </div>
                            <div className="text-xs text-gray-500">
                              {new Date(payment.createdAt).toLocaleTimeString('zh-CN')}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* Pagination */}
                  <div className="flex items-center justify-between mt-4">
                    <div className="text-sm text-gray-500">
                      显示 {((pagination.page - 1) * pagination.limit) + 1} 到{' '}
                      {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
                      共 {pagination.total} 条记录
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                        disabled={pagination.page <= 1}
                      >
                        上一页
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                        disabled={pagination.page >= pagination.totalPages}
                      >
                        下一页
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {analytics && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>收入趋势</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={analytics.revenueTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="period"
                        tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short' })}
                      />
                      <YAxis />
                      <Tooltip
                        labelFormatter={(value) => new Date(value).toLocaleDateString('zh-CN')}
                        formatter={(value: any) => [`¥${Number(value).toLocaleString()}`, '收入']}
                      />
                      <Line type="monotone" dataKey="revenue" stroke="#82ca9d" strokeWidth={3} />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>支付方式分布</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={analytics.paymentMethods}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ paymentMethod, percent }) =>
                          `${paymentMethod === 'wechat' ? '微信' : paymentMethod === 'alipay' ? '支付宝' : paymentMethod} ${(percent * 100).toFixed(0)}%`
                        }
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="_sum.amount"
                      >
                        {analytics.paymentMethods.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value: any) => [`¥${Number(value).toLocaleString()}`, '金额']} />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>收入排行榜</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>排名</TableHead>
                        <TableHead>导师</TableHead>
                        <TableHead>总收入</TableHead>
                        <TableHead>交易次数</TableHead>
                        <TableHead>平均订单</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {analytics.topTutors.map((tutor, index) => (
                        <TableRow key={tutor.id}>
                          <TableCell>
                            <Badge variant={index < 3 ? "default" : "secondary"}>
                              #{index + 1}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{tutor.name || '未设置姓名'}</div>
                              <div className="text-sm text-gray-500">{tutor.email}</div>
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">
                            ¥{Number(tutor.totalEarnings).toLocaleString()}
                          </TableCell>
                          <TableCell>
                            {Number(tutor.totalTransactions)}
                          </TableCell>
                          <TableCell>
                            ¥{Number(tutor.averageOrderValue).toFixed(0)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminPaymentManagement;
