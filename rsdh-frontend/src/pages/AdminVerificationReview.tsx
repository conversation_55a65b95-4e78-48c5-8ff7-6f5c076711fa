import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { 
  FileText, 
  GraduationCap, 
  Briefcase, 
  CheckCircle, 
  XCircle,
  Clock,
  User,
  Download
} from "lucide-react";
import { toast } from 'sonner';
import { apiClient, PendingVerifications, VerificationMaterial } from '@/lib/api';

const AdminVerificationReview = () => {
  const [pendingVerifications, setPendingVerifications] = useState<PendingVerifications | null>(null);
  const [loading, setLoading] = useState(true);
  const [reviewingItem, setReviewingItem] = useState<{
    type: 'education' | 'career';
    id: string;
    status: 'approved' | 'rejected';
  } | null>(null);
  const [reviewNotes, setReviewNotes] = useState('');

  useEffect(() => {
    fetchPendingVerifications();
  }, []);

  const fetchPendingVerifications = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/verification-materials/admin/pending');
      setPendingVerifications(response);
    } catch (error) {
      console.error('Error fetching pending verifications:', error);
      toast.error('获取待审核材料失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReview = async (
    type: 'education' | 'career',
    verificationId: string,
    status: 'approved' | 'rejected'
  ) => {
    setReviewingItem({ type, id: verificationId, status });
  };

  const submitReview = async () => {
    if (!reviewingItem) return;

    try {
      const endpoint = `/verification-materials/admin/${reviewingItem.type}/${reviewingItem.id}/status`;
      
      await apiClient.put(endpoint, {
        status: reviewingItem.status,
        reviewNotes: reviewNotes.trim() || undefined
      });

      toast.success(`材料已${reviewingItem.status === 'approved' ? '通过' : '拒绝'}审核`);
      
      // Reset review state
      setReviewingItem(null);
      setReviewNotes('');
      
      // Refresh data
      fetchPendingVerifications();
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('审核提交失败');
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getMaterialTypeLabel = (type: string, category: 'education' | 'career') => {
    const educationTypes: Record<string, string> = {
      'diploma': '毕业证书',
      'transcript': '成绩单',
      'certificate': '学位证书',
      'degree_certificate': '学历证明',
      'other': '其他'
    };

    const careerTypes: Record<string, string> = {
      'work_certificate': '在职证明',
      'business_card': '工牌/名片',
      'contract': '劳动合同',
      'employment_letter': '录用通知书',
      'other': '其他'
    };

    const types = category === 'education' ? educationTypes : careerTypes;
    return types[type] || type;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  const totalPending = (pendingVerifications?.educationVerifications.length || 0) + 
                     (pendingVerifications?.careerVerifications.length || 0);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">验证材料审核</h1>
          <p className="text-gray-600">
            待审核材料：{totalPending} 项
          </p>
        </div>

        {totalPending === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">暂无待审核的验证材料</p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {/* Education Verifications */}
            {pendingVerifications?.educationVerifications && pendingVerifications.educationVerifications.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <GraduationCap className="h-5 w-5" />
                    教育背景验证材料 ({pendingVerifications.educationVerifications.length})
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {pendingVerifications.educationVerifications.map((verification) => (
                    <div key={verification.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <User className="h-4 w-4 text-gray-500" />
                            <span className="font-medium">{verification.education.tutor.user.name}</span>
                            <span className="text-sm text-gray-500">({verification.education.tutor.user.email})</span>
                          </div>
                          <h3 className="font-medium">{verification.education.degree}</h3>
                          <p className="text-sm text-gray-600">{verification.education.institution}</p>
                        </div>
                        <Badge variant="secondary">
                          <Clock className="h-3 w-3 mr-1" />
                          待审核
                        </Badge>
                      </div>

                      <div className="bg-gray-50 rounded p-3 mb-3">
                        <div className="flex items-center gap-3">
                          <FileText className="h-4 w-4 text-gray-500" />
                          <div className="flex-1">
                            <p className="text-sm font-medium">{verification.file.originalName}</p>
                            <p className="text-xs text-gray-500">
                              {getMaterialTypeLabel(verification.materialType, 'education')} • 
                              {formatFileSize(verification.file.size)}
                            </p>
                            {verification.description && (
                              <p className="text-xs text-gray-600 mt-1">{verification.description}</p>
                            )}
                          </div>
                          <Button size="sm" variant="outline">
                            <Download className="h-3 w-3 mr-1" />
                            下载
                          </Button>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleReview('education', verification.id, 'approved')}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-3 w-3 mr-1" />
                          通过
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleReview('education', verification.id, 'rejected')}
                        >
                          <XCircle className="h-3 w-3 mr-1" />
                          拒绝
                        </Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Career Verifications */}
            {pendingVerifications?.careerVerifications && pendingVerifications.careerVerifications.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Briefcase className="h-5 w-5" />
                    工作经历验证材料 ({pendingVerifications.careerVerifications.length})
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {pendingVerifications.careerVerifications.map((verification) => (
                    <div key={verification.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <User className="h-4 w-4 text-gray-500" />
                            <span className="font-medium">{verification.career.tutor.user.name}</span>
                            <span className="text-sm text-gray-500">({verification.career.tutor.user.email})</span>
                          </div>
                          <h3 className="font-medium">{verification.career.title}</h3>
                          <p className="text-sm text-gray-600">{verification.career.company}</p>
                        </div>
                        <Badge variant="secondary">
                          <Clock className="h-3 w-3 mr-1" />
                          待审核
                        </Badge>
                      </div>

                      <div className="bg-gray-50 rounded p-3 mb-3">
                        <div className="flex items-center gap-3">
                          <FileText className="h-4 w-4 text-gray-500" />
                          <div className="flex-1">
                            <p className="text-sm font-medium">{verification.file.originalName}</p>
                            <p className="text-xs text-gray-500">
                              {getMaterialTypeLabel(verification.materialType, 'career')} • 
                              {formatFileSize(verification.file.size)}
                            </p>
                            {verification.description && (
                              <p className="text-xs text-gray-600 mt-1">{verification.description}</p>
                            )}
                          </div>
                          <Button size="sm" variant="outline">
                            <Download className="h-3 w-3 mr-1" />
                            下载
                          </Button>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleReview('career', verification.id, 'approved')}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-3 w-3 mr-1" />
                          通过
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleReview('career', verification.id, 'rejected')}
                        >
                          <XCircle className="h-3 w-3 mr-1" />
                          拒绝
                        </Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Review Modal */}
        {reviewingItem && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-medium mb-4">
                {reviewingItem.status === 'approved' ? '通过审核' : '拒绝审核'}
              </h3>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="review-notes">审核备注</Label>
                  <Textarea
                    id="review-notes"
                    value={reviewNotes}
                    onChange={(e) => setReviewNotes(e.target.value)}
                    placeholder={reviewingItem.status === 'approved' ? '可选：添加通过原因' : '请说明拒绝原因'}
                    className="mt-1"
                    rows={3}
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button
                    onClick={submitReview}
                    className={reviewingItem.status === 'approved' ? 'bg-green-600 hover:bg-green-700' : ''}
                    variant={reviewingItem.status === 'approved' ? 'default' : 'destructive'}
                  >
                    确认{reviewingItem.status === 'approved' ? '通过' : '拒绝'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setReviewingItem(null);
                      setReviewNotes('');
                    }}
                  >
                    取消
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminVerificationReview;
