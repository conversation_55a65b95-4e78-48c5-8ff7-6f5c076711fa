
export interface Tutor {
  id: string;
  user_id?: string;
  name: string;
  university: string;
  major: string;
  graduation_year: number;
  current_job: string;
  company: string;
  province: string;
  gaokao_score?: number;
  gaokao_province?: string;
  bio?: string;
  experience?: string;
  specialties?: string[];
  tags?: string[];
  price: number; // Deprecated, use hourlyRate instead
  hourlyRate?: number;
  halfHourRate?: number;
  isFree?: boolean;
  currency?: string;
  rating: number;
  review_count: number;
  consultation_count: number;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
  avatar_url?: string;
}

export interface TutorEducation {
  id: string;
  tutor_id: string;
  degree: string;
  school: string;
  major: string;
  start_year: number;
  end_year: number;
  created_at?: string;
}

export interface TutorCareer {
  id: string;
  tutor_id: string;
  company: string;
  position: string;
  start_date: string;
  end_date?: string;
  is_current: boolean;
  created_at?: string;
}

export interface Appointment {
  id: string;
  student_id: string;
  tutor_id: string;
  appointment_date: string;
  start_time: string;
  end_time: string;
  duration: number;
  price: number;
  currency?: string;
  requiresPayment?: boolean;
  paymentStatus?: 'pending' | 'paid' | 'failed' | 'refunded' | 'not_required';
  confirmationStatus?: 'pending' | 'confirmed' | 'rejected';
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  meeting_type: 'video' | 'voice' | 'offline';
  location?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
  tutor?: Tutor;
  payment?: Payment;
}

export interface Payment {
  id: string;
  appointmentId: string;
  tutorId: string;
  studentId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
  paymentMethod: string;
  transactionId?: string;
  wechatOrderId?: string;
  paidAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateAppointmentResult {
  appointment: Appointment;
  requiresPayment: boolean;
  paymentId?: string;
  price: number;
}

export interface WeChatPaymentResponse {
  prepayId: string;
  paySign: string;
  timeStamp: string;
  nonceStr: string;
  package: string;
  signType: string;
}

export interface Review {
  id: string;
  appointment_id: string;
  student_id: string;
  tutor_id: string;
  rating: number;
  content?: string;
  created_at?: string;
  tutors?: {
    name: string;
  };
}

export interface TutorAvailability {
  id: string;
  tutor_id: string;
  date: string;
  start_time: string;
  end_time: string;
  is_available: boolean;
  created_at?: string;
}

export interface Profile {
  id: string;
  phone?: string;
  avatar_url?: string;
  created_at?: string;
  updated_at?: string;
}
