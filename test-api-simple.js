// Simple API test script
const BASE_URL = 'http://localhost:3001';
const ADMIN_TOKEN = Buffer.from('<EMAIL>').toString('base64');

async function testAPI(endpoint, description) {
    try {
        const response = await fetch(`${BASE_URL}${endpoint}`, {
            headers: {
                'Authorization': `Bearer ${ADMIN_TOKEN}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log(`✅ ${description}: 成功`);
            console.log(`   响应数据:`, JSON.stringify(data, null, 2).substring(0, 200) + '...');
            return true;
        } else {
            console.log(`❌ ${description}: HTTP ${response.status}`);
            return false;
        }
    } catch (error) {
        console.log(`❌ ${description}: ${error.message}`);
        return false;
    }
}

async function runTests() {
    console.log('🧪 开始API测试...\n');
    
    const tests = [
        ['/api/admin/dashboard/stats', '仪表板统计'],
        ['/api/admin/dashboard/user-growth', '用户增长'],
        ['/api/admin/dashboard/appointment-trends', '预约趋势'],
        ['/api/admin/tutors/low-rating', '低分导师'],
        ['/api/admin/analytics/revenue', '收入分析'],
        ['/api/admin/system/health', '系统健康']
    ];
    
    let passed = 0;
    let total = tests.length;
    
    for (const [endpoint, description] of tests) {
        const success = await testAPI(endpoint, description);
        if (success) passed++;
        console.log(''); // 空行
    }
    
    console.log(`📊 测试结果: ${passed}/${total} 通过 (${((passed/total)*100).toFixed(1)}%)`);
    
    if (passed === total) {
        console.log('🎉 所有API测试通过！');
    } else {
        console.log('⚠️ 部分API测试失败，需要修复');
    }
}

// 检查是否有fetch
if (typeof fetch === 'undefined') {
    console.log('正在导入fetch...');
    import('node-fetch').then(({ default: fetch }) => {
        global.fetch = fetch;
        runTests();
    }).catch(() => {
        console.log('请安装node-fetch: npm install node-fetch');
    });
} else {
    runTests();
}
