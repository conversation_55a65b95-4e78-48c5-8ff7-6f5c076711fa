<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员面板测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/chart.js"></script>
    <style>
        .admin-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stat-card {
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="admin-card text-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <h1 class="text-2xl font-bold">🛡️ 管理员面板</h1>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm opacity-90">管理员: <EMAIL></span>
                        <button class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            退出登录
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Stats Overview -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="stat-card bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                                <span class="text-white text-sm font-bold">👥</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">总用户数</p>
                            <p class="text-2xl font-semibold text-gray-900" id="totalUsers">-</p>
                        </div>
                    </div>
                </div>

                <div class="stat-card bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                                <span class="text-white text-sm font-bold">🎓</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">导师总数</p>
                            <p class="text-2xl font-semibold text-gray-900" id="totalTutors">-</p>
                        </div>
                    </div>
                </div>

                <div class="stat-card bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                                <span class="text-white text-sm font-bold">📅</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">预约总数</p>
                            <p class="text-2xl font-semibold text-gray-900" id="totalAppointments">-</p>
                        </div>
                    </div>
                </div>

                <div class="stat-card bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                                <span class="text-white text-sm font-bold">💰</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">总收入</p>
                            <p class="text-2xl font-semibold text-gray-900" id="totalRevenue">-</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Management Sections -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- User Management -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">用户管理</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">活跃用户</span>
                                <span class="text-sm font-medium" id="activeUsers">-</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">本月新增</span>
                                <span class="text-sm font-medium" id="newUsersThisMonth">-</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">增长率</span>
                                <span class="text-sm font-medium" id="userGrowth">-</span>
                            </div>
                        </div>
                        <button class="mt-4 w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                            查看用户列表
                        </button>
                    </div>
                </div>

                <!-- Tutor Management -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">导师管理</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">已审核</span>
                                <span class="text-sm font-medium text-green-600" id="approvedTutors">-</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">待审核</span>
                                <span class="text-sm font-medium text-yellow-600" id="pendingTutors">-</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">平均评分</span>
                                <span class="text-sm font-medium" id="averageRating">-</span>
                            </div>
                        </div>
                        <button class="mt-4 w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                            审核导师
                        </button>
                    </div>
                </div>
            </div>

            <!-- API Test Section -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">API 测试</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <button onclick="testAPI('/api/admin/dashboard/stats')" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                            测试仪表板统计
                        </button>
                        <button onclick="testAPI('/api/admin/dashboard/user-growth')" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                            测试用户增长
                        </button>
                        <button onclick="testAPI('/api/admin/tutors/low-rating')" class="bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                            测试低分导师
                        </button>
                        <button onclick="testAPI('/api/admin/analytics/revenue')" class="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                            测试收入分析
                        </button>
                        <button onclick="testAPI('/api/admin/system/health')" class="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                            测试系统健康
                        </button>
                        <button onclick="window.open('http://localhost:3001/documentation', '_blank')" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                            查看API文档
                        </button>
                    </div>
                    
                    <div class="mt-6">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">API 响应:</h4>
                        <pre id="apiResponse" class="bg-gray-100 p-4 rounded-lg text-xs overflow-auto max-h-64">点击上方按钮测试API...</pre>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="mt-8 bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">系统状态</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                <span class="text-green-600 text-xl">✅</span>
                            </div>
                            <p class="text-sm font-medium text-gray-900">后端服务</p>
                            <p class="text-xs text-green-600">运行正常</p>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                <span class="text-green-600 text-xl">🗄️</span>
                            </div>
                            <p class="text-sm font-medium text-gray-900">数据库</p>
                            <p class="text-xs text-green-600">连接正常</p>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                <span class="text-green-600 text-xl">📊</span>
                            </div>
                            <p class="text-sm font-medium text-gray-900">API文档</p>
                            <p class="text-xs text-green-600">可访问</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 管理员认证token (简化版，实际应用中应该更安全)
        const adminToken = btoa('<EMAIL>');
        
        // 加载仪表板数据
        async function loadDashboardData() {
            try {
                const response = await fetch('http://localhost:3001/api/admin/dashboard/stats', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // 更新统计数据
                    document.getElementById('totalUsers').textContent = data.users.total.toLocaleString();
                    document.getElementById('totalTutors').textContent = data.tutors.total.toLocaleString();
                    document.getElementById('totalAppointments').textContent = data.appointments.total.toLocaleString();
                    document.getElementById('totalRevenue').textContent = `¥${data.payments.totalRevenue.toLocaleString()}`;
                    
                    // 更新详细数据
                    document.getElementById('activeUsers').textContent = data.users.active.toLocaleString();
                    document.getElementById('newUsersThisMonth').textContent = data.users.newThisMonth.toLocaleString();
                    document.getElementById('userGrowth').textContent = `${data.users.growth.toFixed(1)}%`;
                    
                    document.getElementById('approvedTutors').textContent = data.tutors.approved.toLocaleString();
                    document.getElementById('pendingTutors').textContent = data.tutors.pending.toLocaleString();
                    document.getElementById('averageRating').textContent = `${data.tutors.averageRating.toFixed(1)}⭐`;
                    
                } else {
                    console.error('Failed to load dashboard data:', response.status);
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }
        
        // 测试API端点
        async function testAPI(endpoint) {
            const responseElement = document.getElementById('apiResponse');
            responseElement.textContent = '正在请求...';
            
            try {
                const response = await fetch(`http://localhost:3001${endpoint}`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                responseElement.textContent = JSON.stringify(data, null, 2);
                
                if (response.ok) {
                    responseElement.className = 'bg-green-50 border border-green-200 p-4 rounded-lg text-xs overflow-auto max-h-64';
                } else {
                    responseElement.className = 'bg-red-50 border border-red-200 p-4 rounded-lg text-xs overflow-auto max-h-64';
                }
            } catch (error) {
                responseElement.textContent = `错误: ${error.message}`;
                responseElement.className = 'bg-red-50 border border-red-200 p-4 rounded-lg text-xs overflow-auto max-h-64';
            }
        }
        
        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
        });
    </script>
</body>
</html>
