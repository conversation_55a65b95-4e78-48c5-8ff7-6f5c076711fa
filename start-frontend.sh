#!/bin/bash

echo "🚀 启动前端服务..."

cd "$(dirname "$0")/rsdh-frontend"

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    pnpm install
fi

# 启动开发服务器
echo "🎨 启动Vite开发服务器..."
npx vite --port 3000 --host 0.0.0.0 &

FRONTEND_PID=$!
echo "前端进程ID: $FRONTEND_PID"

# 等待服务器启动
sleep 5

# 检查服务器是否启动成功
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ 前端服务启动成功: http://localhost:3000"
else
    echo "❌ 前端服务启动失败"
fi

echo "前端PID: $FRONTEND_PID" > ../frontend.pid
